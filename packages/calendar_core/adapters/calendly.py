from __future__ import annotations

import os
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

import httpx
from aiocache import cached, Cache
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from ..interface import CalendarProvider
from ailex_auth import get_access_token

logger = logging.getLogger(__name__)


class CalendlyProvider(CalendarProvider):
    """Complete Calendly adapter with event types, scheduling links, and availability."""

    def __init__(self, firm_id: str, provider_name: str, capability: Dict[str, Any] = None):
        self._firm_id = firm_id
        self.provider_name = provider_name
        self.capability = capability or {}
        self.api_base = os.getenv("CALENDLY_API_BASE", "https://api.calendly.com")

        # Initialize cache
        cache_url = os.getenv("CACHE_URL", "redis://localhost:6379/1")
        try:
            self.cache = Cache.from_url(cache_url)
        except Exception:
            # Fallback to memory cache if Redis is not available
            self.cache = Cache(Cache.MEMORY)

        # Don't call parent __init__ to avoid immediate token fetch
        # Token will be fetched on-demand via _get_token()

    async def _get_token(self) -> str:
        """Get access token from auth service."""
        return await get_access_token(self._firm_id, "calendly")

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((httpx.HTTPStatusError, httpx.RequestError))
    )
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Make authenticated request to Calendly API with retry logic."""
        token = await self._get_token()

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        url = f"{self.api_base}{endpoint}"

        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                if method.upper() == "GET":
                    response = await client.get(url, headers=headers, params=params)
                elif method.upper() == "POST":
                    response = await client.post(url, headers=headers, json=data, params=params)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

                # Handle rate limiting
                if response.status_code == 429:
                    logger.warning("Rate limit exceeded for Calendly API")
                    raise httpx.HTTPStatusError(
                        "Rate limit exceeded",
                        request=response.request,
                        response=response
                    )

                response.raise_for_status()
                return response.json()

            except httpx.HTTPStatusError as e:
                logger.error(f"Calendly API error: {e.response.status_code} - {e.response.text}")
                raise
            except Exception as e:
                logger.error(f"Unexpected error calling Calendly API: {str(e)}")
                raise

    async def list_calendars(self) -> List[Dict[str, Any]]:
        """List calendars (returns event types for Calendly)."""
        event_types = await self.get_event_types()
        return [
            {
                "id": event_type["uri"],
                "name": event_type["name"],
                "description": event_type.get("description", ""),
                "primary": True,  # All Calendly event types are considered primary
                "selected": event_type.get("active", True)
            }
            for event_type in event_types
        ]

    async def create_event(self, calendar_id: str, event: Dict[str, Any]) -> Dict[str, Any]:
        """Create a Calendly scheduling link (Calendly doesn't support direct event creation)."""
        # For Calendly, we create a scheduling link instead of a direct event
        # The calendar_id should be an event type URI
        link_data = await self.create_scheduling_link(
            event_type_id=calendar_id,
            start_time=event.get("start"),
            end_time=event.get("end")
        )

        return {
            "id": link_data["booking_url"],
            "htmlLink": link_data["booking_url"],
            "summary": event.get("summary", "Calendly Meeting"),
            "start": event.get("start"),
            "end": event.get("end"),
            "calendly_link": link_data["booking_url"],
            "provider": "calendly"
        }

    @cached(ttl=1800, cache=Cache.MEMORY)  # 30-minute TTL cache
    async def get_event_types(self, user_uri: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get event types for the authenticated user with caching."""
        params = {}
        if user_uri:
            params["user"] = user_uri

        try:
            response = await self._make_request("GET", "/event_types", params=params)
            event_types = response.get("collection", [])

            # Transform to our standard format
            return [
                {
                    "uri": et["uri"],
                    "name": et["name"],
                    "description": et.get("description_plain", ""),
                    "duration": et["duration"],
                    "scheduling_url": et["scheduling_url"],
                    "active": et["active"],
                    "kind": et["kind"],
                    "pooling_type": et.get("pooling_type"),
                    "type": et["type"],
                    "color": et.get("color"),
                    "created_at": et["created_at"],
                    "updated_at": et["updated_at"],
                    "internal_note": et.get("internal_note")
                }
                for et in event_types
            ]
        except Exception as e:
            logger.error(f"Failed to get Calendly event types: {str(e)}")
            raise

    async def create_scheduling_link(
        self,
        event_type_id: str,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        max_events: int = 1,
        **kwargs
    ) -> Dict[str, Any]:
        """Create a scheduling link for a specific event type."""
        # Ensure event_type_id is a full URI
        if not event_type_id.startswith("https://"):
            event_type_id = f"https://api.calendly.com/event_types/{event_type_id}"

        data = {
            "max_events": max_events,
            "owner": event_type_id,
            "owner_type": "EventType"
        }

        try:
            response = await self._make_request("POST", "/scheduling_links", data=data)
            resource = response.get("resource", {})

            return {
                "booking_url": resource["booking_url"],
                "owner": resource["owner"],
                "owner_type": resource["owner_type"],
                "created_at": resource["created_at"]
            }
        except Exception as e:
            logger.error(f"Failed to create Calendly scheduling link: {str(e)}")
            raise

    async def get_availability(
        self,
        calendar_ids: List[str],
        time_min: datetime,
        time_max: datetime
    ) -> List[Dict[str, Any]]:
        """Get availability for Calendly event types."""
        results = []

        for calendar_id in calendar_ids:
            try:
                # For Calendly, we need to get the user's availability
                # This is a simplified implementation - in practice, you'd need to:
                # 1. Get the user URI from the event type
                # 2. Query the user's availability
                # 3. Cross-reference with event type availability

                # Get user info first
                user_response = await self._make_request("GET", "/users/me")
                user_uri = user_response["resource"]["uri"]

                # Query availability for the user
                params = {
                    "user": user_uri,
                    "start_time": time_min.isoformat(),
                    "end_time": time_max.isoformat()
                }

                # Note: Calendly's availability API might be different
                # This is a placeholder implementation
                await self._make_request("GET", "/user_availability_schedules", params=params)

                # Transform to our standard format
                busy_times = []
                # Process availability data and convert to busy times
                # This would need to be implemented based on Calendly's actual API response

                results.append({
                    "calendar_id": calendar_id,
                    "busy": busy_times
                })

            except Exception as e:
                logger.warning(f"Failed to get availability for {calendar_id}: {str(e)}")
                # Return empty availability on error
                results.append({
                    "calendar_id": calendar_id,
                    "busy": []
                })

        return results
