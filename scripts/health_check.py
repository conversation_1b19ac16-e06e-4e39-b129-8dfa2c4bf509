#!/usr/bin/env python3
"""
Simple health check script for voice-svc deployment verification.

This script can be used to verify that the voice-svc is running correctly
after deployment to Fly.io or other platforms.

Usage:
    python scripts/health_check.py [URL]
    
Examples:
    python scripts/health_check.py https://avr-voice-svc.fly.dev
    python scripts/health_check.py http://localhost:8000
"""

import sys
import asyncio
import argparse
from typing import Dict, Any
import httpx


async def check_endpoint(client: httpx.AsyncClient, base_url: str, endpoint: str) -> Dict[str, Any]:
    """Check a specific endpoint."""
    try:
        url = f"{base_url.rstrip('/')}{endpoint}"
        response = await client.get(url, timeout=10.0)
        
        return {
            "endpoint": endpoint,
            "url": url,
            "status_code": response.status_code,
            "success": response.status_code == 200,
            "response_time_ms": int(response.elapsed.total_seconds() * 1000),
            "content_length": len(response.content) if response.content else 0
        }
        
    except httpx.TimeoutException:
        return {
            "endpoint": endpoint,
            "url": f"{base_url.rstrip('/')}{endpoint}",
            "success": False,
            "error": "timeout"
        }
    except Exception as e:
        return {
            "endpoint": endpoint,
            "url": f"{base_url.rstrip('/')}{endpoint}",
            "success": False,
            "error": str(e)
        }


async def run_health_checks(base_url: str) -> Dict[str, Any]:
    """Run health checks against the voice service."""
    print(f"🔍 Running health checks against: {base_url}")
    
    endpoints = [
        "/livez",      # Liveness check (critical for Fly.io)
        "/health",     # Detailed health check
        "/metrics",    # Prometheus metrics
        "/docs",       # API documentation
    ]
    
    results = {}
    
    async with httpx.AsyncClient() as client:
        for endpoint in endpoints:
            print(f"  Checking {endpoint}...")
            result = await check_endpoint(client, base_url, endpoint)
            results[endpoint] = result
            
            if result["success"]:
                response_time = result.get("response_time_ms", "?")
                print(f"    ✅ {endpoint} - {result['status_code']} ({response_time}ms)")
            else:
                error = result.get("error", "unknown error")
                print(f"    ❌ {endpoint} - {error}")
    
    return results


def print_summary(results: Dict[str, Any]) -> bool:
    """Print summary of health check results."""
    print("\n📊 Health Check Summary")
    print("=" * 50)
    
    total_checks = len(results)
    successful_checks = sum(1 for r in results.values() if r["success"])
    failed_checks = total_checks - successful_checks
    
    print(f"Total checks: {total_checks}")
    print(f"Successful: {successful_checks}")
    print(f"Failed: {failed_checks}")
    
    if failed_checks == 0:
        print("\n🎉 All health checks passed!")
        return True
    else:
        print(f"\n⚠️  {failed_checks} health check(s) failed:")
        for endpoint, result in results.items():
            if not result["success"]:
                error = result.get("error", "unknown error")
                print(f"  - {endpoint}: {error}")
        return False


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Health check for voice-svc")
    parser.add_argument(
        "url", 
        nargs="?", 
        default="http://localhost:8000",
        help="Base URL of the voice service (default: http://localhost:8000)"
    )
    parser.add_argument(
        "--critical-only",
        action="store_true",
        help="Only check critical endpoints (/livez)"
    )
    
    args = parser.parse_args()
    
    try:
        results = await run_health_checks(args.url)
        success = print_summary(results)
        
        # Exit with appropriate code
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⏹️  Health check interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Health check failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
