#!/usr/bin/env python3
"""Simple API test for booking endpoints."""
import sys
import os
sys.path.insert(0, '/mnt/persist/workspace')

import json
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch, MagicMock
from uuid import uuid4

from fastapi.testclient import TestClient
from fastapi import Fast<PERSON><PERSON>

# Create a minimal FastAPI app for testing
app = FastAPI()

# Import and add the booking router
from app.api.v1.endpoints.booking import router
app.include_router(router, prefix="/api/v1")

client = TestClient(app)


def test_propose_endpoint():
    """Test the propose endpoint with mocked dependencies."""
    
    # Mock the dependencies
    with patch('app.api.v1.endpoints.booking.get_firm_id') as mock_get_firm_id:
        mock_get_firm_id.return_value = "test-firm-123"
        
        with patch('app.api.v1.endpoints.booking.proposeSlot') as mock_propose:
            # Mock proposed slots
            from app.booking_helpers import ProposedSlot
            start_time = datetime.utcnow() + timedelta(hours=1)
            mock_slots = [
                ProposedSlot(
                    start_at=start_time,
                    end_at=start_time + timedelta(minutes=30),
                    provider="google",
                    calendar_id="primary",
                    duration_minutes=30
                ),
                ProposedSlot(
                    start_at=start_time + timedelta(hours=1),
                    end_at=start_time + timedelta(hours=1, minutes=30),
                    provider="calendly",
                    calendar_id="event_type_123",
                    duration_minutes=30
                )
            ]
            mock_propose.return_value = mock_slots

            # Test the endpoint
            request_data = {
                "duration_minutes": 30,
                "max_slots": 2
            }
            
            response = client.post("/api/v1/propose", json=request_data)
            
            assert response.status_code == 200
            data = response.json()
            assert len(data) == 2
            assert data[0]["provider"] == "google"
            assert data[1]["provider"] == "calendly"
            
            print("✓ Propose endpoint test passed")


def test_confirm_endpoint():
    """Test the confirm endpoint with mocked dependencies."""
    
    with patch('app.api.v1.endpoints.booking.get_firm_id') as mock_get_firm_id:
        mock_get_firm_id.return_value = "test-firm-123"
        
        with patch('app.api.v1.endpoints.booking.get_async_db') as mock_get_db:
            mock_db = AsyncMock()
            mock_get_db.return_value = mock_db
            
            with patch('app.api.v1.endpoints.booking.confirmSlot') as mock_confirm:
                booking_id = str(uuid4())
                mock_confirm.return_value = {
                    "booking_id": booking_id,
                    "external_id": "google-event-123",
                    "provider": "google",
                    "provider_event_link": "https://calendar.google.com/event/123",
                    "start_at": "2024-01-15T14:00:00Z",
                    "end_at": "2024-01-15T14:30:00Z",
                    "status": "confirmed",
                    "booked_at": datetime.utcnow().isoformat()
                }

                # Test the endpoint
                start_time = datetime.utcnow() + timedelta(hours=1)
                end_time = start_time + timedelta(minutes=30)
                
                request_data = {
                    "start_at": start_time.isoformat(),
                    "end_at": end_time.isoformat(),
                    "provider": "google",
                    "calendar_id": "primary",
                    "summary": "Test Meeting",
                    "attendee_email": "<EMAIL>"
                }
                
                response = client.post("/api/v1/confirm", json=request_data)
                
                assert response.status_code == 200
                data = response.json()
                assert data["id"] == booking_id
                assert data["provider"] == "google"
                assert data["external_id"] == "google-event-123"
                
                print("✓ Confirm endpoint test passed")


def test_legacy_book_endpoint():
    """Test the legacy book endpoint with mocked dependencies."""
    
    with patch('app.api.v1.endpoints.booking.get_firm_id') as mock_get_firm_id:
        mock_get_firm_id.return_value = "test-firm-123"
        
        with patch('app.api.v1.endpoints.booking.get_async_db') as mock_get_db:
            mock_db = AsyncMock()
            mock_get_db.return_value = mock_db
            
            with patch('app.api.v1.endpoints.booking.confirmSlot') as mock_confirm:
                booking_id = str(uuid4())
                mock_confirm.return_value = {
                    "booking_id": booking_id,
                    "external_id": "google-event-123",
                    "provider": "google",
                    "provider_event_link": "https://calendar.google.com/event/123",
                    "start_at": "2024-01-15T14:00:00Z",
                    "end_at": "2024-01-15T14:30:00Z",
                    "status": "confirmed",
                    "booked_at": datetime.utcnow().isoformat()
                }

                # Test the endpoint
                start_time = datetime.utcnow() + timedelta(hours=1)
                end_time = start_time + timedelta(minutes=30)
                
                request_data = {
                    "provider": "google",
                    "calendar_id": "primary",
                    "summary": "Test Meeting",
                    "start_at": start_time.isoformat(),
                    "end_at": end_time.isoformat()
                }
                
                response = client.post("/api/v1/book", json=request_data)
                
                assert response.status_code == 200
                data = response.json()
                assert data["id"] == booking_id
                assert data["provider"] == "google"
                assert data["external_id"] == "google-event-123"
                
                print("✓ Legacy book endpoint test passed")


def test_error_handling():
    """Test error handling in endpoints."""
    
    with patch('app.api.v1.endpoints.booking.get_firm_id') as mock_get_firm_id:
        mock_get_firm_id.return_value = "test-firm-123"
        
        with patch('app.api.v1.endpoints.booking.get_async_db') as mock_get_db:
            mock_db = AsyncMock()
            mock_get_db.return_value = mock_db
            
            with patch('app.api.v1.endpoints.booking.confirmSlot') as mock_confirm:
                from app.booking_helpers import SlotUnavailableError
                mock_confirm.side_effect = SlotUnavailableError("Slot is no longer available")

                # Test slot unavailable error
                start_time = datetime.utcnow() + timedelta(hours=1)
                end_time = start_time + timedelta(minutes=30)
                
                request_data = {
                    "start_at": start_time.isoformat(),
                    "end_at": end_time.isoformat(),
                    "provider": "google",
                    "calendar_id": "primary",
                    "summary": "Test Meeting"
                }
                
                response = client.post("/api/v1/confirm", json=request_data)
                
                assert response.status_code == 409
                assert "Slot is no longer available" in response.json()["detail"]
                
                print("✓ Error handling test passed")


if __name__ == "__main__":
    print("Testing booking API endpoints...")
    
    try:
        test_propose_endpoint()
        test_confirm_endpoint()
        test_legacy_book_endpoint()
        test_error_handling()
        
        print("\nAll API tests passed! ✓")
        
    except Exception as e:
        print(f"\nTest failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
