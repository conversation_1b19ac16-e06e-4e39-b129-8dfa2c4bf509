"""
Unified availability API endpoints.

This module provides endpoints for aggregating availability from multiple calendar providers.
"""
import logging
from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request, status

from ....middleware.subscription_check import require_feature
from ....schemas.calendar import (
    UnifiedAvailabilityRequest,
    AvailabilitySlotDTO,
    AvailabilitySlot
)
from ....utils.availability import get_aggregated_availability
from ....api.deps import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter()


def convert_slot_to_dto(slot: AvailabilitySlot) -> AvailabilitySlotDTO:
    """Convert AvailabilitySlot to AvailabilitySlotDTO."""
    duration_minutes = int((slot.end - slot.start).total_seconds() / 60)
    return AvailabilitySlotDTO(
        start=slot.start,
        end=slot.end,
        provider=slot.provider,
        duration_minutes=duration_minutes
    )


@router.post("/unified", response_model=List[AvailabilitySlotDTO])
@require_feature("calendar_booking")
async def get_unified_availability(
    request: UnifiedAvailabilityRequest,
    current_user: dict = Depends(get_current_user),
    req: Request = None
) -> List[AvailabilitySlotDTO]:
    """
    Get unified availability from all connected calendar providers.
    
    This endpoint aggregates availability from Google Calendar, Calendly, and other
    connected providers, merges overlapping slots, and returns a unified list
    normalized to 15-minute granularity.
    
    Args:
        request: The availability request with time range
        current_user: Current authenticated user
        req: FastAPI request object (for middleware)
        
    Returns:
        List of unified availability slots
        
    Raises:
        HTTPException: If user is not authenticated or feature not available
    """
    try:
        # Extract tenant ID from current user
        tenant_id = current_user.get("user_id")
        if not tenant_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User ID not found in authentication token"
            )
        
        # Convert to UUID
        try:
            tenant_uuid = UUID(tenant_id)
        except ValueError:
            # If tenant_id is not a UUID, use it as string but log warning
            logger.warning(f"Tenant ID is not a valid UUID: {tenant_id}")
            tenant_uuid = tenant_id
        
        logger.info(f"Fetching unified availability for tenant {tenant_uuid} from {request.from_dt} to {request.to_dt}")
        
        # Get aggregated availability
        availability_slots = await get_aggregated_availability(
            tenant_uuid,
            request.from_dt,
            request.to_dt
        )
        
        # Convert to DTOs
        slot_dtos = [convert_slot_to_dto(slot) for slot in availability_slots]
        
        logger.info(f"Returning {len(slot_dtos)} unified availability slots")
        return slot_dtos
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching unified availability: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch unified availability"
        )


@router.get("/health")
async def availability_health_check():
    """Health check endpoint for availability service."""
    return {"status": "healthy", "service": "unified-availability"}
