from __future__ import annotations

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

# Import packages with proper path handling
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', '..'))

try:
    from packages.calendar_core import get_provider
    from packages.shared import models as shared_models
except ImportError:
    # Fallback for testing
    def get_provider(firm_id: str, provider_name: str):
        class MockProvider:
            async def get_availability(self, calendar_ids, time_min, time_max):
                return [{"calendar_id": cid, "busy": []} for cid in calendar_ids]
        return MockProvider()

    class MockModels:
        class BookingProvider:
            GOOGLE = "google"
            CALENDLY = "calendly"

    shared_models = MockModels()

from ....db.base import get_async_db
from ....api.deps import get_current_user
from ....utils.rate_limiter import RateLimiter, RateLimitExceeded
from ....utils.statsd import get_statsd_client
from ....schemas.calendar import (
    AvailabilityRequest,
    FreeBusyResponse,
    TimeRange,
)
from ...booking_helpers import (
    proposeSlot,
    confirmSlot,
    ProposedSlot,
    SlotUnavailableError,
    ProviderError
)
from pydantic import BaseModel

router = APIRouter()

# Rate limiter for booking endpoint
booking_limiter = RateLimiter(limit=5, window=60)


async def get_firm_id(current_user=Depends(get_current_user)) -> str:
    # For tests, the user id acts as the firm id
    return current_user.id


def _merge_ranges(ranges: List[dict]) -> List[dict]:
    """Merge overlapping busy ranges."""
    if not ranges:
        return []
    sorted_ranges = sorted(ranges, key=lambda r: r["start"])
    merged = [sorted_ranges[0].copy()]
    for r in sorted_ranges[1:]:
        last = merged[-1]
        if r["start"] <= last["end"]:
            if r["end"] > last["end"]:
                last["end"] = r["end"]
        else:
            merged.append(r.copy())
    return merged


class BookingRequest(BaseModel):
    provider: shared_models.BookingProvider
    calendar_id: str
    summary: str
    start_at: datetime
    end_at: datetime


class ProposeSlotRequest(BaseModel):
    preferred_start: Optional[datetime] = None
    duration_minutes: int = 30
    max_slots: int = 3


class ProposedSlotResponse(BaseModel):
    start_at: datetime
    end_at: datetime
    provider: str
    calendar_id: str
    duration_minutes: int


class ConfirmSlotRequest(BaseModel):
    start_at: datetime
    end_at: datetime
    provider: str
    calendar_id: str
    summary: str
    attendee_email: Optional[str] = None
    description: Optional[str] = None


class BookingResponse(BaseModel):
    id: UUID
    provider: shared_models.BookingProvider
    external_id: Optional[str] = None
    start_at: datetime
    booked_at: datetime
    provider_event_link: Optional[str] = None


@router.post("/availability", response_model=List[FreeBusyResponse])
async def availability(
    req: AvailabilityRequest,
    provider: str,
    firm_id: str = Depends(get_firm_id),
):
    provider_client = get_provider(firm_id, provider)
    raw = await provider_client.get_availability(
        req.calendar_ids, req.time_min, req.time_max
    )
    responses: List[FreeBusyResponse] = []
    for item in raw:
        merged = _merge_ranges(item.get("busy", []))
        responses.append(
            FreeBusyResponse(
                calendar_id=item["calendar_id"],
                busy=[TimeRange(**b) if isinstance(b, dict) else b for b in merged],
            )
        )
    return responses


@router.post("/propose", response_model=List[ProposedSlotResponse])
async def propose_slots(
    req: ProposeSlotRequest,
    firm_id: str = Depends(get_firm_id),
):
    """Propose available booking slots across providers."""
    try:
        proposed_slots = await proposeSlot(
            tenant_id=firm_id,
            preferred_start=req.preferred_start,
            duration_minutes=req.duration_minutes,
            max_slots=req.max_slots
        )

        return [
            ProposedSlotResponse(
                start_at=slot.start_at,
                end_at=slot.end_at,
                provider=slot.provider,
                calendar_id=slot.calendar_id,
                duration_minutes=slot.duration_minutes
            )
            for slot in proposed_slots
        ]
    except Exception as e:
        get_statsd_client().incr("booking.propose_failed")
        raise HTTPException(status_code=500, detail=f"Failed to propose slots: {str(e)}")


@router.post("/confirm", response_model=BookingResponse)
async def confirm_slot(
    req: ConfirmSlotRequest,
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    firm_id: str = Depends(get_firm_id),
):
    """Confirm a proposed slot and create the booking."""
    try:
        await booking_limiter.is_rate_limited(request)
    except RateLimitExceeded as e:
        get_statsd_client().incr("booking.rate_limited")
        raise HTTPException(status_code=429, detail="Rate limit exceeded") from e

    # Create ProposedSlot object
    proposed_slot = ProposedSlot(
        start_at=req.start_at,
        end_at=req.end_at,
        provider=req.provider,
        calendar_id=req.calendar_id,
        duration_minutes=int((req.end_at - req.start_at).total_seconds() / 60)
    )

    try:
        result = await confirmSlot(
            tenant_id=firm_id,
            proposed_slot=proposed_slot,
            summary=req.summary,
            db=db,
            attendee_email=req.attendee_email,
            description=req.description
        )

        get_statsd_client().incr("booking.confirmed")

        return BookingResponse(
            id=UUID(result["booking_id"]),
            provider=shared_models.BookingProvider(req.provider.lower()),
            external_id=result["external_id"],
            start_at=req.start_at,
            booked_at=datetime.fromisoformat(result["booked_at"]),
            provider_event_link=result["provider_event_link"],
        )

    except SlotUnavailableError as e:
        get_statsd_client().incr("booking.slot_unavailable")
        raise HTTPException(status_code=409, detail=str(e))
    except ProviderError as e:
        get_statsd_client().incr("booking.provider_error")
        raise HTTPException(status_code=502, detail=f"Provider error: {str(e)}")
    except Exception as e:
        get_statsd_client().incr("booking.confirm_failed")
        raise HTTPException(status_code=500, detail=f"Failed to confirm booking: {str(e)}")


@router.post("/book", response_model=BookingResponse)
async def book(
    req: BookingRequest,
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    firm_id: str = Depends(get_firm_id),
):
    """Legacy booking endpoint - now uses confirmSlot internally."""
    try:
        await booking_limiter.is_rate_limited(request)
    except RateLimitExceeded as e:
        get_statsd_client().incr("booking.rate_limited")
        raise HTTPException(status_code=429, detail="Rate limit exceeded") from e

    # Create ProposedSlot object from legacy request
    proposed_slot = ProposedSlot(
        start_at=req.start_at,
        end_at=req.end_at,
        provider=req.provider.value,
        calendar_id=req.calendar_id,
        duration_minutes=int((req.end_at - req.start_at).total_seconds() / 60)
    )

    try:
        result = await confirmSlot(
            tenant_id=firm_id,
            proposed_slot=proposed_slot,
            summary=req.summary,
            db=db
        )

        get_statsd_client().incr("booking.legacy_confirmed")

        return BookingResponse(
            id=UUID(result["booking_id"]),
            provider=req.provider,
            external_id=result["external_id"],
            start_at=req.start_at,
            booked_at=datetime.fromisoformat(result["booked_at"]),
            provider_event_link=result["provider_event_link"],
        )

    except SlotUnavailableError as e:
        get_statsd_client().incr("booking.slot_unavailable")
        raise HTTPException(status_code=409, detail=str(e))
    except ProviderError as e:
        get_statsd_client().incr("booking.provider_error")
        raise HTTPException(status_code=502, detail=f"Provider error: {str(e)}")
    except Exception as e:
        get_statsd_client().incr("booking.legacy_failed")
        raise HTTPException(status_code=500, detail=f"Failed to create booking: {str(e)}")


@router.get("/events", response_model=List[BookingResponse])
async def events(
    provider: Optional[shared_models.BookingProvider] = None,
    db: AsyncSession = Depends(get_async_db),
    firm_id: str = Depends(get_firm_id),
):
    stmt = select(shared_models.Booking).where(
        shared_models.Booking.firm_id == firm_id
    )
    if provider:
        stmt = stmt.where(shared_models.Booking.provider == provider)

    result = await db.execute(stmt)
    bookings = result.scalars().all()
    return [
        BookingResponse(
            id=b.id,
            provider=b.provider,
            external_id=b.external_id,
            start_at=b.start_at,
            booked_at=b.booked_at,
            provider_event_link=b.provider_event_link,
        )
        for b in bookings
    ]

