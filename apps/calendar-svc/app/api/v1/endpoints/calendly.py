"""
Calendly-specific API endpoints for event types and scheduling links.
"""
from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, status

# Import packages with proper path handling
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', '..'))

try:
    from packages.calendar_core import get_provider
except ImportError:
    # Fallback for testing
    def get_provider(firm_id: str, provider_name: str):
        class MockProvider:
            async def get_availability(self, calendar_ids, time_min, time_max):
                return [{"calendar_id": cid, "busy": []} for cid in calendar_ids]
        return Mock<PERSON><PERSON>ider()
from ....repositories.calendar_repository import CalendarRepository
from ....schemas.calendar import (
    EventTypeDTO,
    SchedulingLinkDTO,
    SchedulingLinkRequest,
)
from ...deps import get_current_user, get_repository

router = APIRouter()


async def get_firm_id(current_user=Depends(get_current_user)) -> str:
    """Get firm ID from current user."""
    return current_user.id


async def get_calendly_connection(
    firm_id: str = Depends(get_firm_id),
    repo: CalendarRepository = Depends(get_repository(CalendarRepository))
):
    """Get active Calendly connection for the current user."""
    connection = await repo.get_connection_by_user_and_provider(firm_id, "calendly")
    if not connection or not connection.is_active:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No active Calendly connection found. Please connect your Calendly account first."
        )
    return connection


@router.get("/event-types", response_model=List[EventTypeDTO])
async def get_event_types(
    firm_id: str = Depends(get_firm_id),
    connection=Depends(get_calendly_connection),
) -> List[EventTypeDTO]:
    """
    Get all event types for the authenticated Calendly user.

    Requires the 'calendar_booking' feature to be enabled for the user's subscription.
    """
    try:
        # Get the Calendly provider
        provider = get_provider(firm_id, "calendly")

        # Fetch event types
        event_types = await provider.get_event_types()

        # Transform to DTOs
        return [
            EventTypeDTO(
                uri=et["uri"],
                name=et["name"],
                description=et["description"],
                duration=et["duration"],
                scheduling_url=et["scheduling_url"],
                active=et["active"],
                kind=et["kind"],
                pooling_type=et["pooling_type"],
                type=et["type"],
                color=et["color"],
                created_at=datetime.fromisoformat(et["created_at"].replace("Z", "+00:00")),
                updated_at=datetime.fromisoformat(et["updated_at"].replace("Z", "+00:00")),
                internal_note=et["internal_note"]
            )
            for et in event_types
        ]

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve event types: {str(e)}"
        )


@router.post("/scheduling-links", response_model=SchedulingLinkDTO, status_code=status.HTTP_201_CREATED)
async def create_scheduling_link(
    request: SchedulingLinkRequest,
    firm_id: str = Depends(get_firm_id),
    connection=Depends(get_calendly_connection),
) -> SchedulingLinkDTO:
    """
    Create a scheduling link for a specific Calendly event type.

    Requires the 'calendar_booking' feature to be enabled for the user's subscription.
    """
    try:
        # Get the Calendly provider
        provider = get_provider(firm_id, "calendly")

        # Create scheduling link
        link_data = await provider.create_scheduling_link(
            event_type_id=request.event_type_id,
            start_time=request.start_time.isoformat() if request.start_time else None,
            end_time=request.end_time.isoformat() if request.end_time else None,
            max_events=request.max_events
        )

        return SchedulingLinkDTO(
            booking_url=link_data["booking_url"],
            owner=link_data["owner"],
            owner_type=link_data["owner_type"],
            created_at=datetime.fromisoformat(link_data["created_at"].replace("Z", "+00:00"))
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create scheduling link: {str(e)}"
        )


@router.get("/availability")
async def get_calendly_availability(
    start_time: datetime,
    end_time: datetime,
    event_type_ids: Optional[List[str]] = None,
    firm_id: str = Depends(get_firm_id),
    connection=Depends(get_calendly_connection),
):
    """
    Get availability for Calendly event types.

    Requires the 'calendar_booking' feature to be enabled for the user's subscription.
    """
    try:
        # Get the Calendly provider
        provider = get_provider(firm_id, "calendly")

        # If no specific event types provided, get all
        if not event_type_ids:
            event_types = await provider.get_event_types()
            event_type_ids = [et["uri"] for et in event_types]

        # Get availability
        availability = await provider.get_availability(
            calendar_ids=event_type_ids,
            time_min=start_time,
            time_max=end_time
        )

        return availability

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve availability: {str(e)}"
        )
