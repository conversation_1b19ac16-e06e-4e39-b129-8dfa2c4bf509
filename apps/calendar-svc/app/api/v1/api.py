from fastapi import APIRouter

# Import endpoints
from .endpoints import calendar, booking, calendly, availability

# Create API router
api_router = APIRouter()

# Include routers
api_router.include_router(calendar.router, prefix="/calendars", tags=["calendars"])
api_router.include_router(booking.router, tags=["booking"])
api_router.include_router(calendly.router, prefix="/calendly", tags=["calendly"])
api_router.include_router(availability.router, prefix="/availability", tags=["availability"])
