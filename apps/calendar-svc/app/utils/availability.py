"""
Unified availability aggregator utility.

This module provides functionality to aggregate availability from multiple calendar providers
(Google Calendar, Calendly, etc.) and merge them into a unified response.
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from ..db.models.calendar_connection import CalendarConnection
from ..schemas.calendar import AvailabilitySlot
from ..config import settings

# Import calendar_core with proper path handling
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

try:
    from packages.calendar_core import get_provider
except ImportError:
    # Fallback for testing - create a mock provider
    def get_provider(firm_id: str, provider_name: str):
        class MockProvider:
            async def get_availability(self, calendar_ids, time_min, time_max):
                return [{"calendar_id": cid, "busy": []} for cid in calendar_ids]
        return MockProvider()

logger = logging.getLogger(__name__)

# 15-minute granularity for slot normalization
SLOT_GRANULARITY_MINUTES = 15


def normalize_to_granularity(dt: datetime, granularity_minutes: int = SLOT_GRANULARITY_MINUTES) -> datetime:
    """
    Normalize datetime to the specified granularity (default 15 minutes).
    
    Args:
        dt: The datetime to normalize
        granularity_minutes: The granularity in minutes (default 15)
        
    Returns:
        Normalized datetime rounded down to the nearest granularity boundary
    """
    minutes = (dt.minute // granularity_minutes) * granularity_minutes
    return dt.replace(minute=minutes, second=0, microsecond=0)


def merge_overlapping_slots(slots: List[AvailabilitySlot]) -> List[AvailabilitySlot]:
    """
    Merge overlapping or adjacent availability slots.
    
    Args:
        slots: List of availability slots to merge
        
    Returns:
        List of merged availability slots
    """
    if not slots:
        return []
    
    # Sort slots by start time
    sorted_slots = sorted(slots, key=lambda x: x.start)
    merged = [sorted_slots[0]]
    
    for current in sorted_slots[1:]:
        last_merged = merged[-1]
        
        # Check if current slot overlaps or is adjacent to the last merged slot
        if current.start <= last_merged.end:
            # Merge slots - extend the end time and combine providers
            merged_end = max(last_merged.end, current.end)
            providers = set([last_merged.provider, current.provider])
            
            # Create new merged slot with combined provider info
            merged[-1] = AvailabilitySlot(
                start=last_merged.start,
                end=merged_end,
                provider=",".join(sorted(providers))
            )
        else:
            # No overlap, add as new slot
            merged.append(current)
    
    return merged


def convert_busy_to_available_slots(
    busy_times: List[Dict[str, Any]], 
    time_min: datetime, 
    time_max: datetime,
    provider: str
) -> List[AvailabilitySlot]:
    """
    Convert busy times to available slots.
    
    Args:
        busy_times: List of busy time ranges from provider
        time_min: Start of the query range
        time_max: End of the query range
        provider: Provider name
        
    Returns:
        List of available slots
    """
    available_slots = []
    
    # Normalize query range to granularity
    normalized_start = normalize_to_granularity(time_min)
    normalized_end = normalize_to_granularity(time_max)
    
    # Sort busy times by start time
    busy_sorted = sorted(busy_times, key=lambda x: datetime.fromisoformat(x['start'].replace('Z', '+00:00')))
    
    current_time = normalized_start
    
    for busy in busy_sorted:
        busy_start = datetime.fromisoformat(busy['start'].replace('Z', '+00:00'))
        busy_end = datetime.fromisoformat(busy['end'].replace('Z', '+00:00'))
        
        # Normalize busy times
        busy_start = normalize_to_granularity(busy_start)
        busy_end = normalize_to_granularity(busy_end + timedelta(minutes=SLOT_GRANULARITY_MINUTES))
        
        # Add available slot before this busy time
        if current_time < busy_start:
            available_slots.append(AvailabilitySlot(
                start=current_time,
                end=busy_start,
                provider=provider
            ))
        
        # Move current time to end of busy period
        current_time = max(current_time, busy_end)
    
    # Add final available slot if there's time remaining
    if current_time < normalized_end:
        available_slots.append(AvailabilitySlot(
            start=current_time,
            end=normalized_end,
            provider=provider
        ))
    
    # Filter out slots shorter than granularity
    return [
        slot for slot in available_slots 
        if (slot.end - slot.start).total_seconds() >= SLOT_GRANULARITY_MINUTES * 60
    ]


async def get_active_calendar_connections(tenant_id: UUID, db: AsyncSession) -> List[CalendarConnection]:
    """
    Get active calendar connections for a tenant.
    
    Args:
        tenant_id: The tenant UUID
        db: Database session
        
    Returns:
        List of active calendar connections
    """
    query = select(CalendarConnection).where(
        CalendarConnection.user_id == str(tenant_id),
        CalendarConnection.is_active == True
    )
    result = await db.execute(query)
    return result.scalars().all()


async def fetch_provider_availability(
    connection: CalendarConnection,
    time_min: datetime,
    time_max: datetime
) -> List[AvailabilitySlot]:
    """
    Fetch availability from a single provider.
    
    Args:
        connection: Calendar connection
        time_min: Start time
        time_max: End time
        
    Returns:
        List of availability slots from this provider
    """
    try:
        provider = get_provider(connection.user_id, connection.provider)
        
        # Get calendar IDs - use primary if available, otherwise use connection calendar_id
        calendar_ids = [connection.calendar_id or "primary"]
        
        # Fetch busy times from provider
        busy_data = await provider.get_availability(calendar_ids, time_min, time_max)
        
        # Convert to available slots
        all_slots = []
        for calendar_data in busy_data:
            busy_times = calendar_data.get("busy", [])
            slots = convert_busy_to_available_slots(busy_times, time_min, time_max, connection.provider)
            all_slots.extend(slots)
        
        return all_slots
        
    except Exception as e:
        logger.warning(f"Failed to fetch availability from {connection.provider}: {str(e)}")
        return []


async def get_aggregated_availability(
    tenant_id: UUID,
    from_dt: datetime,
    to_dt: datetime
) -> List[AvailabilitySlot]:
    """
    Get aggregated availability from all connected providers for a tenant.

    Args:
        tenant_id: The tenant UUID
        from_dt: Start of the time range
        to_dt: End of the time range

    Returns:
        List of merged and normalized availability slots
    """
    # Generate cache key
    cache_key = f"avail:{tenant_id}:{from_dt.isoformat()}:{to_dt.isoformat()}"

    async def fetch_availability():
        """Inner function to fetch availability from all providers."""
        # Import here to avoid circular imports
        from ..db.base import get_async_db

        # Get database session
        db_gen = get_async_db()
        db = await db_gen.__anext__()

        try:
            # Get active calendar connections
            connections = await get_active_calendar_connections(tenant_id, db)

            if not connections:
                logger.info(f"No active calendar connections found for tenant {tenant_id}")
                return []

            # Fetch availability from all providers concurrently
            tasks = [
                fetch_provider_availability(conn, from_dt, to_dt)
                for conn in connections
            ]

            provider_results = await asyncio.gather(*tasks, return_exceptions=True)

            # Collect all slots, filtering out exceptions
            all_slots = []
            for i, result in enumerate(provider_results):
                if isinstance(result, Exception):
                    logger.warning(f"Provider {connections[i].provider} failed: {result}")
                else:
                    all_slots.extend(result)

            # Merge overlapping slots
            merged_slots = merge_overlapping_slots(all_slots)

            logger.info(f"Aggregated {len(merged_slots)} availability slots for tenant {tenant_id}")
            return merged_slots

        finally:
            # Close database session
            await db.close()

    # For now, just call the function directly without caching
    # TODO: Implement proper caching integration
    return await fetch_availability()
