from pydantic_settings import BaseSettings
from typing import Optional, List

class Settings(BaseSettings):
    # Google OAuth
    google_client_id: str
    google_client_secret: str
    google_redirect_uri: str
    
    # Calendly OAuth
    calendly_client_id: Optional[str] = None
    calendly_client_secret: Optional[str] = None
    calendly_redirect_uri: Optional[str] = None
    
    # Database
    database_url: str = "sqlite:///./test.db"
    
    # App
    app_name: str = "AI Lex Receptionist - Calendar Service"
    debug: bool = False
    environment: str = "development"
    
    # CORS
    cors_origins: List[str] = ["*"]
    
    # Security
    secret_key: str = "your-secret-key-here"
    access_token_expire_minutes: int = 30

    # Webhooks
    calendly_webhook_secret: str = "test_webhook_secret"

    # Cache settings
    avail_cache_ttl: int = 30  # Availability cache TTL in seconds
    redis_uri: Optional[str] = None
    redis_url: Optional[str] = None  # Alternative name for Redis URI

    # Additional settings from env
    database_echo: bool = False
    jwt_secret_key: Optional[str] = None
    jwt_algorithm: str = "HS256"
    jwt_access_token_expire_minutes: int = 30
    api_v1_str: str = "/api/v1"
    project_name: str = "AI Lex Receptionist - Calendar Service"
    backend_cors_origins: List[str] = ["*"]
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        
    @property
    def GOOGLE_CLIENT_ID(self) -> str:
        return self.google_client_id
        
    @property
    def GOOGLE_CLIENT_SECRET(self) -> str:
        return self.google_client_secret
        
    @property
    def GOOGLE_REDIRECT_URI(self) -> str:
        return self.google_redirect_uri
        
    @property
    def CALENDLY_CLIENT_ID(self) -> Optional[str]:
        return self.calendly_client_id
        
    @property
    def CALENDLY_CLIENT_SECRET(self) -> Optional[str]:
        return self.calendly_client_secret
        
    @property
    def CALENDLY_REDIRECT_URI(self) -> Optional[str]:
        return self.calendly_redirect_uri
        
    @property
    def DATABASE_URL(self) -> str:
        return self.database_url
        
    @property
    def DATABASE_URL_ASYNC(self) -> str:
        # Convert sync URL to async URL if needed
        if self.database_url.startswith("sqlite+aiosqlite"):
            return self.database_url  # Already async
        elif self.database_url.startswith("sqlite"):
            return self.database_url.replace("sqlite:///", "sqlite+aiosqlite:///")
        elif self.database_url.startswith("postgresql+asyncpg"):
            return self.database_url  # Already async
        return self.database_url.replace("postgresql://", "postgresql+asyncpg://")

    @property
    def CALENDLY_WEBHOOK_SECRET(self) -> str:
        return self.calendly_webhook_secret

    @property
    def AVAIL_CACHE_TTL(self) -> int:
        return self.avail_cache_ttl

    @property
    def REDIS_URI(self) -> Optional[str]:
        return self.redis_uri

# Create settings instance
settings = Settings()
