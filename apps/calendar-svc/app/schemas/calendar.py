from pydantic import BaseModel, Field, HttpUrl, validator, EmailStr
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import uuid
from enum import Enum

from .base import BaseSchema

# Enums
class CalendarProvider(str, Enum):
    GOOGLE = "google"
    MICROSOFT = "microsoft"
    CALENDLY = "calendly"
    OTHER = "other"

class CalendarConnectionStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"

# Calendar Connection Schemas
class CalendarConnectionBase(BaseModel):
    user_id: str = Field(..., description="ID of the user who owns this connection")
    provider: CalendarProvider = Field(..., description="Calendar provider (e.g., 'google', 'microsoft')")
    calendar_id: Optional[str] = Field(None, description="Primary calendar ID from the provider")
    provider_user_id: Optional[str] = Field(None, description="User ID from the provider")
    provider_email: Optional[str] = Field(None, description="Email associated with the provider account")
    scopes: Optional[List[str]] = Field(default_factory=list, description="List of OAuth scopes")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional provider-specific data")

class CalendarConnectionCreate(CalendarConnectionBase):
    access_token: str = Field(..., description="OAuth access token")
    refresh_token: Optional[str] = Field(None, description="OAuth refresh token (if available)")
    token_expiry: Optional[datetime] = Field(None, description="When the access token expires")

class CalendarConnectionUpdate(BaseModel):
    calendar_id: Optional[str] = None
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    token_expiry: Optional[datetime] = None
    status: Optional[CalendarConnectionStatus] = None
    metadata: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None

class CalendarConnectionResponse(CalendarConnectionBase, BaseSchema):
    status: CalendarConnectionStatus = Field(default=CalendarConnectionStatus.ACTIVE)
    is_active: bool = Field(default=True)
    token_expiry: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "user_id": "user_123",
                "provider": "google",
                "calendar_id": "primary",
                "provider_user_id": "**********",
                "provider_email": "<EMAIL>",
                "status": "active",
                "is_active": True,
                "scopes": ["https://www.googleapis.com/auth/calendar"],
                "metadata": {},
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-01T00:00:00Z"
            }
        }

# Calendar Schemas
class Calendar(BaseModel):
    id: str = Field(..., description="ID of the calendar")
    name: str = Field(..., description="Name of the calendar")
    description: Optional[str] = None
    timezone: Optional[str] = Field(None, description="IANA timezone of the calendar")
    primary: bool = Field(False, description="Whether this is the primary calendar")
    selected: bool = Field(False, description="Whether this calendar is selected for use")

    class Config:
        schema_extra = {
            "example": {
                "id": "primary",
                "name": "<EMAIL>",
                "description": "Primary calendar",
                "timezone": "America/Los_Angeles",
                "primary": True,
                "selected": True
            }
        }

# Availability Schemas
class TimeRange(BaseModel):
    start: datetime = Field(..., description="Start time of the range")
    end: datetime = Field(..., description="End time of the range")

    @validator('end')
    def validate_time_range(cls, v, values):
        if 'start' in values and v <= values['start']:
            raise ValueError("End time must be after start time")
        return v

class AvailabilityRequest(BaseModel):
    calendar_ids: List[str] = Field(..., description="List of calendar IDs to check")
    time_min: datetime = Field(..., description="Start of the time range to check")
    time_max: datetime = Field(..., description="End of the time range to check")
    time_zone: str = Field("UTC", description="IANA timezone for the request")
    
    @validator('time_max')
    def validate_time_range(cls, v, values):
        if 'time_min' in values and v <= values['time_min']:
            raise ValueError("time_max must be after time_min")
        if (v - values.get('time_min', v)) > timedelta(days=90):
            raise ValueError("Time range cannot exceed 90 days")
        return v

class FreeBusyResponse(BaseModel):
    calendar_id: str = Field(..., description="Calendar ID")
    busy: List[TimeRange] = Field(default_factory=list, description="List of busy time ranges")

# Appointment Schemas
class AppointmentStatus(str, Enum):
    SCHEDULED = "scheduled"
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"
    RESCHEDULED = "rescheduled"
    COMPLETED = "completed"
    NO_SHOW = "no_show"

class Attendee(BaseModel):
    email: EmailStr
    display_name: Optional[str] = None
    response_status: Optional[str] = None  # 'needsAction', 'accepted', 'declined', 'tentative'

class AppointmentBase(BaseModel):
    calendar_connection_id: uuid.UUID = Field(..., description="ID of the calendar connection")
    event_id: Optional[str] = Field(None, description="ID from the calendar provider")
    summary: str = Field(..., description="Title of the appointment")
    description: Optional[str] = None
    location: Optional[str] = None
    start_time: datetime = Field(..., description="Start time of the appointment")
    end_time: datetime = Field(..., description="End time of the appointment")
    timezone: str = Field("UTC", description="IANA timezone of the appointment")
    status: AppointmentStatus = Field(default=AppointmentStatus.SCHEDULED)
    status_note: Optional[str] = None
    is_recurring: bool = Field(False, description="Whether this is a recurring appointment")
    recurrence_rule: Optional[str] = Field(None, description="Recurrence rule in iCal format")
    master_event_id: Optional[str] = Field(None, description="For recurring events, the ID of the master event")
    attendees: List[Attendee] = Field(default_factory=list, description="List of attendees")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

class AppointmentCreate(AppointmentBase):
    pass

class AppointmentUpdate(BaseModel):
    summary: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    timezone: Optional[str] = None
    status: Optional[AppointmentStatus] = None
    status_note: Optional[str] = None
    attendees: Optional[List[Attendee]] = None
    metadata: Optional[Dict[str, Any]] = None

class AppointmentResponse(AppointmentBase, BaseSchema):
    created_at: datetime
    updated_at: datetime
    
    class Config:
        schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "calendar_connection_id": "123e4567-e89b-12d3-a456-426614174000",
                "event_id": "abc123",
                "summary": "Initial Consultation",
                "description": "Initial consultation with the client",
                "start_time": "2023-01-15T10:00:00-08:00",
                "end_time": "2023-01-15T11:00:00-08:00",
                "timezone": "America/Los_Angeles",
                "status": "scheduled",
                "is_recurring": False,
                "attendees": [
                    {"email": "<EMAIL>", "display_name": "Client Name"}
                ],
                "metadata": {},
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-01T00:00:00Z"
            }
        }

# OAuth Schemas
class OAuthState(BaseModel):
    user_id: str
    redirect_uri: Optional[str] = None
    state: Optional[Dict[str, Any]] = None

class OAuthTokenResponse(BaseModel):
    access_token: str
    token_type: str
    expires_in: int
    refresh_token: Optional[str] = None
    scope: Optional[str] = None
    id_token: Optional[str] = None

# Calendly-specific Schemas
class EventTypeDTO(BaseModel):
    """Calendly Event Type response model."""
    uri: str = Field(..., description="Calendly URI for the event type")
    name: str = Field(..., description="Name of the event type")
    description: Optional[str] = Field(None, description="Description of the event type")
    duration: int = Field(..., description="Duration in minutes")
    scheduling_url: str = Field(..., description="Public scheduling URL")
    active: bool = Field(True, description="Whether the event type is active")
    kind: str = Field(..., description="Type of event (e.g., 'solo', 'group')")
    pooling_type: Optional[str] = Field(None, description="Pooling type for group events")
    type: str = Field(..., description="Event type category")
    color: Optional[str] = Field(None, description="Color associated with the event type")
    created_at: datetime = Field(..., description="When the event type was created")
    updated_at: datetime = Field(..., description="When the event type was last updated")
    internal_note: Optional[str] = Field(None, description="Internal note for the event type")

    class Config:
        schema_extra = {
            "example": {
                "uri": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
                "name": "30 Minute Meeting",
                "description": "A brief 30-minute consultation",
                "duration": 30,
                "scheduling_url": "https://calendly.com/user/30min",
                "active": True,
                "kind": "solo",
                "type": "StandardEventType",
                "color": "#0069ff",
                "created_at": "2023-01-01T00:00:00.000000Z",
                "updated_at": "2023-01-01T00:00:00.000000Z"
            }
        }

class SchedulingLinkDTO(BaseModel):
    """Calendly Scheduling Link response model."""
    booking_url: str = Field(..., description="URL for booking the appointment")
    owner: str = Field(..., description="Owner of the scheduling link")
    owner_type: str = Field(..., description="Type of owner (e.g., 'EventType')")
    created_at: datetime = Field(..., description="When the link was created")

    class Config:
        schema_extra = {
            "example": {
                "booking_url": "https://calendly.com/d/abc123/30-minute-meeting",
                "owner": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
                "owner_type": "EventType",
                "created_at": "2023-01-01T00:00:00.000000Z"
            }
        }

class SchedulingLinkRequest(BaseModel):
    """Request model for creating a scheduling link."""
    event_type_id: str = Field(..., description="Calendly event type URI or ID")
    max_events: int = Field(1, description="Maximum number of events that can be scheduled")
    start_time: Optional[datetime] = Field(None, description="Earliest time for scheduling")
    end_time: Optional[datetime] = Field(None, description="Latest time for scheduling")

    class Config:
        schema_extra = {
            "example": {
                "event_type_id": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
                "max_events": 1,
                "start_time": "2023-12-01T09:00:00Z",
                "end_time": "2023-12-31T17:00:00Z"
            }
        }

# Unified Availability Schemas
class AvailabilitySlot(BaseModel):
    """Unified availability slot model."""
    start: datetime = Field(..., description="Start time of the availability slot")
    end: datetime = Field(..., description="End time of the availability slot")
    provider: str = Field(..., description="Calendar provider (e.g., 'google', 'calendly')")

    @validator('end')
    def validate_time_range(cls, v, values):
        if 'start' in values and v <= values['start']:
            raise ValueError("End time must be after start time")
        return v

    class Config:
        schema_extra = {
            "example": {
                "start": "2023-12-01T09:00:00Z",
                "end": "2023-12-01T09:15:00Z",
                "provider": "google"
            }
        }

class AvailabilitySlotDTO(BaseModel):
    """DTO for availability slot responses."""
    start: datetime = Field(..., description="Start time of the availability slot")
    end: datetime = Field(..., description="End time of the availability slot")
    provider: str = Field(..., description="Calendar provider (e.g., 'google', 'calendly')")
    duration_minutes: int = Field(..., description="Duration of the slot in minutes")

    @validator('duration_minutes', always=True)
    def calculate_duration(cls, v, values):
        if 'start' in values and 'end' in values:
            delta = values['end'] - values['start']
            return int(delta.total_seconds() / 60)
        return v

    class Config:
        schema_extra = {
            "example": {
                "start": "2023-12-01T09:00:00Z",
                "end": "2023-12-01T09:15:00Z",
                "provider": "google",
                "duration_minutes": 15
            }
        }

class UnifiedAvailabilityRequest(BaseModel):
    """Request model for unified availability."""
    from_dt: datetime = Field(..., alias="from", description="Start of the time range to check")
    to_dt: datetime = Field(..., alias="to", description="End of the time range to check")
    time_zone: str = Field("UTC", description="IANA timezone for the request")

    @validator('to_dt')
    def validate_time_range(cls, v, values):
        if 'from_dt' in values and v <= values['from_dt']:
            raise ValueError("'to' must be after 'from'")
        if (v - values.get('from_dt', v)) > timedelta(days=90):
            raise ValueError("Time range cannot exceed 90 days")
        return v

    class Config:
        allow_population_by_field_name = True
        schema_extra = {
            "example": {
                "from": "2023-12-01T09:00:00Z",
                "to": "2023-12-01T17:00:00Z",
                "time_zone": "UTC"
            }
        }
