"""
Subscription check middleware and decorators for calendar service.

This provides a simplified feature checking mechanism for the calendar service.
"""
import logging
from functools import wraps
from typing import List, Optional

from fastapi import HTTPException, Request, status
from fastapi.dependencies.utils import get_dependant
from fastapi.routing import APIRoute

logger = logging.getLogger(__name__)


class SubscriptionCheckMiddleware:
    """
    Simplified subscription check middleware for calendar service.
    
    In a production environment, this would integrate with a subscription service.
    For now, it provides a basic feature checking mechanism.
    """
    
    def __init__(self, app, enabled_features: Optional[List[str]] = None):
        self.app = app
        # Default features for development/testing
        self.enabled_features = enabled_features or [
            "calendar_booking",
            "voice_intake",
            "calendar_integration"
        ]
    
    async def __call__(self, request: Request, call_next):
        """Process request through subscription check."""
        # Skip middleware for health checks and docs
        if request.url.path in ["/health", "/", "/docs", "/redoc", "/openapi.json"]:
            return await call_next(request)
        
        # Inject features into request state for downstream use
        request.state.features = self.enabled_features
        request.state.tenant_id = getattr(request.state, 'tenant_id', 'default-tenant')
        
        return await call_next(request)


def require_feature(feature_name: str):
    """
    Decorator to require a specific feature for an endpoint.
    
    Args:
        feature_name: Name of the required feature
        
    Returns:
        Decorator function
        
    Raises:
        HTTPException: If the feature is not available
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract request from args/kwargs
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                # Try to find request in kwargs
                for value in kwargs.values():
                    if isinstance(value, Request):
                        request = value
                        break
            
            if not request:
                # If no request found, assume feature is available (for testing)
                logger.warning(f"No request found for feature check: {feature_name}")
                return await func(*args, **kwargs)
            
            # Check if features are available in request state
            features = getattr(request.state, 'features', [])
            
            if feature_name not in features:
                logger.warning(f"Feature '{feature_name}' not available for request")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Access denied: feature '{feature_name}' not available"
                )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def check_feature_access(request: Request, feature_name: str) -> bool:
    """
    Check if a feature is available for the current request.
    
    Args:
        request: FastAPI request object
        feature_name: Name of the feature to check
        
    Returns:
        True if feature is available, False otherwise
    """
    features = getattr(request.state, 'features', [])
    return feature_name in features


def get_user_features(request: Request) -> List[str]:
    """
    Get list of available features for the current request.
    
    Args:
        request: FastAPI request object
        
    Returns:
        List of available feature names
    """
    return getattr(request.state, 'features', [])
