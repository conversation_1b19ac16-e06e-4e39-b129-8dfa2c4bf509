"""Booking helpers for calendar service with real provider integration."""
from __future__ import annotations

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

from fastapi import HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from packages.calendar_core import get_provider
from packages.shared import models as shared_models
from .utils.timezones import get_available_slots

logger = logging.getLogger(__name__)


class SlotUnavailableError(Exception):
    """Raised when a proposed slot is no longer available."""
    pass


class ProviderError(Exception):
    """Raised when a provider operation fails."""
    pass


class ProposedSlot:
    """Represents a proposed booking slot with provider metadata."""
    
    def __init__(
        self,
        start_at: datetime,
        end_at: datetime,
        provider: str,
        calendar_id: str,
        duration_minutes: int = 30
    ):
        self.start_at = start_at
        self.end_at = end_at
        self.provider = provider
        self.calendar_id = calendar_id
        self.duration_minutes = duration_minutes
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "start_at": self.start_at.isoformat(),
            "end_at": self.end_at.isoformat(),
            "provider": self.provider,
            "calendar_id": self.calendar_id,
            "duration_minutes": self.duration_minutes
        }


async def get_aggregated_availability(
    tenant_id: str,
    time_min: datetime,
    time_max: datetime,
    providers: Optional[List[str]] = None
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get aggregated availability across multiple providers.
    
    Args:
        tenant_id: The tenant/firm ID
        time_min: Start of time range to check
        time_max: End of time range to check
        providers: List of providers to check (defaults to all configured)
        
    Returns:
        Dictionary mapping provider names to their availability data
    """
    if providers is None:
        providers = ["google", "calendly"]  # Default providers
    
    availability_data = {}
    
    for provider_name in providers:
        try:
            provider = get_provider(tenant_id, provider_name)
            
            # Get primary calendar for the provider
            calendars = await provider.list_calendars()
            if not calendars:
                logger.warning(f"No calendars found for provider {provider_name}")
                continue
                
            # Use primary calendar or first available
            primary_calendar = next(
                (cal for cal in calendars if cal.get("primary", False)),
                calendars[0] if calendars else None
            )
            
            if not primary_calendar:
                continue
                
            calendar_ids = [primary_calendar["id"]]
            
            # Get availability from provider
            availability = await provider.get_availability(
                calendar_ids, time_min, time_max
            )
            
            availability_data[provider_name] = {
                "calendars": calendar_ids,
                "availability": availability
            }
            
        except Exception as e:
            logger.error(f"Failed to get availability for provider {provider_name}: {str(e)}")
            # Continue with other providers even if one fails
            availability_data[provider_name] = {
                "calendars": [],
                "availability": [],
                "error": str(e)
            }
    
    return availability_data


async def proposeSlot(
    tenant_id: str,
    preferred_start: Optional[datetime] = None,
    duration_minutes: int = 30,
    max_slots: int = 3
) -> List[ProposedSlot]:
    """
    Propose available booking slots across providers.
    
    Args:
        tenant_id: The tenant/firm ID
        preferred_start: Preferred start time (defaults to next business hour)
        duration_minutes: Duration of the slot in minutes
        max_slots: Maximum number of slots to return
        
    Returns:
        List of proposed slots with provider metadata
    """
    # Set default time range if not provided
    if preferred_start is None:
        now = datetime.utcnow()
        # Start from next hour, during business hours (9 AM - 5 PM UTC)
        preferred_start = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
        if preferred_start.hour < 9:
            preferred_start = preferred_start.replace(hour=9)
        elif preferred_start.hour >= 17:
            # Move to next day 9 AM
            preferred_start = (preferred_start + timedelta(days=1)).replace(hour=9)
    
    # Search window: 5 business days from preferred start
    time_min = preferred_start
    time_max = preferred_start + timedelta(days=5)
    
    # Get availability from all providers
    availability_data = await get_aggregated_availability(tenant_id, time_min, time_max)
    
    proposed_slots = []
    slot_duration = timedelta(minutes=duration_minutes)
    
    # Process each provider's availability
    for provider_name, provider_data in availability_data.items():
        if "error" in provider_data:
            continue
            
        availability = provider_data.get("availability", [])
        calendars = provider_data.get("calendars", [])
        
        if not availability or not calendars:
            continue
            
        # Process each calendar's availability
        for cal_availability in availability:
            calendar_id = cal_availability.get("calendar_id")
            busy_times = cal_availability.get("busy", [])
            
            # Convert busy times to datetime objects
            busy_slots = []
            for busy in busy_times:
                try:
                    start = datetime.fromisoformat(busy["start"].replace("Z", "+00:00"))
                    end = datetime.fromisoformat(busy["end"].replace("Z", "+00:00"))
                    busy_slots.append({"start": start, "end": end})
                except (KeyError, ValueError) as e:
                    logger.warning(f"Invalid busy time format: {busy}, error: {e}")
                    continue
            
            # Find available slots
            available_slots = get_available_slots(
                busy_slots=busy_slots,
                start_time=time_min,
                end_time=time_max,
                duration=slot_duration,
                buffer=timedelta(minutes=5)  # 5-minute buffer
            )
            
            # Convert to ProposedSlot objects
            for slot in available_slots:
                if len(proposed_slots) >= max_slots:
                    break
                    
                proposed_slot = ProposedSlot(
                    start_at=slot["start"],
                    end_at=slot["end"],
                    provider=provider_name,
                    calendar_id=calendar_id,
                    duration_minutes=duration_minutes
                )
                proposed_slots.append(proposed_slot)
            
            if len(proposed_slots) >= max_slots:
                break
        
        if len(proposed_slots) >= max_slots:
            break
    
    # Sort by start time and return
    proposed_slots.sort(key=lambda x: x.start_at)
    return proposed_slots[:max_slots]


@retry(
    stop=stop_after_attempt(2),
    wait=wait_exponential(multiplier=1, min=2, max=5),
    retry=retry_if_exception_type((ProviderError,))
)
async def confirmSlot(
    tenant_id: str,
    proposed_slot: ProposedSlot,
    summary: str,
    db: AsyncSession,
    attendee_email: Optional[str] = None,
    description: Optional[str] = None
) -> Dict[str, Any]:
    """
    Confirm a proposed slot by creating the event/scheduling link and persisting the booking.
    
    Args:
        tenant_id: The tenant/firm ID
        proposed_slot: The slot to confirm
        summary: Event summary/title
        db: Database session
        attendee_email: Optional attendee email
        description: Optional event description
        
    Returns:
        Dictionary with booking confirmation details
        
    Raises:
        SlotUnavailableError: If the slot is no longer available
        ProviderError: If provider operation fails
    """
    try:
        # Get the provider
        provider = get_provider(tenant_id, proposed_slot.provider)
        
        # Check if slot is still available (basic check)
        current_time = datetime.utcnow()
        if proposed_slot.start_at <= current_time:
            raise SlotUnavailableError("Proposed slot is in the past")
        
        # Prepare event data
        event_data = {
            "summary": summary,
            "description": description or "",
            "start": proposed_slot.start_at.isoformat(),
            "end": proposed_slot.end_at.isoformat(),
        }
        
        # Add attendee if provided
        if attendee_email:
            event_data["attendees"] = [{"email": attendee_email}]
        
        # Create event/scheduling link based on provider
        if proposed_slot.provider.lower() == "calendly":
            # For Calendly, create_event returns a scheduling link
            result = await provider.create_event(proposed_slot.calendar_id, event_data)
            external_id = result.get("calendly_link", result.get("id"))
            provider_event_link = result.get("calendly_link", result.get("htmlLink"))
        else:
            # For Google/Outlook, create_event returns an actual event
            result = await provider.create_event(proposed_slot.calendar_id, event_data)
            external_id = result.get("id")
            provider_event_link = result.get("htmlLink") or result.get("meetingLink")
        
        # Create booking record
        booking = shared_models.Booking(
            firm_id=tenant_id,
            provider=shared_models.BookingProvider(proposed_slot.provider.lower()),
            external_id=external_id,
            start_at=proposed_slot.start_at,
            booked_at=datetime.utcnow(),
            provider_event_link=provider_event_link,
            metadata_=result,
            status="confirmed"
        )
        
        # Persist to database
        db.add(booking)
        await db.commit()
        await db.refresh(booking)
        
        return {
            "booking_id": str(booking.id),
            "external_id": external_id,
            "provider": proposed_slot.provider,
            "provider_event_link": provider_event_link,
            "start_at": proposed_slot.start_at.isoformat(),
            "end_at": proposed_slot.end_at.isoformat(),
            "status": "confirmed",
            "booked_at": booking.booked_at.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to confirm slot: {str(e)}")
        
        # Check if it's a slot availability issue
        if "conflict" in str(e).lower() or "busy" in str(e).lower():
            raise SlotUnavailableError(f"Slot is no longer available: {str(e)}")
        
        # For 5xx errors, raise ProviderError to trigger retry
        if hasattr(e, 'response') and hasattr(e.response, 'status_code'):
            if 500 <= e.response.status_code < 600:
                raise ProviderError(f"Provider server error: {str(e)}")
        
        # Re-raise other exceptions
        raise
