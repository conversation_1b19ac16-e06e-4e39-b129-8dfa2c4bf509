#!/usr/bin/env python3
"""Simple test for booking helpers without full app setup."""
import sys
import os
sys.path.insert(0, '/mnt/persist/workspace')

import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch, MagicMock
from uuid import uuid4

from app.booking_helpers import (
    proposeSlot,
    confirmSlot,
    get_aggregated_availability,
    ProposedSlot,
    SlotUnavailableError,
    ProviderError
)


@pytest.mark.asyncio
async def test_proposed_slot_creation():
    """Test ProposedSlot creation and serialization."""
    start_time = datetime.utcnow() + timedelta(hours=1)
    end_time = start_time + timedelta(minutes=30)
    
    slot = ProposedSlot(
        start_at=start_time,
        end_at=end_time,
        provider="google",
        calendar_id="primary",
        duration_minutes=30
    )
    
    assert slot.start_at == start_time
    assert slot.end_at == end_time
    assert slot.provider == "google"
    assert slot.calendar_id == "primary"
    assert slot.duration_minutes == 30
    
    # Test serialization
    slot_dict = slot.to_dict()
    assert slot_dict["provider"] == "google"
    assert slot_dict["calendar_id"] == "primary"
    assert slot_dict["duration_minutes"] == 30


@pytest.mark.asyncio
async def test_get_aggregated_availability_success():
    """Test successful availability aggregation."""
    tenant_id = "test-tenant"
    time_min = datetime(2024, 1, 15, 8, 0, 0)
    time_max = datetime(2024, 1, 15, 18, 0, 0)

    with patch('app.booking_helpers.get_provider') as mock_get_provider:
        # Mock Google provider
        google_provider = AsyncMock()
        google_provider.list_calendars.return_value = [{"id": "primary", "primary": True}]
        google_provider.get_availability.return_value = [
            {"calendar_id": "primary", "busy": []}
        ]

        # Mock Calendly provider
        calendly_provider = AsyncMock()
        calendly_provider.list_calendars.return_value = [
            {"id": "https://api.calendly.com/event_types/123", "primary": True}
        ]
        calendly_provider.get_availability.return_value = [
            {"calendar_id": "https://api.calendly.com/event_types/123", "busy": []}
        ]

        # Configure mock to return different providers
        def get_provider_side_effect(tenant, provider_name):
            if provider_name == "google":
                return google_provider
            elif provider_name == "calendly":
                return calendly_provider
            else:
                raise ValueError(f"Unknown provider: {provider_name}")

        mock_get_provider.side_effect = get_provider_side_effect

        # Test aggregation
        result = await get_aggregated_availability(tenant_id, time_min, time_max)

        # Verify both providers were called
        assert "google" in result
        assert "calendly" in result
        assert result["google"]["calendars"] == ["primary"]
        assert result["calendly"]["calendars"] == ["https://api.calendly.com/event_types/123"]


@pytest.mark.asyncio
async def test_propose_slot_basic():
    """Test basic slot proposal functionality."""
    tenant_id = "test-tenant"
    
    with patch('app.booking_helpers.get_aggregated_availability') as mock_get_availability:
        # Mock availability data
        mock_get_availability.return_value = {
            "google": {
                "calendars": ["primary"],
                "availability": [
                    {
                        "calendar_id": "primary",
                        "busy": []  # No busy times = all available
                    }
                ]
            }
        }

        with patch('app.booking_helpers.get_available_slots') as mock_get_slots:
            # Mock available slots
            now = datetime.utcnow()
            mock_get_slots.return_value = [
                {"start": now + timedelta(hours=1), "end": now + timedelta(hours=1, minutes=30)},
                {"start": now + timedelta(hours=2), "end": now + timedelta(hours=2, minutes=30)},
            ]

            slots = await proposeSlot(tenant_id, max_slots=2)

            assert len(slots) > 0
            assert all(isinstance(slot, ProposedSlot) for slot in slots)
            assert slots[0].provider == "google"
            assert slots[0].calendar_id == "primary"


@pytest.mark.asyncio
async def test_confirm_slot_google():
    """Test Google Calendar slot confirmation."""
    tenant_id = "test-tenant"
    summary = "Test Meeting"
    
    # Create proposed slot
    start_time = datetime.utcnow() + timedelta(hours=1)
    end_time = start_time + timedelta(minutes=30)
    proposed_slot = ProposedSlot(
        start_at=start_time,
        end_at=end_time,
        provider="google",
        calendar_id="primary",
        duration_minutes=30
    )

    # Mock database session
    mock_db = AsyncMock()
    mock_db.add = MagicMock()
    mock_db.commit = AsyncMock()
    mock_db.refresh = AsyncMock()

    with patch('app.booking_helpers.get_provider') as mock_get_provider:
        # Mock Google provider
        google_provider = AsyncMock()
        google_provider.create_event.return_value = {
            "id": "google-event-123",
            "htmlLink": "https://calendar.google.com/event/123",
            "summary": summary
        }
        mock_get_provider.return_value = google_provider

        # Mock booking creation
        mock_booking = MagicMock()
        mock_booking.id = uuid4()
        mock_booking.booked_at = datetime.utcnow()
        
        def mock_refresh_side_effect(obj):
            obj.id = mock_booking.id
            obj.booked_at = mock_booking.booked_at
        
        mock_db.refresh.side_effect = mock_refresh_side_effect

        result = await confirmSlot(
            tenant_id=tenant_id,
            proposed_slot=proposed_slot,
            summary=summary,
            db=mock_db
        )

        # Verify result
        assert result["external_id"] == "google-event-123"
        assert result["provider"] == "google"
        assert result["provider_event_link"] == "https://calendar.google.com/event/123"
        assert result["status"] == "confirmed"

        # Verify database operations
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()


@pytest.mark.asyncio
async def test_confirm_slot_past_time_error():
    """Test that SlotUnavailableError is raised for past slots."""
    tenant_id = "test-tenant"
    summary = "Test Meeting"
    
    # Create slot in the past
    past_time = datetime.utcnow() - timedelta(hours=1)
    past_slot = ProposedSlot(
        start_at=past_time,
        end_at=past_time + timedelta(minutes=30),
        provider="google",
        calendar_id="primary",
        duration_minutes=30
    )

    mock_db = AsyncMock()

    with pytest.raises(SlotUnavailableError):
        await confirmSlot(
            tenant_id=tenant_id,
            proposed_slot=past_slot,
            summary=summary,
            db=mock_db
        )


if __name__ == "__main__":
    import asyncio
    
    async def run_tests():
        print("Testing ProposedSlot creation...")
        await test_proposed_slot_creation()
        print("✓ ProposedSlot creation test passed")
        
        print("Testing availability aggregation...")
        await test_get_aggregated_availability_success()
        print("✓ Availability aggregation test passed")
        
        print("Testing slot proposal...")
        await test_propose_slot_basic()
        print("✓ Slot proposal test passed")
        
        print("Testing Google slot confirmation...")
        await test_confirm_slot_google()
        print("✓ Google slot confirmation test passed")
        
        print("Testing past slot error...")
        await test_confirm_slot_past_time_error()
        print("✓ Past slot error test passed")
        
        print("\nAll tests passed! ✓")
    
    asyncio.run(run_tests())
