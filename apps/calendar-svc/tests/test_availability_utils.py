"""
Tests for availability utility functions.
"""
import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch, MagicMock
from uuid import uuid4

from app.utils.availability import (
    normalize_to_granularity,
    merge_overlapping_slots,
    convert_busy_to_available_slots,
    get_aggregated_availability,
    SLOT_GRANULARITY_MINUTES
)
from app.schemas.calendar import AvailabilitySlot
from app.db.models.calendar_connection import CalendarConnection


class TestNormalizeToGranularity:
    """Test datetime normalization to granularity."""
    
    def test_normalize_to_15_minute_boundary(self):
        """Test normalization to 15-minute boundaries."""
        # Test various times
        test_cases = [
            (datetime(2023, 12, 1, 9, 7, 30), datetime(2023, 12, 1, 9, 0, 0)),
            (datetime(2023, 12, 1, 9, 15, 45), datetime(2023, 12, 1, 9, 15, 0)),
            (datetime(2023, 12, 1, 9, 23, 10), datetime(2023, 12, 1, 9, 15, 0)),
            (datetime(2023, 12, 1, 9, 30, 0), datetime(2023, 12, 1, 9, 30, 0)),
            (datetime(2023, 12, 1, 9, 44, 59), datetime(2023, 12, 1, 9, 30, 0)),
        ]
        
        for input_dt, expected_dt in test_cases:
            result = normalize_to_granularity(input_dt)
            assert result == expected_dt
    
    def test_normalize_custom_granularity(self):
        """Test normalization with custom granularity."""
        dt = datetime(2023, 12, 1, 9, 37, 30)
        
        # 30-minute granularity
        result = normalize_to_granularity(dt, 30)
        expected = datetime(2023, 12, 1, 9, 30, 0)
        assert result == expected
        
        # 5-minute granularity
        result = normalize_to_granularity(dt, 5)
        expected = datetime(2023, 12, 1, 9, 35, 0)
        assert result == expected


class TestMergeOverlappingSlots:
    """Test slot merging logic."""
    
    def test_merge_overlapping_slots(self):
        """Test merging overlapping slots."""
        slots = [
            AvailabilitySlot(
                start=datetime(2023, 12, 1, 9, 0),
                end=datetime(2023, 12, 1, 10, 0),
                provider="google"
            ),
            AvailabilitySlot(
                start=datetime(2023, 12, 1, 9, 30),
                end=datetime(2023, 12, 1, 11, 0),
                provider="calendly"
            ),
        ]
        
        merged = merge_overlapping_slots(slots)
        
        assert len(merged) == 1
        assert merged[0].start == datetime(2023, 12, 1, 9, 0)
        assert merged[0].end == datetime(2023, 12, 1, 11, 0)
        assert "google" in merged[0].provider
        assert "calendly" in merged[0].provider
    
    def test_merge_adjacent_slots(self):
        """Test merging adjacent slots."""
        slots = [
            AvailabilitySlot(
                start=datetime(2023, 12, 1, 9, 0),
                end=datetime(2023, 12, 1, 10, 0),
                provider="google"
            ),
            AvailabilitySlot(
                start=datetime(2023, 12, 1, 10, 0),
                end=datetime(2023, 12, 1, 11, 0),
                provider="calendly"
            ),
        ]
        
        merged = merge_overlapping_slots(slots)
        
        assert len(merged) == 1
        assert merged[0].start == datetime(2023, 12, 1, 9, 0)
        assert merged[0].end == datetime(2023, 12, 1, 11, 0)
    
    def test_no_overlap_slots(self):
        """Test slots with no overlap."""
        slots = [
            AvailabilitySlot(
                start=datetime(2023, 12, 1, 9, 0),
                end=datetime(2023, 12, 1, 10, 0),
                provider="google"
            ),
            AvailabilitySlot(
                start=datetime(2023, 12, 1, 11, 0),
                end=datetime(2023, 12, 1, 12, 0),
                provider="calendly"
            ),
        ]
        
        merged = merge_overlapping_slots(slots)
        
        assert len(merged) == 2
        assert merged[0].provider == "google"
        assert merged[1].provider == "calendly"
    
    def test_empty_slots(self):
        """Test empty slot list."""
        merged = merge_overlapping_slots([])
        assert merged == []


class TestConvertBusyToAvailableSlots:
    """Test conversion from busy times to available slots."""
    
    def test_convert_with_busy_times(self):
        """Test conversion with busy periods."""
        busy_times = [
            {"start": "2023-12-01T10:00:00Z", "end": "2023-12-01T11:00:00Z"},
            {"start": "2023-12-01T14:00:00Z", "end": "2023-12-01T15:00:00Z"},
        ]
        
        time_min = datetime(2023, 12, 1, 9, 0)
        time_max = datetime(2023, 12, 1, 17, 0)
        
        slots = convert_busy_to_available_slots(busy_times, time_min, time_max, "google")
        
        # Should have 3 available slots: 9-10, 11-14, 15-17
        assert len(slots) == 3
        
        # Check first slot
        assert slots[0].start == datetime(2023, 12, 1, 9, 0)
        assert slots[0].end == datetime(2023, 12, 1, 10, 0)
        assert slots[0].provider == "google"
        
        # Check second slot
        assert slots[1].start == datetime(2023, 12, 1, 11, 0)
        assert slots[1].end == datetime(2023, 12, 1, 14, 0)
        
        # Check third slot
        assert slots[2].start == datetime(2023, 12, 1, 15, 0)
        assert slots[2].end == datetime(2023, 12, 1, 17, 0)
    
    def test_convert_no_busy_times(self):
        """Test conversion with no busy periods."""
        busy_times = []
        
        time_min = datetime(2023, 12, 1, 9, 0)
        time_max = datetime(2023, 12, 1, 17, 0)
        
        slots = convert_busy_to_available_slots(busy_times, time_min, time_max, "google")
        
        # Should have one slot covering the entire period
        assert len(slots) == 1
        assert slots[0].start == datetime(2023, 12, 1, 9, 0)
        assert slots[0].end == datetime(2023, 12, 1, 17, 0)
        assert slots[0].provider == "google"
    
    def test_filter_short_slots(self):
        """Test filtering of slots shorter than granularity."""
        busy_times = [
            {"start": "2023-12-01T09:05:00Z", "end": "2023-12-01T09:10:00Z"},
        ]
        
        time_min = datetime(2023, 12, 1, 9, 0)
        time_max = datetime(2023, 12, 1, 9, 15)
        
        slots = convert_busy_to_available_slots(busy_times, time_min, time_max, "google")
        
        # Should filter out slots shorter than 15 minutes
        assert len(slots) == 0


@pytest.mark.asyncio
class TestGetAggregatedAvailability:
    """Test aggregated availability function."""
    
    @patch('app.utils.availability.get_async_db')
    @patch('app.utils.availability.get_active_calendar_connections')
    @patch('app.utils.availability.cache_get_or_set')
    async def test_get_aggregated_availability_success(
        self, 
        mock_cache, 
        mock_get_connections, 
        mock_get_db
    ):
        """Test successful availability aggregation."""
        tenant_id = uuid4()
        from_dt = datetime(2023, 12, 1, 9, 0)
        to_dt = datetime(2023, 12, 1, 17, 0)
        
        # Mock database session
        mock_db = AsyncMock()
        mock_get_db.return_value.__aenter__.return_value = mock_db
        
        # Mock calendar connections
        mock_connections = [
            CalendarConnection(
                id=uuid4(),
                user_id=str(tenant_id),
                provider="google",
                calendar_id="primary",
                is_active=True
            ),
            CalendarConnection(
                id=uuid4(),
                user_id=str(tenant_id),
                provider="calendly",
                calendar_id="event_type_123",
                is_active=True
            )
        ]
        mock_get_connections.return_value = mock_connections
        
        # Mock cache to call the function directly
        async def mock_cache_func(key, func, ttl):
            return await func()
        
        mock_cache.side_effect = mock_cache_func
        
        # Mock provider responses
        with patch('app.utils.availability.get_provider') as mock_get_provider:
            mock_google_provider = AsyncMock()
            mock_google_provider.get_availability.return_value = [
                {"calendar_id": "primary", "busy": []}
            ]
            
            mock_calendly_provider = AsyncMock()
            mock_calendly_provider.get_availability.return_value = [
                {"calendar_id": "event_type_123", "busy": []}
            ]
            
            def provider_factory(user_id, provider_name):
                if provider_name == "google":
                    return mock_google_provider
                elif provider_name == "calendly":
                    return mock_calendly_provider
                else:
                    raise ValueError(f"Unknown provider: {provider_name}")
            
            mock_get_provider.side_effect = provider_factory
            
            # Call the function
            result = await get_aggregated_availability(tenant_id, from_dt, to_dt)
            
            # Verify results
            assert isinstance(result, list)
            # Should have availability slots from both providers
            assert len(result) >= 0  # Could be empty if no availability
    
    @patch('app.utils.availability.get_async_db')
    @patch('app.utils.availability.get_active_calendar_connections')
    @patch('app.utils.availability.cache_get_or_set')
    async def test_get_aggregated_availability_no_connections(
        self, 
        mock_cache, 
        mock_get_connections, 
        mock_get_db
    ):
        """Test availability aggregation with no connections."""
        tenant_id = uuid4()
        from_dt = datetime(2023, 12, 1, 9, 0)
        to_dt = datetime(2023, 12, 1, 17, 0)
        
        # Mock database session
        mock_db = AsyncMock()
        mock_get_db.return_value.__aenter__.return_value = mock_db
        
        # Mock no connections
        mock_get_connections.return_value = []
        
        # Mock cache to call the function directly
        async def mock_cache_func(key, func, ttl):
            return await func()
        
        mock_cache.side_effect = mock_cache_func
        
        # Call the function
        result = await get_aggregated_availability(tenant_id, from_dt, to_dt)
        
        # Should return empty list
        assert result == []
