"""
Integration tests for Calendly booking flow.
"""
import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from fastapi import status

from app.main import app
from packages.shared.models import BookingProvider


@pytest.fixture
def client():
    """Create a test client."""
    return TestClient(app)


@pytest.fixture
def mock_calendly_provider():
    """Mock CalendlyProvider for integration tests."""
    provider = AsyncMock()
    
    # Mock event types
    provider.get_event_types.return_value = [
        {
            "uri": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
            "name": "30 Minute Meeting",
            "description": "A brief 30-minute consultation",
            "duration": 30,
            "scheduling_url": "https://calendly.com/user/30min",
            "active": True,
            "kind": "solo",
            "pooling_type": None,
            "type": "StandardEventType",
            "color": "#0069ff",
            "created_at": "2023-01-01T00:00:00.000000Z",
            "updated_at": "2023-01-01T00:00:00.000000Z",
            "internal_note": None
        }
    ]
    
    # Mock scheduling link creation
    provider.create_scheduling_link.return_value = {
        "booking_url": "https://calendly.com/d/abc123/30-minute-meeting",
        "owner": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
        "owner_type": "EventType",
        "created_at": "2023-01-01T00:00:00.000000Z"
    }
    
    # Mock event creation (returns scheduling link)
    provider.create_event.return_value = {
        "id": "https://calendly.com/d/abc123/30-minute-meeting",
        "htmlLink": "https://calendly.com/d/abc123/30-minute-meeting",
        "summary": "Test Meeting",
        "start": "2023-12-01T10:00:00Z",
        "end": "2023-12-01T10:30:00Z",
        "calendly_link": "https://calendly.com/d/abc123/30-minute-meeting",
        "provider": "calendly"
    }
    
    # Mock availability
    provider.get_availability.return_value = [
        {
            "calendar_id": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
            "busy": []
        }
    ]
    
    return provider


class TestCalendlyIntegration:
    """Integration tests for Calendly booking flow."""

    @pytest.mark.asyncio
    @patch('packages.calendar_core.get_provider')
    @patch('app.api.v1.endpoints.booking.get_async_db')
    async def test_end_to_end_booking_flow(
        self, 
        mock_get_db,
        mock_get_provider,
        client,
        mock_calendly_provider
    ):
        """Test complete booking flow with Calendly."""
        # Mock database
        mock_db = AsyncMock()
        mock_get_db.return_value = mock_db
        
        # Mock provider
        mock_get_provider.return_value = mock_calendly_provider
        
        # Test availability check
        start_time = datetime.utcnow() + timedelta(hours=1)
        end_time = start_time + timedelta(hours=2)
        
        availability_response = client.post(
            "/api/v1/availability",
            json={
                "calendar_ids": ["https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA"],
                "time_min": start_time.isoformat(),
                "time_max": end_time.isoformat()
            },
            params={"provider": "calendly"}
        )
        
        assert availability_response.status_code == status.HTTP_200_OK
        availability_data = availability_response.json()
        assert len(availability_data) == 1
        assert availability_data[0]["calendar_id"] == "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA"
        
        # Test booking creation
        booking_start = start_time + timedelta(minutes=30)
        booking_end = booking_start + timedelta(minutes=30)
        
        booking_response = client.post(
            "/api/v1/book",
            json={
                "provider": "calendly",
                "calendar_id": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
                "summary": "Test Calendly Meeting",
                "start_at": booking_start.isoformat(),
                "end_at": booking_end.isoformat()
            }
        )
        
        assert booking_response.status_code == status.HTTP_200_OK
        booking_data = booking_response.json()
        assert booking_data["provider"] == "calendly"
        assert booking_data["external_id"] == "https://calendly.com/d/abc123/30-minute-meeting"
        assert booking_data["provider_event_link"] == "https://calendly.com/d/abc123/30-minute-meeting"

    @pytest.mark.asyncio
    @patch('packages.calendar_core.get_provider')
    async def test_propose_slot_calendly(
        self,
        mock_get_provider,
        mock_calendly_provider
    ):
        """Test proposeSlot functionality with Calendly."""
        mock_get_provider.return_value = mock_calendly_provider
        
        # This would be called by the voice agent
        from packages.calendar_core import get_provider
        
        provider = get_provider("test-firm", "calendly")
        
        # Check availability (propose slot)
        start_time = datetime.utcnow() + timedelta(hours=1)
        end_time = start_time + timedelta(hours=2)
        
        availability = await provider.get_availability(
            calendar_ids=["https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA"],
            time_min=start_time,
            time_max=end_time
        )
        
        assert len(availability) == 1
        assert availability[0]["calendar_id"] == "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA"
        assert availability[0]["busy"] == []

    @pytest.mark.asyncio
    @patch('packages.calendar_core.get_provider')
    async def test_confirm_slot_calendly(
        self,
        mock_get_provider,
        mock_calendly_provider
    ):
        """Test confirmSlot functionality with Calendly."""
        mock_get_provider.return_value = mock_calendly_provider
        
        # This would be called by the voice agent
        from packages.calendar_core import get_provider
        
        provider = get_provider("test-firm", "calendly")
        
        # Create event (confirm slot)
        event_data = {
            "summary": "Voice Consultation",
            "start": "2023-12-01T10:00:00Z",
            "end": "2023-12-01T10:30:00Z"
        }
        
        result = await provider.create_event(
            "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
            event_data
        )
        
        assert result["id"] == "https://calendly.com/d/abc123/30-minute-meeting"
        assert result["htmlLink"] == "https://calendly.com/d/abc123/30-minute-meeting"
        assert result["provider"] == "calendly"
        assert "calendly_link" in result

    @pytest.mark.asyncio
    @patch('packages.calendar_core.get_provider')
    async def test_calendly_vs_google_provider_compatibility(
        self,
        mock_get_provider
    ):
        """Test that both Google and Calendly providers work with the same interface."""
        # Mock Google provider
        mock_google_provider = AsyncMock()
        mock_google_provider.create_event.return_value = {
            "id": "google-event-123",
            "htmlLink": "https://calendar.google.com/event/123",
            "summary": "Google Meeting",
            "start": "2023-12-01T10:00:00Z",
            "end": "2023-12-01T10:30:00Z"
        }
        
        # Mock Calendly provider
        mock_calendly_provider = AsyncMock()
        mock_calendly_provider.create_event.return_value = {
            "id": "https://calendly.com/d/abc123/30-minute-meeting",
            "htmlLink": "https://calendly.com/d/abc123/30-minute-meeting",
            "summary": "Calendly Meeting",
            "start": "2023-12-01T10:00:00Z",
            "end": "2023-12-01T10:30:00Z",
            "provider": "calendly"
        }
        
        # Test both providers with the same interface
        def mock_provider_factory(firm_id, provider_name):
            if provider_name == "google":
                return mock_google_provider
            elif provider_name == "calendly":
                return mock_calendly_provider
            else:
                raise ValueError(f"Unknown provider: {provider_name}")
        
        mock_get_provider.side_effect = mock_provider_factory
        
        from packages.calendar_core import get_provider
        
        # Test Google provider
        google_provider = get_provider("test-firm", "google")
        google_result = await google_provider.create_event("primary", {
            "summary": "Test Meeting",
            "start": "2023-12-01T10:00:00Z",
            "end": "2023-12-01T10:30:00Z"
        })
        
        assert google_result["id"] == "google-event-123"
        assert "calendar.google.com" in google_result["htmlLink"]
        
        # Test Calendly provider
        calendly_provider = get_provider("test-firm", "calendly")
        calendly_result = await calendly_provider.create_event(
            "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
            {
                "summary": "Test Meeting",
                "start": "2023-12-01T10:00:00Z",
                "end": "2023-12-01T10:30:00Z"
            }
        )
        
        assert calendly_result["id"] == "https://calendly.com/d/abc123/30-minute-meeting"
        assert "calendly.com" in calendly_result["htmlLink"]
        assert calendly_result["provider"] == "calendly"
