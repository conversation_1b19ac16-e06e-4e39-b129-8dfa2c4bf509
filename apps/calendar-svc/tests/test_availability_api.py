"""
Tests for unified availability API endpoints.
"""
import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch
from uuid import uuid4

from fastapi.testclient import TestClient
from fastapi import status

from app.main import app
from app.schemas.calendar import AvailabilitySlot


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def mock_user():
    """Mock authenticated user."""
    return {
        "user_id": str(uuid4()),
        "email": "<EMAIL>",
        "scopes": ["calendar_booking"]
    }


@pytest.fixture
def availability_request():
    """Sample availability request."""
    return {
        "from": "2023-12-01T09:00:00Z",
        "to": "2023-12-01T17:00:00Z",
        "time_zone": "UTC"
    }


@pytest.fixture
def mock_availability_slots():
    """Mock availability slots."""
    return [
        AvailabilitySlot(
            start=datetime(2023, 12, 1, 9, 0),
            end=datetime(2023, 12, 1, 10, 0),
            provider="google"
        ),
        AvailabilitySlot(
            start=datetime(2023, 12, 1, 11, 0),
            end=datetime(2023, 12, 1, 12, 0),
            provider="calendly"
        ),
        AvailabilitySlot(
            start=datetime(2023, 12, 1, 14, 0),
            end=datetime(2023, 12, 1, 15, 30),
            provider="google,calendly"
        )
    ]


class TestUnifiedAvailabilityEndpoint:
    """Test unified availability endpoint."""
    
    @patch('app.api.v1.endpoints.availability.get_aggregated_availability')
    @patch('app.api.deps.get_current_user')
    def test_get_unified_availability_success(
        self, 
        mock_get_user, 
        mock_get_availability,
        client,
        mock_user,
        availability_request,
        mock_availability_slots
    ):
        """Test successful unified availability request."""
        # Mock authentication
        mock_get_user.return_value = mock_user
        
        # Mock availability aggregation
        mock_get_availability.return_value = mock_availability_slots
        
        # Make request
        response = client.post(
            "/api/v1/availability/unified",
            json=availability_request
        )
        
        # Verify response
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 3
        
        # Check first slot
        slot = data[0]
        assert slot["start"] == "2023-12-01T09:00:00"
        assert slot["end"] == "2023-12-01T10:00:00"
        assert slot["provider"] == "google"
        assert slot["duration_minutes"] == 60
        
        # Check merged slot
        merged_slot = data[2]
        assert merged_slot["provider"] == "google,calendly"
        assert merged_slot["duration_minutes"] == 90
    
    @patch('app.api.deps.get_current_user')
    def test_get_unified_availability_unauthorized(
        self, 
        mock_get_user,
        client,
        availability_request
    ):
        """Test unauthorized access."""
        # Mock no user
        mock_get_user.return_value = {}
        
        # Make request
        response = client.post(
            "/api/v1/availability/unified",
            json=availability_request
        )
        
        # Should return 401
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    @patch('app.api.v1.endpoints.availability.get_aggregated_availability')
    @patch('app.api.deps.get_current_user')
    def test_get_unified_availability_server_error(
        self, 
        mock_get_user, 
        mock_get_availability,
        client,
        mock_user,
        availability_request
    ):
        """Test server error handling."""
        # Mock authentication
        mock_get_user.return_value = mock_user
        
        # Mock availability aggregation error
        mock_get_availability.side_effect = Exception("Database error")
        
        # Make request
        response = client.post(
            "/api/v1/availability/unified",
            json=availability_request
        )
        
        # Should return 500
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Failed to fetch unified availability" in response.json()["detail"]
    
    def test_get_unified_availability_invalid_request(self, client):
        """Test invalid request data."""
        invalid_request = {
            "from": "invalid-date",
            "to": "2023-12-01T17:00:00Z"
        }
        
        response = client.post(
            "/api/v1/availability/unified",
            json=invalid_request
        )
        
        # Should return 422 for validation error
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_get_unified_availability_invalid_time_range(self, client):
        """Test invalid time range (end before start)."""
        invalid_request = {
            "from": "2023-12-01T17:00:00Z",
            "to": "2023-12-01T09:00:00Z"
        }
        
        response = client.post(
            "/api/v1/availability/unified",
            json=invalid_request
        )
        
        # Should return 422 for validation error
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @patch('app.api.v1.endpoints.availability.get_aggregated_availability')
    @patch('app.api.deps.get_current_user')
    def test_get_unified_availability_empty_result(
        self, 
        mock_get_user, 
        mock_get_availability,
        client,
        mock_user,
        availability_request
    ):
        """Test empty availability result."""
        # Mock authentication
        mock_get_user.return_value = mock_user
        
        # Mock empty availability
        mock_get_availability.return_value = []
        
        # Make request
        response = client.post(
            "/api/v1/availability/unified",
            json=availability_request
        )
        
        # Verify response
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 0


class TestAvailabilityHealthCheck:
    """Test availability health check endpoint."""
    
    def test_health_check(self, client):
        """Test health check endpoint."""
        response = client.get("/api/v1/availability/health")
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "unified-availability"


class TestCacheIntegration:
    """Test cache integration for availability."""
    
    @patch('app.utils.availability.cache_get_or_set')
    @patch('app.api.v1.endpoints.availability.get_aggregated_availability')
    @patch('app.api.deps.get_current_user')
    def test_cache_hit_scenario(
        self, 
        mock_get_user, 
        mock_get_availability,
        mock_cache,
        client,
        mock_user,
        availability_request,
        mock_availability_slots
    ):
        """Test cache hit scenario."""
        # Mock authentication
        mock_get_user.return_value = mock_user
        
        # Mock cache hit (return cached data without calling aggregation)
        mock_cache.return_value = mock_availability_slots
        mock_get_availability.return_value = mock_availability_slots
        
        # Make first request
        response1 = client.post(
            "/api/v1/availability/unified",
            json=availability_request
        )
        
        # Make second request
        response2 = client.post(
            "/api/v1/availability/unified",
            json=availability_request
        )
        
        # Both should succeed
        assert response1.status_code == status.HTTP_200_OK
        assert response2.status_code == status.HTTP_200_OK
        
        # Verify cache was used
        assert mock_cache.called
    
    @patch('app.utils.availability.cache_get_or_set')
    @patch('app.api.v1.endpoints.availability.get_aggregated_availability')
    @patch('app.api.deps.get_current_user')
    def test_cache_miss_scenario(
        self, 
        mock_get_user, 
        mock_get_availability,
        mock_cache,
        client,
        mock_user,
        availability_request,
        mock_availability_slots
    ):
        """Test cache miss scenario."""
        # Mock authentication
        mock_get_user.return_value = mock_user
        
        # Mock cache miss (call the actual function)
        async def cache_miss_func(key, func, ttl):
            return await func()
        
        mock_cache.side_effect = cache_miss_func
        mock_get_availability.return_value = mock_availability_slots
        
        # Make request
        response = client.post(
            "/api/v1/availability/unified",
            json=availability_request
        )
        
        # Should succeed
        assert response.status_code == status.HTTP_200_OK
        
        # Verify cache function was called
        assert mock_cache.called
