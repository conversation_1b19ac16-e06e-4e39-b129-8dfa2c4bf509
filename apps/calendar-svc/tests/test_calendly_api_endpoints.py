"""
Tests for new Calendly API endpoints (event types, scheduling links, availability).
"""
import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from fastapi import status

from app.main import app
from app.db.models.calendar_connection import CalendarConnection


@pytest.fixture
def client():
    """Create a test client."""
    return TestClient(app)


@pytest.fixture
def mock_calendly_connection():
    """Mock Calendly connection."""
    connection = MagicMock(spec=CalendarConnection)
    connection.id = "test-connection-id"
    connection.user_id = "test-user-123"
    connection.provider = "calendly"
    connection.is_active = True
    connection.access_token = "test-access-token"
    connection.refresh_token = "test-refresh-token"
    return connection


@pytest.fixture
def mock_event_types():
    """Mock event types data."""
    return [
        {
            "uri": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
            "name": "30 Minute Meeting",
            "description": "A brief 30-minute consultation",
            "duration": 30,
            "scheduling_url": "https://calendly.com/user/30min",
            "active": True,
            "kind": "solo",
            "pooling_type": None,
            "type": "StandardEventType",
            "color": "#0069ff",
            "created_at": "2023-01-01T00:00:00.000000Z",
            "updated_at": "2023-01-01T00:00:00.000000Z",
            "internal_note": None
        }
    ]


@pytest.fixture
def mock_scheduling_link():
    """Mock scheduling link data."""
    return {
        "booking_url": "https://calendly.com/d/abc123/30-minute-meeting",
        "owner": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
        "owner_type": "EventType",
        "created_at": "2023-01-01T00:00:00.000000Z"
    }


class TestCalendlyAPIEndpoints:
    """Test cases for new Calendly API endpoints."""

    @pytest.mark.asyncio
    @patch('app.api.v1.endpoints.calendly.get_current_user')
    @patch('app.api.v1.endpoints.calendly.get_repository')
    @patch('packages.calendar_core.get_provider')
    async def test_get_event_types_success(
        self, 
        mock_get_provider, 
        mock_get_repository, 
        mock_get_current_user,
        client,
        mock_calendly_connection,
        mock_event_types
    ):
        """Test successful event types retrieval."""
        # Mock dependencies
        mock_get_current_user.return_value = MagicMock(id="test-user-123")
        mock_repo = AsyncMock()
        mock_repo.get_connection_by_user_and_provider = AsyncMock(return_value=mock_calendly_connection)
        mock_get_repository.return_value = lambda: mock_repo
        
        # Mock provider
        mock_provider = AsyncMock()
        mock_provider.get_event_types = AsyncMock(return_value=mock_event_types)
        mock_get_provider.return_value = mock_provider
        
        # Make request
        response = client.get("/api/v1/calendly/event-types")
        
        # Assertions
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 1
        assert data[0]["name"] == "30 Minute Meeting"
        assert data[0]["duration"] == 30

    @pytest.mark.asyncio
    @patch('app.api.v1.endpoints.calendly.get_current_user')
    @patch('app.api.v1.endpoints.calendly.get_repository')
    async def test_get_event_types_no_connection(
        self, 
        mock_get_repository, 
        mock_get_current_user,
        client
    ):
        """Test event types retrieval with no Calendly connection."""
        # Mock dependencies
        mock_get_current_user.return_value = MagicMock(id="test-user-123")
        mock_repo = AsyncMock()
        mock_repo.get_connection_by_user_and_provider = AsyncMock(return_value=None)
        mock_get_repository.return_value = lambda: mock_repo
        
        # Make request
        response = client.get("/api/v1/calendly/event-types")
        
        # Assertions
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "No active Calendly connection found" in response.json()["detail"]

    @pytest.mark.asyncio
    @patch('app.api.v1.endpoints.calendly.get_current_user')
    @patch('app.api.v1.endpoints.calendly.get_repository')
    @patch('packages.calendar_core.get_provider')
    async def test_create_scheduling_link_success(
        self, 
        mock_get_provider, 
        mock_get_repository, 
        mock_get_current_user,
        client,
        mock_calendly_connection,
        mock_scheduling_link
    ):
        """Test successful scheduling link creation."""
        # Mock dependencies
        mock_get_current_user.return_value = MagicMock(id="test-user-123")
        mock_repo = AsyncMock()
        mock_repo.get_connection_by_user_and_provider = AsyncMock(return_value=mock_calendly_connection)
        mock_get_repository.return_value = lambda: mock_repo
        
        # Mock provider
        mock_provider = AsyncMock()
        mock_provider.create_scheduling_link = AsyncMock(return_value=mock_scheduling_link)
        mock_get_provider.return_value = mock_provider
        
        # Request data
        request_data = {
            "event_type_id": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
            "max_events": 1,
            "start_time": "2023-12-01T09:00:00Z",
            "end_time": "2023-12-31T17:00:00Z"
        }
        
        # Make request
        response = client.post("/api/v1/calendly/scheduling-links", json=request_data)
        
        # Assertions
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["booking_url"] == "https://calendly.com/d/abc123/30-minute-meeting"
        assert data["owner_type"] == "EventType"

    @pytest.mark.asyncio
    @patch('app.api.v1.endpoints.calendly.get_current_user')
    @patch('app.api.v1.endpoints.calendly.get_repository')
    @patch('packages.calendar_core.get_provider')
    async def test_get_availability_success(
        self, 
        mock_get_provider, 
        mock_get_repository, 
        mock_get_current_user,
        client,
        mock_calendly_connection
    ):
        """Test successful availability retrieval."""
        # Mock dependencies
        mock_get_current_user.return_value = MagicMock(id="test-user-123")
        mock_repo = AsyncMock()
        mock_repo.get_connection_by_user_and_provider = AsyncMock(return_value=mock_calendly_connection)
        mock_get_repository.return_value = lambda: mock_repo
        
        # Mock provider
        mock_provider = AsyncMock()
        mock_provider.get_event_types = AsyncMock(return_value=[
            {"uri": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA"}
        ])
        mock_provider.get_availability = AsyncMock(return_value=[
            {
                "calendar_id": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
                "busy": []
            }
        ])
        mock_get_provider.return_value = mock_provider
        
        # Make request
        start_time = datetime.utcnow().isoformat()
        end_time = (datetime.utcnow() + timedelta(days=1)).isoformat()
        
        response = client.get(
            f"/api/v1/calendly/availability?start_time={start_time}&end_time={end_time}"
        )
        
        # Assertions
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 1
        assert data[0]["calendar_id"] == "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA"

    @pytest.mark.asyncio
    @patch('app.api.v1.endpoints.calendly.get_current_user')
    @patch('app.api.v1.endpoints.calendly.get_repository')
    @patch('packages.calendar_core.get_provider')
    async def test_provider_error_handling(
        self, 
        mock_get_provider, 
        mock_get_repository, 
        mock_get_current_user,
        client,
        mock_calendly_connection
    ):
        """Test error handling when provider fails."""
        # Mock dependencies
        mock_get_current_user.return_value = MagicMock(id="test-user-123")
        mock_repo = AsyncMock()
        mock_repo.get_connection_by_user_and_provider = AsyncMock(return_value=mock_calendly_connection)
        mock_get_repository.return_value = lambda: mock_repo
        
        # Mock provider to raise exception
        mock_provider = AsyncMock()
        mock_provider.get_event_types = AsyncMock(side_effect=Exception("Calendly API Error"))
        mock_get_provider.return_value = mock_provider
        
        # Make request
        response = client.get("/api/v1/calendly/event-types")
        
        # Assertions
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Failed to retrieve event types" in response.json()["detail"]

    def test_invalid_request_data(self, client):
        """Test handling of invalid request data."""
        # Invalid request data (missing required fields)
        request_data = {
            "max_events": 1
            # Missing event_type_id
        }
        
        # Make request
        response = client.post("/api/v1/calendly/scheduling-links", json=request_data)
        
        # Assertions
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
