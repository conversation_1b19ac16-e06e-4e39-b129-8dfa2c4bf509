"""Tests for booking helpers with real provider integration."""
import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch, MagicMock
from uuid import uuid4

import respx
import httpx
from sqlalchemy.ext.asyncio import AsyncSession

from ..app.booking_helpers import (
    proposeSlot,
    confirmSlot,
    get_aggregated_availability,
    ProposedSlot,
    SlotUnavailableError,
    ProviderError
)
from packages.shared import models as shared_models


@pytest.fixture
def mock_db_session():
    """Mock database session."""
    session = AsyncMock(spec=AsyncSession)
    session.add = MagicMock()
    session.commit = AsyncMock()
    session.refresh = AsyncMock()
    return session


@pytest.fixture
def sample_proposed_slot():
    """Sample proposed slot for testing."""
    start_time = datetime.utcnow() + timedelta(hours=1)
    end_time = start_time + timedelta(minutes=30)
    return ProposedSlot(
        start_at=start_time,
        end_at=end_time,
        provider="google",
        calendar_id="primary",
        duration_minutes=30
    )


@pytest.fixture
def mock_google_availability():
    """Mock Google Calendar availability response."""
    return [
        {
            "calendar_id": "primary",
            "busy": [
                {
                    "start": "2024-01-15T10:00:00Z",
                    "end": "2024-01-15T11:00:00Z"
                }
            ]
        }
    ]


@pytest.fixture
def mock_calendly_availability():
    """Mock Calendly availability response."""
    return [
        {
            "calendar_id": "https://api.calendly.com/event_types/123",
            "busy": []
        }
    ]


class TestGetAggregatedAvailability:
    """Test availability aggregation across providers."""

    @pytest.mark.asyncio
    async def test_aggregates_multiple_providers(self, mock_google_availability, mock_calendly_availability):
        """Test that availability is aggregated from multiple providers."""
        tenant_id = "test-tenant"
        time_min = datetime(2024, 1, 15, 8, 0, 0)
        time_max = datetime(2024, 1, 15, 18, 0, 0)

        with patch('apps.calendar_svc.app.booking_helpers.get_provider') as mock_get_provider:
            # Mock Google provider
            google_provider = AsyncMock()
            google_provider.list_calendars.return_value = [{"id": "primary", "primary": True}]
            google_provider.get_availability.return_value = mock_google_availability

            # Mock Calendly provider
            calendly_provider = AsyncMock()
            calendly_provider.list_calendars.return_value = [
                {"id": "https://api.calendly.com/event_types/123", "primary": True}
            ]
            calendly_provider.get_availability.return_value = mock_calendly_availability

            # Configure mock to return different providers
            def get_provider_side_effect(tenant, provider_name):
                if provider_name == "google":
                    return google_provider
                elif provider_name == "calendly":
                    return calendly_provider
                else:
                    raise ValueError(f"Unknown provider: {provider_name}")

            mock_get_provider.side_effect = get_provider_side_effect

            # Test aggregation
            result = await get_aggregated_availability(tenant_id, time_min, time_max)

            # Verify both providers were called
            assert "google" in result
            assert "calendly" in result
            assert result["google"]["availability"] == mock_google_availability
            assert result["calendly"]["availability"] == mock_calendly_availability

    @pytest.mark.asyncio
    async def test_handles_provider_errors_gracefully(self):
        """Test that provider errors don't break aggregation."""
        tenant_id = "test-tenant"
        time_min = datetime(2024, 1, 15, 8, 0, 0)
        time_max = datetime(2024, 1, 15, 18, 0, 0)

        with patch('apps.calendar_svc.app.booking_helpers.get_provider') as mock_get_provider:
            # Mock Google provider to work
            google_provider = AsyncMock()
            google_provider.list_calendars.return_value = [{"id": "primary", "primary": True}]
            google_provider.get_availability.return_value = [{"calendar_id": "primary", "busy": []}]

            # Mock Calendly provider to fail
            calendly_provider = AsyncMock()
            calendly_provider.list_calendars.side_effect = Exception("Auth failed")

            def get_provider_side_effect(tenant, provider_name):
                if provider_name == "google":
                    return google_provider
                elif provider_name == "calendly":
                    return calendly_provider

            mock_get_provider.side_effect = get_provider_side_effect

            result = await get_aggregated_availability(tenant_id, time_min, time_max)

            # Google should work, Calendly should have error
            assert "google" in result
            assert "calendars" in result["google"]
            assert "calendly" in result
            assert "error" in result["calendly"]


class TestProposeSlot:
    """Test slot proposal functionality."""

    @pytest.mark.asyncio
    async def test_proposes_slots_from_multiple_providers(self):
        """Test that slots are proposed from multiple providers."""
        tenant_id = "test-tenant"
        
        with patch('apps.calendar_svc.app.booking_helpers.get_aggregated_availability') as mock_get_availability:
            # Mock availability data
            mock_get_availability.return_value = {
                "google": {
                    "calendars": ["primary"],
                    "availability": [
                        {
                            "calendar_id": "primary",
                            "busy": []  # No busy times = all available
                        }
                    ]
                },
                "calendly": {
                    "calendars": ["event_type_123"],
                    "availability": [
                        {
                            "calendar_id": "event_type_123",
                            "busy": []
                        }
                    ]
                }
            }

            with patch('apps.calendar_svc.app.booking_helpers.get_available_slots') as mock_get_slots:
                # Mock available slots
                now = datetime.utcnow()
                mock_get_slots.return_value = [
                    {"start": now + timedelta(hours=1), "end": now + timedelta(hours=1, minutes=30)},
                    {"start": now + timedelta(hours=2), "end": now + timedelta(hours=2, minutes=30)},
                ]

                slots = await proposeSlot(tenant_id, max_slots=3)

                assert len(slots) > 0
                assert all(isinstance(slot, ProposedSlot) for slot in slots)
                # Should have slots from both providers
                providers = {slot.provider for slot in slots}
                assert len(providers) >= 1  # At least one provider

    @pytest.mark.asyncio
    async def test_respects_max_slots_limit(self):
        """Test that max_slots parameter is respected."""
        tenant_id = "test-tenant"
        max_slots = 2

        with patch('apps.calendar_svc.app.booking_helpers.get_aggregated_availability') as mock_get_availability:
            mock_get_availability.return_value = {
                "google": {
                    "calendars": ["primary"],
                    "availability": [{"calendar_id": "primary", "busy": []}]
                }
            }

            with patch('apps.calendar_svc.app.booking_helpers.get_available_slots') as mock_get_slots:
                now = datetime.utcnow()
                # Return more slots than max_slots
                mock_get_slots.return_value = [
                    {"start": now + timedelta(hours=i), "end": now + timedelta(hours=i, minutes=30)}
                    for i in range(1, 6)  # 5 slots
                ]

                slots = await proposeSlot(tenant_id, max_slots=max_slots)

                assert len(slots) <= max_slots

    @pytest.mark.asyncio
    async def test_handles_no_availability(self):
        """Test behavior when no availability is found."""
        tenant_id = "test-tenant"

        with patch('apps.calendar_svc.app.booking_helpers.get_aggregated_availability') as mock_get_availability:
            mock_get_availability.return_value = {}

            slots = await proposeSlot(tenant_id)

            assert len(slots) == 0


class TestConfirmSlot:
    """Test slot confirmation functionality."""

    @pytest.mark.asyncio
    async def test_confirms_google_slot_successfully(self, sample_proposed_slot, mock_db_session):
        """Test successful Google Calendar slot confirmation."""
        tenant_id = "test-tenant"
        summary = "Test Meeting"

        with patch('apps.calendar_svc.app.booking_helpers.get_provider') as mock_get_provider:
            # Mock Google provider
            google_provider = AsyncMock()
            google_provider.create_event.return_value = {
                "id": "google-event-123",
                "htmlLink": "https://calendar.google.com/event/123",
                "summary": summary
            }
            mock_get_provider.return_value = google_provider

            # Mock booking creation
            mock_booking = MagicMock()
            mock_booking.id = uuid4()
            mock_booking.booked_at = datetime.utcnow()
            mock_db_session.refresh.side_effect = lambda obj: setattr(obj, 'id', mock_booking.id)

            result = await confirmSlot(
                tenant_id=tenant_id,
                proposed_slot=sample_proposed_slot,
                summary=summary,
                db=mock_db_session
            )

            # Verify result
            assert result["external_id"] == "google-event-123"
            assert result["provider"] == "google"
            assert result["provider_event_link"] == "https://calendar.google.com/event/123"
            assert result["status"] == "confirmed"

            # Verify database operations
            mock_db_session.add.assert_called_once()
            mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_confirms_calendly_slot_successfully(self, mock_db_session):
        """Test successful Calendly slot confirmation."""
        tenant_id = "test-tenant"
        summary = "Test Meeting"
        
        # Create Calendly proposed slot
        start_time = datetime.utcnow() + timedelta(hours=1)
        end_time = start_time + timedelta(minutes=30)
        calendly_slot = ProposedSlot(
            start_at=start_time,
            end_at=end_time,
            provider="calendly",
            calendar_id="event_type_123",
            duration_minutes=30
        )

        with patch('apps.calendar_svc.app.booking_helpers.get_provider') as mock_get_provider:
            # Mock Calendly provider
            calendly_provider = AsyncMock()
            calendly_provider.create_event.return_value = {
                "id": "https://calendly.com/book/abc123",
                "calendly_link": "https://calendly.com/book/abc123",
                "summary": summary
            }
            mock_get_provider.return_value = calendly_provider

            # Mock booking creation
            mock_booking = MagicMock()
            mock_booking.id = uuid4()
            mock_booking.booked_at = datetime.utcnow()
            mock_db_session.refresh.side_effect = lambda obj: setattr(obj, 'id', mock_booking.id)

            result = await confirmSlot(
                tenant_id=tenant_id,
                proposed_slot=calendly_slot,
                summary=summary,
                db=mock_db_session
            )

            # Verify result
            assert result["external_id"] == "https://calendly.com/book/abc123"
            assert result["provider"] == "calendly"
            assert result["provider_event_link"] == "https://calendly.com/book/abc123"

    @pytest.mark.asyncio
    async def test_raises_slot_unavailable_for_past_slot(self, mock_db_session):
        """Test that SlotUnavailableError is raised for past slots."""
        tenant_id = "test-tenant"
        summary = "Test Meeting"
        
        # Create slot in the past
        past_time = datetime.utcnow() - timedelta(hours=1)
        past_slot = ProposedSlot(
            start_at=past_time,
            end_at=past_time + timedelta(minutes=30),
            provider="google",
            calendar_id="primary",
            duration_minutes=30
        )

        with pytest.raises(SlotUnavailableError):
            await confirmSlot(
                tenant_id=tenant_id,
                proposed_slot=past_slot,
                summary=summary,
                db=mock_db_session
            )

    @pytest.mark.asyncio
    async def test_retries_on_provider_error(self, sample_proposed_slot, mock_db_session):
        """Test that ProviderError triggers retry logic."""
        tenant_id = "test-tenant"
        summary = "Test Meeting"

        with patch('apps.calendar_svc.app.booking_helpers.get_provider') as mock_get_provider:
            # Mock provider to fail with 5xx error first, then succeed
            google_provider = AsyncMock()
            
            # Create a mock response with status_code
            mock_response = MagicMock()
            mock_response.status_code = 503
            
            # Create exception with response attribute
            server_error = Exception("Server error")
            server_error.response = mock_response
            
            google_provider.create_event.side_effect = [
                server_error,  # First call fails
                {  # Second call succeeds
                    "id": "google-event-123",
                    "htmlLink": "https://calendar.google.com/event/123"
                }
            ]
            mock_get_provider.return_value = google_provider

            # Mock booking creation
            mock_booking = MagicMock()
            mock_booking.id = uuid4()
            mock_booking.booked_at = datetime.utcnow()
            mock_db_session.refresh.side_effect = lambda obj: setattr(obj, 'id', mock_booking.id)

            result = await confirmSlot(
                tenant_id=tenant_id,
                proposed_slot=sample_proposed_slot,
                summary=summary,
                db=mock_db_session
            )

            # Should succeed after retry
            assert result["external_id"] == "google-event-123"
            # Verify provider was called twice (original + retry)
            assert google_provider.create_event.call_count == 2
