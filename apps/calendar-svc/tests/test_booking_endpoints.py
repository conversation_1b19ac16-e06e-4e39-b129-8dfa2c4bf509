"""Tests for booking API endpoints with real provider integration."""
import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch, MagicMock
from uuid import uuid4

import respx
import httpx
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.main import app
from app.booking_helpers import ProposedSlot
from packages.shared import models as shared_models


@pytest.fixture
def client():
    """Test client for the FastAPI app."""
    return TestClient(app)


@pytest.fixture
def mock_firm_id():
    """Mock firm ID for testing."""
    return "test-firm-123"


@pytest.fixture
def mock_auth_user():
    """Mock authenticated user."""
    return {"id": "test-firm-123", "email": "<EMAIL>"}


@pytest.fixture
def sample_booking_request():
    """Sample booking request data."""
    start_time = datetime.utcnow() + timedelta(hours=1)
    end_time = start_time + timedelta(minutes=30)
    
    return {
        "provider": "google",
        "calendar_id": "primary",
        "summary": "Test Meeting",
        "start_at": start_time.isoformat(),
        "end_at": end_time.isoformat()
    }


@pytest.fixture
def sample_propose_request():
    """Sample propose slots request data."""
    return {
        "preferred_start": (datetime.utcnow() + timedelta(hours=1)).isoformat(),
        "duration_minutes": 30,
        "max_slots": 3
    }


@pytest.fixture
def sample_confirm_request():
    """Sample confirm slot request data."""
    start_time = datetime.utcnow() + timedelta(hours=1)
    end_time = start_time + timedelta(minutes=30)
    
    return {
        "start_at": start_time.isoformat(),
        "end_at": end_time.isoformat(),
        "provider": "google",
        "calendar_id": "primary",
        "summary": "Test Meeting",
        "attendee_email": "<EMAIL>",
        "description": "Test meeting description"
    }


class TestProposeEndpoint:
    """Test the /propose endpoint."""

    def test_propose_slots_success(self, client, mock_auth_user, sample_propose_request):
        """Test successful slot proposal."""
        with patch('app.api.v1.endpoints.booking.get_firm_id') as mock_get_firm_id:
            mock_get_firm_id.return_value = mock_auth_user["id"]
            
            with patch('app.api.v1.endpoints.booking.proposeSlot') as mock_propose:
                # Mock proposed slots
                start_time = datetime.utcnow() + timedelta(hours=1)
                mock_slots = [
                    ProposedSlot(
                        start_at=start_time,
                        end_at=start_time + timedelta(minutes=30),
                        provider="google",
                        calendar_id="primary",
                        duration_minutes=30
                    ),
                    ProposedSlot(
                        start_at=start_time + timedelta(hours=1),
                        end_at=start_time + timedelta(hours=1, minutes=30),
                        provider="calendly",
                        calendar_id="event_type_123",
                        duration_minutes=30
                    )
                ]
                mock_propose.return_value = mock_slots

                response = client.post("/api/v1/propose", json=sample_propose_request)

                assert response.status_code == 200
                data = response.json()
                assert len(data) == 2
                assert data[0]["provider"] == "google"
                assert data[1]["provider"] == "calendly"

    def test_propose_slots_with_defaults(self, client, mock_auth_user):
        """Test slot proposal with default parameters."""
        with patch('app.api.v1.endpoints.booking.get_firm_id') as mock_get_firm_id:
            mock_get_firm_id.return_value = mock_auth_user["id"]
            
            with patch('app.api.v1.endpoints.booking.proposeSlot') as mock_propose:
                mock_propose.return_value = []

                response = client.post("/api/v1/propose", json={})

                assert response.status_code == 200
                # Verify default parameters were used
                mock_propose.assert_called_once_with(
                    tenant_id=mock_auth_user["id"],
                    preferred_start=None,
                    duration_minutes=30,
                    max_slots=3
                )

    def test_propose_slots_error_handling(self, client, mock_auth_user, sample_propose_request):
        """Test error handling in slot proposal."""
        with patch('app.api.v1.endpoints.booking.get_firm_id') as mock_get_firm_id:
            mock_get_firm_id.return_value = mock_auth_user["id"]
            
            with patch('app.api.v1.endpoints.booking.proposeSlot') as mock_propose:
                mock_propose.side_effect = Exception("Provider unavailable")

                response = client.post("/api/v1/propose", json=sample_propose_request)

                assert response.status_code == 500
                assert "Failed to propose slots" in response.json()["detail"]


class TestConfirmEndpoint:
    """Test the /confirm endpoint."""

    def test_confirm_slot_success(self, client, mock_auth_user, sample_confirm_request):
        """Test successful slot confirmation."""
        with patch('app.api.v1.endpoints.booking.get_firm_id') as mock_get_firm_id:
            mock_get_firm_id.return_value = mock_auth_user["id"]
            
            with patch('app.api.v1.endpoints.booking.get_async_db') as mock_get_db:
                mock_db = AsyncMock()
                mock_get_db.return_value = mock_db
                
                with patch('app.api.v1.endpoints.booking.confirmSlot') as mock_confirm:
                    booking_id = str(uuid4())
                    mock_confirm.return_value = {
                        "booking_id": booking_id,
                        "external_id": "google-event-123",
                        "provider": "google",
                        "provider_event_link": "https://calendar.google.com/event/123",
                        "start_at": sample_confirm_request["start_at"],
                        "end_at": sample_confirm_request["end_at"],
                        "status": "confirmed",
                        "booked_at": datetime.utcnow().isoformat()
                    }

                    response = client.post("/api/v1/confirm", json=sample_confirm_request)

                    assert response.status_code == 200
                    data = response.json()
                    assert data["id"] == booking_id
                    assert data["provider"] == "GOOGLE"
                    assert data["external_id"] == "google-event-123"

    def test_confirm_slot_unavailable(self, client, mock_auth_user, sample_confirm_request):
        """Test slot unavailable error handling."""
        with patch('app.api.v1.endpoints.booking.get_firm_id') as mock_get_firm_id:
            mock_get_firm_id.return_value = mock_auth_user["id"]
            
            with patch('app.api.v1.endpoints.booking.get_async_db') as mock_get_db:
                mock_db = AsyncMock()
                mock_get_db.return_value = mock_db
                
                with patch('app.api.v1.endpoints.booking.confirmSlot') as mock_confirm:
                    from app.booking_helpers import SlotUnavailableError
                    mock_confirm.side_effect = SlotUnavailableError("Slot is no longer available")

                    response = client.post("/api/v1/confirm", json=sample_confirm_request)

                    assert response.status_code == 409
                    assert "Slot is no longer available" in response.json()["detail"]

    def test_confirm_provider_error(self, client, mock_auth_user, sample_confirm_request):
        """Test provider error handling."""
        with patch('app.api.v1.endpoints.booking.get_firm_id') as mock_get_firm_id:
            mock_get_firm_id.return_value = mock_auth_user["id"]
            
            with patch('app.api.v1.endpoints.booking.get_async_db') as mock_get_db:
                mock_db = AsyncMock()
                mock_get_db.return_value = mock_db
                
                with patch('app.api.v1.endpoints.booking.confirmSlot') as mock_confirm:
                    from app.booking_helpers import ProviderError
                    mock_confirm.side_effect = ProviderError("Google Calendar API error")

                    response = client.post("/api/v1/confirm", json=sample_confirm_request)

                    assert response.status_code == 502
                    assert "Provider error" in response.json()["detail"]

    def test_confirm_rate_limiting(self, client, mock_auth_user, sample_confirm_request):
        """Test rate limiting on confirm endpoint."""
        with patch('app.api.v1.endpoints.booking.get_firm_id') as mock_get_firm_id:
            mock_get_firm_id.return_value = mock_auth_user["id"]
            
            with patch('app.api.v1.endpoints.booking.booking_limiter') as mock_limiter:
                from app.utils.rate_limiter import RateLimitExceeded
                mock_limiter.is_rate_limited.side_effect = RateLimitExceeded("Rate limit exceeded")

                response = client.post("/api/v1/confirm", json=sample_confirm_request)

                assert response.status_code == 429
                assert "Rate limit exceeded" in response.json()["detail"]


class TestLegacyBookEndpoint:
    """Test the legacy /book endpoint."""

    def test_legacy_book_success(self, client, mock_auth_user, sample_booking_request):
        """Test successful legacy booking."""
        with patch('app.api.v1.endpoints.booking.get_firm_id') as mock_get_firm_id:
            mock_get_firm_id.return_value = mock_auth_user["id"]
            
            with patch('app.api.v1.endpoints.booking.get_async_db') as mock_get_db:
                mock_db = AsyncMock()
                mock_get_db.return_value = mock_db
                
                with patch('app.api.v1.endpoints.booking.confirmSlot') as mock_confirm:
                    booking_id = str(uuid4())
                    mock_confirm.return_value = {
                        "booking_id": booking_id,
                        "external_id": "google-event-123",
                        "provider": "google",
                        "provider_event_link": "https://calendar.google.com/event/123",
                        "start_at": sample_booking_request["start_at"],
                        "end_at": sample_booking_request["end_at"],
                        "status": "confirmed",
                        "booked_at": datetime.utcnow().isoformat()
                    }

                    response = client.post("/api/v1/book", json=sample_booking_request)

                    assert response.status_code == 200
                    data = response.json()
                    assert data["id"] == booking_id
                    assert data["provider"] == "google"

    def test_legacy_book_maintains_compatibility(self, client, mock_auth_user, sample_booking_request):
        """Test that legacy endpoint maintains backward compatibility."""
        with patch('app.api.v1.endpoints.booking.get_firm_id') as mock_get_firm_id:
            mock_get_firm_id.return_value = mock_auth_user["id"]
            
            with patch('app.api.v1.endpoints.booking.get_async_db') as mock_get_db:
                mock_db = AsyncMock()
                mock_get_db.return_value = mock_db
                
                with patch('app.api.v1.endpoints.booking.confirmSlot') as mock_confirm:
                    booking_id = str(uuid4())
                    mock_confirm.return_value = {
                        "booking_id": booking_id,
                        "external_id": "google-event-123",
                        "provider": "google",
                        "provider_event_link": "https://calendar.google.com/event/123",
                        "start_at": sample_booking_request["start_at"],
                        "end_at": sample_booking_request["end_at"],
                        "status": "confirmed",
                        "booked_at": datetime.utcnow().isoformat()
                    }

                    response = client.post("/api/v1/book", json=sample_booking_request)

                    # Verify confirmSlot was called with correct ProposedSlot
                    assert mock_confirm.called
                    call_args = mock_confirm.call_args
                    proposed_slot = call_args[1]["proposed_slot"]
                    
                    assert proposed_slot.provider == "google"
                    assert proposed_slot.calendar_id == "primary"
                    assert proposed_slot.duration_minutes == 30


@respx.mock
class TestEndToEndBookingFlow:
    """End-to-end tests with real provider HTTP mocks."""

    def test_google_booking_flow(self, client, mock_auth_user):
        """Test complete Google Calendar booking flow."""
        # Mock Google Calendar API responses
        respx.post("https://www.googleapis.com/calendar/v3/freeBusy").mock(
            return_value=httpx.Response(200, json={
                "calendars": {
                    "primary": {
                        "busy": []
                    }
                }
            })
        )
        
        respx.post("https://www.googleapis.com/calendar/v3/calendars/primary/events").mock(
            return_value=httpx.Response(200, json={
                "id": "google-event-123",
                "htmlLink": "https://calendar.google.com/event/123",
                "summary": "Test Meeting"
            })
        )

        with patch('app.api.v1.endpoints.booking.get_firm_id') as mock_get_firm_id:
            mock_get_firm_id.return_value = mock_auth_user["id"]
            
            with patch('app.api.v1.endpoints.booking.get_async_db') as mock_get_db:
                mock_db = AsyncMock()
                mock_get_db.return_value = mock_db
                
                with patch('packages.calendar_core.adapters.google.get_access_token') as mock_auth:
                    mock_auth.return_value = "mock-google-token"

                    # Test propose slots
                    propose_request = {
                        "duration_minutes": 30,
                        "max_slots": 2
                    }
                    
                    propose_response = client.post("/api/v1/propose", json=propose_request)
                    assert propose_response.status_code == 200
                    
                    # Test confirm slot (using legacy endpoint for simplicity)
                    start_time = datetime.utcnow() + timedelta(hours=1)
                    end_time = start_time + timedelta(minutes=30)
                    
                    booking_request = {
                        "provider": "google",
                        "calendar_id": "primary",
                        "summary": "Test Meeting",
                        "start_at": start_time.isoformat(),
                        "end_at": end_time.isoformat()
                    }
                    
                    booking_response = client.post("/api/v1/book", json=booking_request)
                    assert booking_response.status_code == 200
                    
                    booking_data = booking_response.json()
                    assert booking_data["provider"] == "google"
                    assert booking_data["external_id"] == "google-event-123"

    def test_calendly_booking_flow(self, client, mock_auth_user):
        """Test complete Calendly booking flow."""
        # Mock Calendly API responses
        respx.get("https://api.calendly.com/event_types").mock(
            return_value=httpx.Response(200, json={
                "collection": [
                    {
                        "uri": "https://api.calendly.com/event_types/123",
                        "name": "30 Minute Meeting",
                        "active": True,
                        "duration": 30,
                        "scheduling_url": "https://calendly.com/user/30min"
                    }
                ]
            })
        )
        
        respx.post("https://api.calendly.com/scheduling_links").mock(
            return_value=httpx.Response(200, json={
                "resource": {
                    "booking_url": "https://calendly.com/book/abc123",
                    "owner": "https://api.calendly.com/event_types/123",
                    "owner_type": "EventType",
                    "created_at": "2024-01-15T10:00:00Z"
                }
            })
        )

        with patch('app.api.v1.endpoints.booking.get_firm_id') as mock_get_firm_id:
            mock_get_firm_id.return_value = mock_auth_user["id"]
            
            with patch('app.api.v1.endpoints.booking.get_async_db') as mock_get_db:
                mock_db = AsyncMock()
                mock_get_db.return_value = mock_db
                
                with patch('packages.calendar_core.adapters.calendly.get_access_token') as mock_auth:
                    mock_auth.return_value = "mock-calendly-token"

                    # Test Calendly booking
                    start_time = datetime.utcnow() + timedelta(hours=1)
                    end_time = start_time + timedelta(minutes=30)
                    
                    booking_request = {
                        "provider": "calendly",
                        "calendar_id": "https://api.calendly.com/event_types/123",
                        "summary": "Test Meeting",
                        "start_at": start_time.isoformat(),
                        "end_at": end_time.isoformat()
                    }
                    
                    booking_response = client.post("/api/v1/book", json=booking_request)
                    assert booking_response.status_code == 200
                    
                    booking_data = booking_response.json()
                    assert booking_data["provider"] == "calendly"
                    assert "calendly.com" in booking_data["external_id"]
