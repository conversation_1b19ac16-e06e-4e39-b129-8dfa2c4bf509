"""
Tests for CalendlyProvider implementation.
"""
import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
import httpx

from packages.calendar_core.adapters.calendly import CalendlyProvider


@pytest.fixture
def calendly_provider():
    """Create a CalendlyProvider instance for testing."""
    return CalendlyProvider(
        firm_id="test-firm-123",
        provider_name="calendly",
        capability={"read": True, "write": False}
    )


@pytest.fixture
def mock_event_types():
    """Mock event types response from Calendly API."""
    return {
        "collection": [
            {
                "uri": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
                "name": "30 Minute Meeting",
                "description_plain": "A brief 30-minute consultation",
                "duration": 30,
                "scheduling_url": "https://calendly.com/user/30min",
                "active": True,
                "kind": "solo",
                "pooling_type": None,
                "type": "StandardEventType",
                "color": "#0069ff",
                "created_at": "2023-01-01T00:00:00.000000Z",
                "updated_at": "2023-01-01T00:00:00.000000Z",
                "internal_note": None
            },
            {
                "uri": "https://api.calendly.com/event_types/BBBBBBBBBBBBBBBB",
                "name": "60 Minute Consultation",
                "description_plain": "Extended consultation session",
                "duration": 60,
                "scheduling_url": "https://calendly.com/user/60min",
                "active": True,
                "kind": "solo",
                "pooling_type": None,
                "type": "StandardEventType",
                "color": "#ff6900",
                "created_at": "2023-01-01T00:00:00.000000Z",
                "updated_at": "2023-01-01T00:00:00.000000Z",
                "internal_note": "Extended session"
            }
        ]
    }


@pytest.fixture
def mock_scheduling_link():
    """Mock scheduling link response from Calendly API."""
    return {
        "resource": {
            "booking_url": "https://calendly.com/d/abc123/30-minute-meeting",
            "owner": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
            "owner_type": "EventType",
            "created_at": "2023-01-01T00:00:00.000000Z"
        }
    }


class TestCalendlyProvider:
    """Test cases for CalendlyProvider."""

    @pytest.mark.asyncio
    @patch('packages.calendar_core.adapters.calendly.get_access_token')
    async def test_get_token(self, mock_get_access_token, calendly_provider):
        """Test token retrieval."""
        mock_get_access_token.return_value = "test-access-token"
        
        token = await calendly_provider._get_token()
        
        assert token == "test-access-token"
        mock_get_access_token.assert_called_once_with("test-firm-123", "calendly")

    @pytest.mark.asyncio
    @patch('packages.calendar_core.adapters.calendly.get_access_token')
    async def test_make_request_success(self, mock_get_access_token, calendly_provider):
        """Test successful API request."""
        mock_get_access_token.return_value = "test-access-token"
        
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_response.raise_for_status.return_value = None
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
            
            result = await calendly_provider._make_request("GET", "/test")
            
            assert result == {"success": True}

    @pytest.mark.asyncio
    @patch('packages.calendar_core.adapters.calendly.get_access_token')
    async def test_make_request_rate_limit(self, mock_get_access_token, calendly_provider):
        """Test rate limit handling."""
        mock_get_access_token.return_value = "test-access-token"
        
        mock_response = MagicMock()
        mock_response.status_code = 429
        mock_response.request = MagicMock()
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
            
            with pytest.raises(httpx.HTTPStatusError):
                await calendly_provider._make_request("GET", "/test")

    @pytest.mark.asyncio
    async def test_get_event_types(self, calendly_provider, mock_event_types):
        """Test getting event types."""
        with patch.object(calendly_provider, '_make_request', return_value=mock_event_types):
            event_types = await calendly_provider.get_event_types()
            
            assert len(event_types) == 2
            assert event_types[0]["name"] == "30 Minute Meeting"
            assert event_types[0]["duration"] == 30
            assert event_types[1]["name"] == "60 Minute Consultation"
            assert event_types[1]["duration"] == 60

    @pytest.mark.asyncio
    async def test_create_scheduling_link(self, calendly_provider, mock_scheduling_link):
        """Test creating a scheduling link."""
        with patch.object(calendly_provider, '_make_request', return_value=mock_scheduling_link):
            link_data = await calendly_provider.create_scheduling_link(
                event_type_id="AAAAAAAAAAAAAAAA",
                max_events=1
            )
            
            assert link_data["booking_url"] == "https://calendly.com/d/abc123/30-minute-meeting"
            assert link_data["owner_type"] == "EventType"

    @pytest.mark.asyncio
    async def test_list_calendars(self, calendly_provider, mock_event_types):
        """Test listing calendars (event types)."""
        with patch.object(calendly_provider, 'get_event_types', return_value=mock_event_types["collection"]):
            calendars = await calendly_provider.list_calendars()
            
            assert len(calendars) == 2
            assert calendars[0]["name"] == "30 Minute Meeting"
            assert calendars[0]["primary"] is True
            assert calendars[1]["name"] == "60 Minute Consultation"

    @pytest.mark.asyncio
    async def test_create_event(self, calendly_provider, mock_scheduling_link):
        """Test creating an event (scheduling link)."""
        with patch.object(calendly_provider, 'create_scheduling_link', return_value=mock_scheduling_link["resource"]):
            event_data = {
                "summary": "Test Meeting",
                "start": "2023-12-01T10:00:00Z",
                "end": "2023-12-01T10:30:00Z"
            }
            
            result = await calendly_provider.create_event("test-calendar-id", event_data)
            
            assert result["id"] == "https://calendly.com/d/abc123/30-minute-meeting"
            assert result["htmlLink"] == "https://calendly.com/d/abc123/30-minute-meeting"
            assert result["summary"] == "Test Meeting"
            assert result["provider"] == "calendly"

    @pytest.mark.asyncio
    async def test_get_availability(self, calendly_provider):
        """Test getting availability."""
        mock_user_response = {
            "resource": {
                "uri": "https://api.calendly.com/users/UUUUUUUUUUUUUUUU"
            }
        }
        
        mock_availability_response = {
            "collection": []
        }
        
        with patch.object(calendly_provider, '_make_request') as mock_request:
            mock_request.side_effect = [mock_user_response, mock_availability_response]
            
            start_time = datetime.utcnow()
            end_time = start_time + timedelta(days=1)
            
            availability = await calendly_provider.get_availability(
                calendar_ids=["test-calendar-id"],
                time_min=start_time,
                time_max=end_time
            )
            
            assert len(availability) == 1
            assert availability[0]["calendar_id"] == "test-calendar-id"
            assert availability[0]["busy"] == []

    @pytest.mark.asyncio
    async def test_get_availability_error_handling(self, calendly_provider):
        """Test availability error handling."""
        with patch.object(calendly_provider, '_make_request', side_effect=Exception("API Error")):
            start_time = datetime.utcnow()
            end_time = start_time + timedelta(days=1)
            
            availability = await calendly_provider.get_availability(
                calendar_ids=["test-calendar-id"],
                time_min=start_time,
                time_max=end_time
            )
            
            # Should return empty availability on error
            assert len(availability) == 1
            assert availability[0]["calendar_id"] == "test-calendar-id"
            assert availability[0]["busy"] == []
