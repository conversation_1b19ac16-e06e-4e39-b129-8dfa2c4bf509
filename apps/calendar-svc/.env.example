# Environment Configuration Example
# Copy this file to .env and update with your actual values

# Environment
ENVIRONMENT=development
DEBUG=false

# Database
DATABASE_URL=sqlite+aiosqlite:///./calendar.db
DATABASE_ECHO=false

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI=http://localhost:8000/api/v1/calendars/google/callback

# Calendly OAuth
CALENDLY_CLIENT_ID=your_calendly_client_id
CALENDLY_CLIENT_SECRET=your_calendly_client_secret
CALENDLY_REDIRECT_URI=http://localhost:8000/api/v1/calendars/calendly/callback
CALENDLY_WEBHOOK_SECRET=your_calendly_webhook_secret

# JWT
JWT_SECRET_KEY=your_jwt_secret_key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Redis (optional - will use memory cache if not provided)
REDIS_URI=redis://localhost:6379/0

# Cache settings
AVAIL_CACHE_TTL=30

# API
API_V1_STR=/api/v1
PROJECT_NAME=AI Lex Receptionist - Calendar Service

# Security
SECRET_KEY=your_secret_key_here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000"]

# App
APP_NAME=calendar-svc
