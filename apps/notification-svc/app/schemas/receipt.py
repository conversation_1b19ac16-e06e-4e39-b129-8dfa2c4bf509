"""Schemas for delivery receipts."""

from datetime import datetime
from typing import Optional, Dict, Any, List
from uuid import UUID
from pydantic import BaseModel, Field

from ..models.receipt import DeliveryChannel, DeliveryProvider, DeliveryStatus


class DeliveryReceiptBase(BaseModel):
    """Base schema for delivery receipts."""
    tenant_id: UUID
    message_id: str
    channel: DeliveryChannel
    provider: DeliveryProvider
    status: DeliveryStatus
    delivered_at: Optional[datetime] = None
    failure_reason: Optional[str] = None
    raw_payload: Dict[str, Any] = Field(default_factory=dict)


class DeliveryReceiptCreate(DeliveryReceiptBase):
    """Schema for creating delivery receipts."""
    pass


class DeliveryReceiptUpdate(BaseModel):
    """Schema for updating delivery receipts."""
    status: Optional[DeliveryStatus] = None
    delivered_at: Optional[datetime] = None
    failure_reason: Optional[str] = None
    raw_payload: Optional[Dict[str, Any]] = None


class DeliveryReceiptResponse(DeliveryReceiptBase):
    """Schema for delivery receipt responses."""
    id: UUID
    created_at: datetime

    class Config:
        orm_mode = True


class FailedNotificationResponse(BaseModel):
    """Schema for failed notification API response."""
    message_id: str
    channel: DeliveryChannel
    provider: DeliveryProvider
    failure_reason: Optional[str]
    created_at: datetime

    class Config:
        orm_mode = True


# Webhook payload schemas
class ResendWebhookPayload(BaseModel):
    """Schema for Resend webhook payloads."""
    type: str
    created_at: str
    data: Dict[str, Any]


class TelnyxWebhookPayload(BaseModel):
    """Schema for Telnyx webhook payloads."""
    event_type: str
    id: str
    occurred_at: str
    payload: Dict[str, Any]
