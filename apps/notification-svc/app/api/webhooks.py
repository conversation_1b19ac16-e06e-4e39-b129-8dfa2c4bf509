"""Webhook handlers for delivery receipts."""

import hmac
import hashlib
import json
import logging
from datetime import datetime
from typing import Any, Dict
from uuid import UUID

from fastapi import APIRouter, Request, HTTPException, Depends, Header
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from ..config import settings
from ..db.base import get_async_db
from ..models.receipt import DeliveryReceipt, DeliveryChannel, DeliveryProvider, DeliveryStatus
from ..schemas.receipt import ResendWebhookPayload, TelnyxWebhookPayload

logger = logging.getLogger(__name__)
router = APIRouter()


def verify_resend_signature(body: bytes, signature: str) -> bool:
    """Verify Resend webhook signature."""
    try:
        expected = hmac.new(
            settings.RESEND_WEBHOOK_SECRET.get_secret_value().encode(),
            body,
            hashlib.sha256,
        ).hexdigest()
        return hmac.compare_digest(f"sha256={expected}", signature)
    except Exception as e:
        logger.error(f"Error verifying Resend signature: {e}")
        return False


def verify_telnyx_signature(body: bytes, signature: str, timestamp: str) -> bool:
    """Verify Telnyx webhook signature."""
    try:
        # Telnyx uses timestamp + body for signature
        payload = f"{timestamp}.{body.decode()}"
        expected = hmac.new(
            settings.TELNYX_WEBHOOK_SECRET.get_secret_value().encode(),
            payload.encode(),
            hashlib.sha256,
        ).hexdigest()
        return hmac.compare_digest(expected, signature)
    except Exception as e:
        logger.error(f"Error verifying Telnyx signature: {e}")
        return False


async def create_or_update_receipt(
    db: AsyncSession,
    message_id: str,
    tenant_id: UUID,
    channel: DeliveryChannel,
    provider: DeliveryProvider,
    status: DeliveryStatus,
    delivered_at: datetime = None,
    failure_reason: str = None,
    raw_payload: Dict[str, Any] = None
) -> DeliveryReceipt:
    """Create or update a delivery receipt."""
    
    # Try to find existing receipt
    stmt = select(DeliveryReceipt).where(
        DeliveryReceipt.message_id == message_id,
        DeliveryReceipt.provider == provider
    )
    result = await db.execute(stmt)
    existing_receipt = result.scalar_one_or_none()
    
    if existing_receipt:
        # Update existing receipt
        update_stmt = update(DeliveryReceipt).where(
            DeliveryReceipt.id == existing_receipt.id
        ).values(
            status=status,
            delivered_at=delivered_at,
            failure_reason=failure_reason,
            raw_payload=raw_payload or {}
        )
        await db.execute(update_stmt)
        await db.commit()
        
        # Refresh the object
        await db.refresh(existing_receipt)
        return existing_receipt
    else:
        # Create new receipt
        new_receipt = DeliveryReceipt(
            tenant_id=tenant_id,
            message_id=message_id,
            channel=channel,
            provider=provider,
            status=status,
            delivered_at=delivered_at,
            failure_reason=failure_reason,
            raw_payload=raw_payload or {}
        )
        db.add(new_receipt)
        await db.commit()
        await db.refresh(new_receipt)
        return new_receipt


@router.post("/webhooks/resend")
async def resend_webhook(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    resend_signature: str = Header(..., alias="resend-signature")
):
    """Handle Resend delivery webhook events."""
    body = await request.body()
    
    # Verify signature
    if not verify_resend_signature(body, resend_signature):
        raise HTTPException(status_code=400, detail="Invalid signature")
    
    try:
        payload = ResendWebhookPayload.parse_raw(body)
        logger.info(f"Received Resend webhook: {payload.type}")
        
        # Extract relevant data from payload
        data = payload.data
        message_id = data.get("id")
        
        if not message_id:
            logger.warning("No message ID in Resend webhook payload")
            return {"status": "ignored", "reason": "no_message_id"}
        
        # Determine status based on event type
        status = DeliveryStatus.DELIVERED if payload.type == "email.delivered" else DeliveryStatus.FAILED
        delivered_at = datetime.fromisoformat(payload.created_at.replace('Z', '+00:00')) if status == DeliveryStatus.DELIVERED else None
        failure_reason = data.get("error", {}).get("message") if status == DeliveryStatus.FAILED else None
        
        # For now, use a default tenant_id - in production this should be extracted from the payload
        # or determined based on the API key/domain
        tenant_id = UUID("00000000-0000-0000-0000-000000000000")
        
        await create_or_update_receipt(
            db=db,
            message_id=message_id,
            tenant_id=tenant_id,
            channel=DeliveryChannel.EMAIL,
            provider=DeliveryProvider.RESEND,
            status=status,
            delivered_at=delivered_at,
            failure_reason=failure_reason,
            raw_payload=payload.dict()
        )
        
        return {"status": "processed"}
        
    except Exception as e:
        logger.error(f"Error processing Resend webhook: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/webhooks/telnyx")
async def telnyx_webhook(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    telnyx_signature_ed25519: str = Header(..., alias="telnyx-signature-ed25519"),
    telnyx_timestamp: str = Header(..., alias="telnyx-timestamp")
):
    """Handle Telnyx SMS delivery webhook events."""
    body = await request.body()
    
    # Verify signature
    if not verify_telnyx_signature(body, telnyx_signature_ed25519, telnyx_timestamp):
        raise HTTPException(status_code=400, detail="Invalid signature")
    
    try:
        payload = TelnyxWebhookPayload.parse_raw(body)
        logger.info(f"Received Telnyx webhook: {payload.event_type}")
        
        # Extract relevant data from payload
        data = payload.payload
        message_id = data.get("id")
        
        if not message_id:
            logger.warning("No message ID in Telnyx webhook payload")
            return {"status": "ignored", "reason": "no_message_id"}
        
        # Determine status based on event type
        status = DeliveryStatus.DELIVERED if payload.event_type == "message.sent" else DeliveryStatus.FAILED
        delivered_at = datetime.fromisoformat(payload.occurred_at.replace('Z', '+00:00')) if status == DeliveryStatus.DELIVERED else None
        failure_reason = data.get("errors", [{}])[0].get("detail") if status == DeliveryStatus.FAILED else None
        
        # For now, use a default tenant_id - in production this should be extracted from the payload
        tenant_id = UUID("00000000-0000-0000-0000-000000000000")
        
        await create_or_update_receipt(
            db=db,
            message_id=message_id,
            tenant_id=tenant_id,
            channel=DeliveryChannel.SMS,
            provider=DeliveryProvider.TELNYX,
            status=status,
            delivered_at=delivered_at,
            failure_reason=failure_reason,
            raw_payload=payload.dict()
        )
        
        return {"status": "processed"}
        
    except Exception as e:
        logger.error(f"Error processing Telnyx webhook: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
