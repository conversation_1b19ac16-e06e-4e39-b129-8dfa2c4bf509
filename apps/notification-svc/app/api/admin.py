"""Admin API endpoints for notification service."""

import logging
from datetime import datetime
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from ..config import settings
from ..db.base import get_async_db
from ..models.receipt import DeliveryReceipt, DeliveryStatus
from ..schemas.receipt import FailedNotificationResponse

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/admin/notifications/failed", response_model=List[FailedNotificationResponse])
async def get_failed_notifications(
    tenant_id: Optional[UUID] = Query(None, description="Filter by tenant ID"),
    since: Optional[datetime] = Query(None, description="Filter notifications created after this date"),
    until: Optional[datetime] = Query(None, description="Filter notifications created before this date"),
    limit: int = Query(50, ge=1, le=1000, description="Maximum number of results to return"),
    db: AsyncSession = Depends(get_async_db)
):
    """
    Get failed delivery receipts with optional filtering.
    
    Returns a list of failed notification deliveries with details about
    the failure reason, channel, provider, and timestamp.
    """
    try:
        # Build the query
        query = select(DeliveryReceipt).where(
            DeliveryReceipt.status == DeliveryStatus.FAILED
        )
        
        # Apply filters
        if tenant_id:
            query = query.where(DeliveryReceipt.tenant_id == tenant_id)
        
        if since:
            query = query.where(DeliveryReceipt.created_at >= since)
            
        if until:
            query = query.where(DeliveryReceipt.created_at <= until)
        
        # Order by most recent first and apply limit
        query = query.order_by(DeliveryReceipt.created_at.desc()).limit(limit)
        
        # Execute query
        result = await db.execute(query)
        receipts = result.scalars().all()
        
        # Convert to response format
        failed_notifications = [
            FailedNotificationResponse(
                message_id=receipt.message_id,
                channel=receipt.channel,
                provider=receipt.provider,
                failure_reason=receipt.failure_reason,
                created_at=receipt.created_at
            )
            for receipt in receipts
        ]
        
        logger.info(f"Retrieved {len(failed_notifications)} failed notifications")
        return failed_notifications
        
    except Exception as e:
        logger.error(f"Error retrieving failed notifications: {e}")
        raise
