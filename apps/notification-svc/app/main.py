"""Main FastAPI application for notification service."""

import logging
from datetime import datetime

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.exceptions import RequestValidationError

from .config import settings
from .api.webhooks import router as webhooks_router
from .api.admin import router as admin_router

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    
    # Initialize FastAPI app
    fastapi_app = FastAPI(
        title=settings.APP_NAME,
        description="AI Lex Notification Service - Handle delivery receipts for email and SMS",
        version=settings.APP_VERSION,
        openapi_url=f"{settings.API_PREFIX}/openapi.json",
        docs_url="/docs",
        redoc_url="/redoc",
    )
    
    # Add CORS middleware
    fastapi_app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.BACKEND_CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Include routers
    fastapi_app.include_router(webhooks_router)
    fastapi_app.include_router(admin_router, prefix=settings.API_PREFIX)
    
    # Health check endpoint
    @fastapi_app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "ok",
            "timestamp": datetime.utcnow().isoformat(),
            "service": settings.APP_NAME,
            "version": settings.APP_VERSION
        }
        
    # Root endpoint
    @fastapi_app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "message": f"Welcome to {settings.APP_NAME} API",
            "version": settings.APP_VERSION,
            "docs": "/docs",
            "redoc": "/redoc"
        }
    
    # Exception handlers
    @fastapi_app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        """Handle validation errors."""
        return JSONResponse(
            status_code=422,
            content={"detail": exc.errors(), "body": exc.body},
        )
    
    return fastapi_app


# Create the main app instance
app = create_app()


@app.on_event("startup")
async def startup_event():
    """Run startup events."""
    logger.info(f"{settings.APP_NAME} startup complete")


@app.on_event("shutdown")
async def shutdown_event():
    """Run shutdown events."""
    logger.info(f"{settings.APP_NAME} shutdown complete")
