"""Configuration settings for notification service."""

import os
from typing import List, Optional
from pydantic import Field, SecretStr
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""

    # Application settings
    APP_NAME: str = "AI Lex Notification Service"
    APP_VERSION: str = "1.0.0"
    APP_ENV: str = "development"
    DEBUG: bool = False
    SECRET_KEY: SecretStr = Field(default="test_secret_key", env="SECRET_KEY")
    API_PREFIX: str = "/api/v1"
    BACKEND_CORS_ORIGINS: List[str] = ["*"]
    
    # Security
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7 days
    ALGORITHM: str = "HS256"
    
    # Database
    DATABASE_URL: str = Field(default="sqlite:///./test.db", env="DATABASE_URL")
    DATABASE_URL_ASYNC: str = Field(default="sqlite+aiosqlite:///./test.db", env="DATABASE_URL_ASYNC")

    # Webhook secrets
    RESEND_WEBHOOK_SECRET: SecretStr = Field(default="test_secret", env="RESEND_WEBHOOK_SECRET")
    TELNYX_WEBHOOK_SECRET: SecretStr = Field(default="test_secret", env="TELNYX_WEBHOOK_SECRET")
    
    # Notification providers
    RESEND_API_KEY: Optional[str] = Field(None, env="RESEND_API_KEY")
    TELNYX_API_KEY: Optional[str] = Field(None, env="TELNYX_API_KEY")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# Global settings instance
settings = Settings()
