"""Test admin API endpoints."""

import os
from datetime import datetime, timed<PERSON>ta
from uuid import uuid4

import pytest
from httpx import AsyncClient

from app.models.receipt import Delivery<PERSON><PERSON>eipt, DeliveryChannel, DeliveryProvider, DeliveryStatus

# Set test environment variables
os.environ["RESEND_WEBHOOK_SECRET"] = "test_resend_secret"
os.environ["TELNYX_WEBHOOK_SECRET"] = "test_telnyx_secret"
os.environ["SECRET_KEY"] = "test_secret_key"
os.environ["DATABASE_URL"] = "sqlite:///./test.db"
os.environ["DATABASE_URL_ASYNC"] = "sqlite+aiosqlite:///./test.db"


@pytest.mark.asyncio
async def test_get_failed_notifications_empty(test_client: AsyncClient):
    """Test getting failed notifications when none exist."""
    response = await test_client.get("/api/v1/admin/notifications/failed")
    
    assert response.status_code == 200
    assert response.json() == []


@pytest.mark.asyncio
async def test_get_failed_notifications(test_client: AsyncClient, test_db):
    """Test getting failed notifications."""
    tenant_id = uuid4()
    
    # Create some test receipts
    failed_receipt = DeliveryReceipt(
        tenant_id=tenant_id,
        message_id="failed_msg_1",
        channel=DeliveryChannel.EMAIL,
        provider=DeliveryProvider.RESEND,
        status=DeliveryStatus.FAILED,
        failure_reason="Email bounced",
        raw_payload={"error": "bounce"}
    )
    
    delivered_receipt = DeliveryReceipt(
        tenant_id=tenant_id,
        message_id="delivered_msg_1",
        channel=DeliveryChannel.SMS,
        provider=DeliveryProvider.TELNYX,
        status=DeliveryStatus.DELIVERED,
        delivered_at=datetime.utcnow(),
        raw_payload={"success": True}
    )
    
    test_db.add(failed_receipt)
    test_db.add(delivered_receipt)
    await test_db.commit()
    
    # Get failed notifications
    response = await test_client.get("/api/v1/admin/notifications/failed")
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["message_id"] == "failed_msg_1"
    assert data[0]["channel"] == "email"
    assert data[0]["provider"] == "resend"
    assert data[0]["failure_reason"] == "Email bounced"


@pytest.mark.asyncio
async def test_get_failed_notifications_with_tenant_filter(test_client: AsyncClient, test_db):
    """Test getting failed notifications filtered by tenant."""
    tenant_id_1 = uuid4()
    tenant_id_2 = uuid4()
    
    # Create receipts for different tenants
    receipt_1 = DeliveryReceipt(
        tenant_id=tenant_id_1,
        message_id="failed_msg_tenant_1",
        channel=DeliveryChannel.EMAIL,
        provider=DeliveryProvider.RESEND,
        status=DeliveryStatus.FAILED,
        failure_reason="Tenant 1 failure"
    )
    
    receipt_2 = DeliveryReceipt(
        tenant_id=tenant_id_2,
        message_id="failed_msg_tenant_2",
        channel=DeliveryChannel.SMS,
        provider=DeliveryProvider.TELNYX,
        status=DeliveryStatus.FAILED,
        failure_reason="Tenant 2 failure"
    )
    
    test_db.add(receipt_1)
    test_db.add(receipt_2)
    await test_db.commit()
    
    # Get failed notifications for tenant 1 only
    response = await test_client.get(
        f"/api/v1/admin/notifications/failed?tenant_id={tenant_id_1}"
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["message_id"] == "failed_msg_tenant_1"
    assert data[0]["failure_reason"] == "Tenant 1 failure"


@pytest.mark.asyncio
async def test_get_failed_notifications_with_date_filter(test_client: AsyncClient, test_db):
    """Test getting failed notifications filtered by date range."""
    tenant_id = uuid4()
    now = datetime.utcnow()
    yesterday = now - timedelta(days=1)
    
    # Create an old receipt
    old_receipt = DeliveryReceipt(
        tenant_id=tenant_id,
        message_id="old_failed_msg",
        channel=DeliveryChannel.EMAIL,
        provider=DeliveryProvider.RESEND,
        status=DeliveryStatus.FAILED,
        failure_reason="Old failure",
        created_at=yesterday
    )
    
    # Create a recent receipt
    recent_receipt = DeliveryReceipt(
        tenant_id=tenant_id,
        message_id="recent_failed_msg",
        channel=DeliveryChannel.SMS,
        provider=DeliveryProvider.TELNYX,
        status=DeliveryStatus.FAILED,
        failure_reason="Recent failure"
    )
    
    test_db.add(old_receipt)
    test_db.add(recent_receipt)
    await test_db.commit()
    
    # Get notifications since today (should only return recent one)
    since_today = now.replace(hour=0, minute=0, second=0, microsecond=0)
    response = await test_client.get(
        f"/api/v1/admin/notifications/failed?since={since_today.isoformat()}"
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["message_id"] == "recent_failed_msg"


@pytest.mark.asyncio
async def test_get_failed_notifications_with_limit(test_client: AsyncClient, test_db):
    """Test getting failed notifications with limit."""
    tenant_id = uuid4()
    
    # Create multiple failed receipts
    for i in range(5):
        receipt = DeliveryReceipt(
            tenant_id=tenant_id,
            message_id=f"failed_msg_{i}",
            channel=DeliveryChannel.EMAIL,
            provider=DeliveryProvider.RESEND,
            status=DeliveryStatus.FAILED,
            failure_reason=f"Failure {i}"
        )
        test_db.add(receipt)
    
    await test_db.commit()
    
    # Get only 3 notifications
    response = await test_client.get("/api/v1/admin/notifications/failed?limit=3")
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 3


@pytest.mark.asyncio
async def test_get_failed_notifications_invalid_limit(test_client: AsyncClient):
    """Test getting failed notifications with invalid limit."""
    response = await test_client.get("/api/v1/admin/notifications/failed?limit=0")
    
    assert response.status_code == 422  # Validation error
    
    response = await test_client.get("/api/v1/admin/notifications/failed?limit=2000")
    
    assert response.status_code == 422  # Validation error
