"""Test configuration and fixtures."""

import asyncio
import os
import sys
from pathlib import Path

import pytest
import pytest_asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# Add the parent directory to the path so we can import app
sys.path.append(str(Path(__file__).parent.parent))
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from app.main import create_app
from app.db.base import get_async_db
from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, String, DateTime, JSON, Text, Enum as SAEnum
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime
import enum

# Create test-specific base and models to avoid schema issues
TestBase = declarative_base()

class TestDeliveryChannel(str, enum.Enum):
    EMAIL = "email"
    SMS = "sms"

class TestDeliveryProvider(str, enum.Enum):
    RESEND = "resend"
    TELNYX = "telnyx"

class TestDeliveryStatus(str, enum.Enum):
    DELIVERED = "delivered"
    FAILED = "failed"

class TestDeliveryReceipt(TestBase):
    __tablename__ = "delivery_receipts"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    tenant_id = Column(String, nullable=False, index=True)
    message_id = Column(Text, nullable=False)
    channel = Column(String, nullable=False)
    provider = Column(String, nullable=False)
    status = Column(String, nullable=False)
    delivered_at = Column(DateTime, nullable=True)
    failure_reason = Column(Text, nullable=True)
    raw_payload = Column(JSON, default=dict)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

# Test database URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///./test_notification.db"


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="session")
async def test_engine():
    """Create test database engine."""
    engine = create_async_engine(
        TEST_DATABASE_URL,
        connect_args={"check_same_thread": False},
        echo=False
    )
    
    # Create test tables
    async with engine.begin() as conn:
        await conn.run_sync(TestBase.metadata.create_all)
    
    yield engine
    
    # Clean up
    await engine.dispose()
    
    # Remove test database file
    db_file = Path("./test_notification.db")
    if db_file.exists():
        db_file.unlink()


@pytest_asyncio.fixture
async def test_db(test_engine):
    """Create test database session."""
    TestingSessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=test_engine,
        class_=AsyncSession
    )
    
    async with TestingSessionLocal() as session:
        yield session


@pytest_asyncio.fixture
async def test_app(test_db):
    """Create test FastAPI app."""
    app = create_app()
    
    # Override database dependency
    async def override_get_async_db():
        yield test_db
    
    app.dependency_overrides[get_async_db] = override_get_async_db
    
    yield app
    
    # Clean up
    app.dependency_overrides.clear()


@pytest_asyncio.fixture
async def test_client(test_app):
    """Create test HTTP client."""
    from httpx import ASGITransport
    async with AsyncClient(transport=ASGITransport(app=test_app), base_url="http://test") as client:
        yield client
