"""Test delivery receipt models."""

import pytest
from uuid import uuid4
from datetime import datetime

from tests.conftest import TestDeliveryReceipt, TestDeliveryChannel, TestDeliveryProvider, TestDeliveryStatus


@pytest.mark.asyncio
async def test_create_delivery_receipt(test_db):
    """Test creating a delivery receipt."""
    tenant_id = uuid4()
    message_id = "test_message_123"
    
    receipt = TestDeliveryReceipt(
        tenant_id=str(tenant_id),
        message_id=message_id,
        channel=TestDeliveryChannel.EMAIL.value,
        provider=TestDeliveryProvider.RESEND.value,
        status=TestDeliveryStatus.DELIVERED.value,
        delivered_at=datetime.utcnow(),
        raw_payload={"test": "data"}
    )
    
    test_db.add(receipt)
    await test_db.commit()
    await test_db.refresh(receipt)
    
    assert receipt.id is not None
    assert receipt.tenant_id == str(tenant_id)
    assert receipt.message_id == message_id
    assert receipt.channel == TestDeliveryChannel.EMAIL.value
    assert receipt.provider == TestDeliveryProvider.RESEND.value
    assert receipt.status == TestDeliveryStatus.DELIVERED.value
    assert receipt.delivered_at is not None
    assert receipt.raw_payload == {"test": "data"}
    assert receipt.created_at is not None


@pytest.mark.asyncio
async def test_create_failed_receipt(test_db):
    """Test creating a failed delivery receipt."""
    tenant_id = uuid4()
    message_id = "failed_message_456"
    
    receipt = TestDeliveryReceipt(
        tenant_id=str(tenant_id),
        message_id=message_id,
        channel=TestDeliveryChannel.SMS.value,
        provider=TestDeliveryProvider.TELNYX.value,
        status=TestDeliveryStatus.FAILED.value,
        failure_reason="Invalid phone number",
        raw_payload={"error": "validation_failed"}
    )
    
    test_db.add(receipt)
    await test_db.commit()
    await test_db.refresh(receipt)
    
    assert receipt.id is not None
    assert receipt.status == TestDeliveryStatus.FAILED.value
    assert receipt.failure_reason == "Invalid phone number"
    assert receipt.delivered_at is None
    assert receipt.raw_payload == {"error": "validation_failed"}


def test_delivery_receipt_enums():
    """Test delivery receipt enum values."""
    # Test DeliveryChannel enum
    assert TestDeliveryChannel.EMAIL == "email"
    assert TestDeliveryChannel.SMS == "sms"

    # Test DeliveryProvider enum
    assert TestDeliveryProvider.RESEND == "resend"
    assert TestDeliveryProvider.TELNYX == "telnyx"

    # Test DeliveryStatus enum
    assert TestDeliveryStatus.DELIVERED == "delivered"
    assert TestDeliveryStatus.FAILED == "failed"
