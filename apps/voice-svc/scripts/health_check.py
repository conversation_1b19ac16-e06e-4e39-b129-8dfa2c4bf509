#!/usr/bin/env python3
"""
Health check script for voice-svc deployment.

Verifies that all required dependencies and services are available:
- Python dependencies
- MJML CLI
- Email service configuration
- Database connectivity
- Redis connectivity
- HTTP endpoints (/health, /livez, /metrics)
"""
import asyncio
import os
import subprocess
import sys
from typing import Dict, Any

# Add the parent directory to the path so we can import from services
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import httpx
import redis.asyncio as redis
from sqlalchemy.ext.asyncio import create_async_engine

from services.email_fallback import EmailFallbackService


async def check_mjml_cli() -> Dict[str, Any]:
    """Check if MJML CLI is installed and working."""
    try:
        result = subprocess.run(
            ['mjml', '--version'],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            version = result.stdout.strip()
            return {
                "status": "ok",
                "version": version,
                "message": f"MJML CLI available: {version}"
            }
        else:
            return {
                "status": "error",
                "message": f"MJML CLI error: {result.stderr}"
            }
            
    except FileNotFoundError:
        return {
            "status": "error",
            "message": "MJML CLI not found. Install with: npm install -g mjml"
        }
    except subprocess.TimeoutExpired:
        return {
            "status": "error",
            "message": "MJML CLI timeout"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"MJML CLI check failed: {e}"
        }


async def check_email_service() -> Dict[str, Any]:
    """Check email service configuration."""
    try:
        service = EmailFallbackService()
        
        # Check required configuration
        issues = []
        
        if not service.resend_api_key:
            issues.append("RESEND_API_KEY not configured")
        
        if not service.email_from:
            issues.append("EMAIL_FROM not configured")
        
        if not service.core_api_url:
            issues.append("CORE_SUBS_API_URL not configured")
        
        if issues:
            return {
                "status": "warning",
                "message": f"Email service configuration issues: {', '.join(issues)}"
            }
        
        return {
            "status": "ok",
            "message": "Email service configuration valid"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"Email service check failed: {e}"
        }


async def check_redis_connectivity() -> Dict[str, Any]:
    """Check Redis connectivity."""
    try:
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
        
        redis_client = redis.from_url(redis_url)
        await redis_client.ping()
        await redis_client.aclose()
        
        return {
            "status": "ok",
            "message": f"Redis connectivity OK: {redis_url}"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"Redis connectivity failed: {e}"
        }


async def check_database_connectivity() -> Dict[str, Any]:
    """Check database connectivity."""
    try:
        database_url = os.getenv("DATABASE_URL")
        if not database_url:
            return {
                "status": "warning",
                "message": "DATABASE_URL not configured"
            }
        
        engine = create_async_engine(database_url)
        
        async with engine.begin() as conn:
            await conn.execute("SELECT 1")
        
        await engine.dispose()
        
        return {
            "status": "ok",
            "message": "Database connectivity OK"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"Database connectivity failed: {e}"
        }


async def check_core_api_connectivity() -> Dict[str, Any]:
    """Check Core API connectivity."""
    try:
        core_api_url = os.getenv("CORE_SUBS_API_URL")
        if not core_api_url:
            return {
                "status": "warning",
                "message": "CORE_SUBS_API_URL not configured"
            }

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Try to reach the health endpoint or root
            health_url = f"{core_api_url.rstrip('/')}/health"
            response = await client.get(health_url)

            if response.status_code == 200:
                return {
                    "status": "ok",
                    "message": f"Core API connectivity OK: {health_url}"
                }
            else:
                return {
                    "status": "warning",
                    "message": f"Core API returned {response.status_code}: {health_url}"
                }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Core API connectivity failed: {e}"
        }


async def check_http_endpoints() -> Dict[str, Any]:
    """Check HTTP endpoints (/health, /livez, /metrics)."""
    try:
        base_url = os.getenv("SERVICE_URL", "http://localhost:8000")
        endpoints = ["/health", "/livez", "/metrics"]
        results = {}

        async with httpx.AsyncClient(timeout=5.0) as client:
            for endpoint in endpoints:
                try:
                    url = f"{base_url}{endpoint}"
                    response = await client.get(url)

                    if response.status_code == 200:
                        results[endpoint] = "ok"
                    else:
                        results[endpoint] = f"status_{response.status_code}"

                except Exception as e:
                    results[endpoint] = f"error: {str(e)[:50]}"

        # Check if all endpoints are working
        all_ok = all(status == "ok" for status in results.values())

        if all_ok:
            return {
                "status": "ok",
                "message": f"All HTTP endpoints OK: {', '.join(endpoints)}",
                "details": results
            }
        else:
            failed = [ep for ep, status in results.items() if status != "ok"]
            return {
                "status": "warning",
                "message": f"Some HTTP endpoints failed: {', '.join(failed)}",
                "details": results
            }

    except Exception as e:
        return {
            "status": "error",
            "message": f"HTTP endpoints check failed: {e}"
        }


async def run_health_checks() -> Dict[str, Any]:
    """Run all health checks."""
    print("🔍 Running voice-svc health checks...")

    checks = {
        "mjml_cli": await check_mjml_cli(),
        "email_service": await check_email_service(),
        "redis": await check_redis_connectivity(),
        "database": await check_database_connectivity(),
        "core_api": await check_core_api_connectivity(),
        "http_endpoints": await check_http_endpoints(),
    }
    
    # Summary
    ok_count = sum(1 for check in checks.values() if check["status"] == "ok")
    warning_count = sum(1 for check in checks.values() if check["status"] == "warning")
    error_count = sum(1 for check in checks.values() if check["status"] == "error")
    
    overall_status = "ok"
    if error_count > 0:
        overall_status = "error"
    elif warning_count > 0:
        overall_status = "warning"
    
    return {
        "overall_status": overall_status,
        "summary": {
            "total": len(checks),
            "ok": ok_count,
            "warnings": warning_count,
            "errors": error_count
        },
        "checks": checks
    }


def print_results(results: Dict[str, Any]) -> None:
    """Print health check results in a readable format."""
    print(f"\n📊 Health Check Results")
    print(f"Overall Status: {results['overall_status'].upper()}")
    print(f"Summary: {results['summary']['ok']}/{results['summary']['total']} OK, "
          f"{results['summary']['warnings']} warnings, {results['summary']['errors']} errors\n")
    
    for name, check in results["checks"].items():
        status_emoji = {
            "ok": "✅",
            "warning": "⚠️",
            "error": "❌"
        }.get(check["status"], "❓")
        
        print(f"{status_emoji} {name.replace('_', ' ').title()}: {check['message']}")
    
    print()


async def main():
    """Main health check function."""
    try:
        results = await run_health_checks()
        print_results(results)
        
        # Exit with appropriate code
        if results["overall_status"] == "error":
            sys.exit(1)
        elif results["overall_status"] == "warning":
            sys.exit(2)  # Warning exit code
        else:
            sys.exit(0)
            
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
