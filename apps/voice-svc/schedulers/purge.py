"""
Background scheduler for purging expired call recordings.

This module sets up a daily cron job to automatically delete recordings
that are older than 30 days and log the deletions to the audit log.
"""
import logging
import os
from datetime import datetime, timezone
from typing import Optional

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import Cron<PERSON>rigger
from sqlalchemy.ext.asyncio import AsyncSession

from db.session import AsyncSessionLocal
from services.recording_service import recording_service, RecordingServiceError

logger = logging.getLogger(__name__)


class PurgeScheduler:
    """Scheduler for purging expired recordings."""
    
    def __init__(self):
        """Initialize the purge scheduler."""
        self.scheduler: Optional[AsyncIOScheduler] = None
        self.purge_cron = os.getenv("PURGE_CRON", "0 2 * * *")  # Default: 02:00 UTC daily
        
    async def start(self):
        """Start the purge scheduler."""
        if self.scheduler is not None:
            logger.warning("Purge scheduler is already running")
            return
        
        self.scheduler = AsyncIOScheduler()
        
        # Parse cron expression (format: "minute hour day month day_of_week")
        cron_parts = self.purge_cron.split()
        if len(cron_parts) != 5:
            logger.error(f"Invalid PURGE_CRON format: {self.purge_cron}")
            return
        
        minute, hour, day, month, day_of_week = cron_parts
        
        # Add the purge job
        self.scheduler.add_job(
            self._purge_expired_recordings,
            trigger=CronTrigger(
                minute=minute,
                hour=hour,
                day=day,
                month=month,
                day_of_week=day_of_week,
                timezone="UTC"
            ),
            id="purge_expired_recordings",
            name="Purge Expired Call Recordings",
            max_instances=1,  # Prevent overlapping executions
            coalesce=True,    # Combine missed executions
            misfire_grace_time=3600  # Allow 1 hour grace period
        )
        
        self.scheduler.start()
        logger.info(f"Started purge scheduler with cron: {self.purge_cron}")
    
    async def stop(self):
        """Stop the purge scheduler."""
        if self.scheduler is not None:
            self.scheduler.shutdown(wait=True)
            self.scheduler = None
            logger.info("Stopped purge scheduler")
    
    async def _purge_expired_recordings(self):
        """
        Purge expired recordings and log the results.
        
        This method is called by the scheduler and handles:
        1. Purging expired recordings from S3 and database
        2. Logging the purge results to audit log
        3. Error handling and logging
        """
        start_time = datetime.now(timezone.utc)
        logger.info("Starting scheduled purge of expired recordings")
        
        async with AsyncSessionLocal() as db:
            try:
                # Perform the purge
                result = await recording_service.purge_expired_recordings(db)
                
                # Log the results
                end_time = datetime.now(timezone.utc)
                duration = (end_time - start_time).total_seconds()
                
                logger.info(
                    f"Purge completed in {duration:.2f}s: "
                    f"{result.purged_count} purged, "
                    f"{result.failed_count} failed, "
                    f"{result.total_processed} total"
                )

                # Update metrics
                try:
                    from services.metrics_service import get_metrics_service
                    metrics_service = get_metrics_service()

                    # Record job duration
                    metrics_service.record_job_duration("purge_recordings", duration)

                    # Record purged recordings count (aggregate across all tenants)
                    # Note: We don't have tenant-specific data here, so we use "system" as tenant
                    metrics_service.increment_recordings_purged("system", result.purged_count)

                except ImportError:
                    logger.warning("Metrics service not available for purge scheduler")

                # Log to audit log (if audit logging is implemented)
                await self._log_purge_audit(db, result, start_time, end_time)
                
            except RecordingServiceError as e:
                logger.error(f"Recording service error during purge: {e}")
            except Exception as e:
                logger.error(f"Unexpected error during purge: {e}")
    
    async def _log_purge_audit(
        self,
        db: AsyncSession,
        result,
        start_time: datetime,
        end_time: datetime
    ):
        """
        Log purge operation to audit log.
        
        Args:
            db: Database session
            result: Purge result object
            start_time: When the purge started
            end_time: When the purge ended
        """
        try:
            # Note: This assumes an audit log table exists
            # If not implemented yet, this will be a no-op
            audit_data = {
                "event": "recording_purged",
                "timestamp": start_time.isoformat(),
                "duration_seconds": (end_time - start_time).total_seconds(),
                "purged_count": result.purged_count,
                "failed_count": result.failed_count,
                "total_processed": result.total_processed
            }
            
            # TODO: Implement actual audit logging when audit_log table is available
            logger.info(f"Audit log entry: {audit_data}")
            
        except Exception as e:
            logger.error(f"Failed to log purge audit: {e}")
    
    async def run_purge_now(self) -> dict:
        """
        Run purge immediately (for testing or manual execution).
        
        Returns:
            Dictionary with purge results
        """
        logger.info("Running manual purge of expired recordings")
        
        async with AsyncSessionLocal() as db:
            try:
                start_time = datetime.now(timezone.utc)
                result = await recording_service.purge_expired_recordings(db)
                end_time = datetime.now(timezone.utc)
                
                duration = (end_time - start_time).total_seconds()

                # Update metrics for manual purge
                try:
                    from services.metrics_service import get_metrics_service
                    metrics_service = get_metrics_service()

                    # Record job duration
                    metrics_service.record_job_duration("manual_purge_recordings", duration)

                    # Record purged recordings count
                    metrics_service.increment_recordings_purged("system", result.purged_count)

                except ImportError:
                    logger.warning("Metrics service not available for manual purge")

                return {
                    "success": True,
                    "purged_count": result.purged_count,
                    "failed_count": result.failed_count,
                    "total_processed": result.total_processed,
                    "duration_seconds": duration,
                    "timestamp": start_time.isoformat()
                }
                
            except Exception as e:
                logger.error(f"Error during manual purge: {e}")
                return {
                    "success": False,
                    "error": str(e),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }


# Global scheduler instance
purge_scheduler = PurgeScheduler()
