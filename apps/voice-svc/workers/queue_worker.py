"""
Queue worker for processing queued calls.

This worker:
- Monitors Redis streams for queued calls across all tenants
- Processes calls when capacity becomes available
- <PERSON><PERSON><PERSON>s calls into the voice session pipeline
- Handles worker lifecycle and error recovery
"""
import asyncio
import logging
import os
import signal
from typing import Dict, Set, Optional, Any
from contextlib import asynccontextmanager

import redis.asyncio as redis
from redis.asyncio import Redis

from services.queue_service import get_queue_service, CallMeta

logger = logging.getLogger(__name__)


class QueueWorker:
    """Background worker for processing queued calls."""
    
    def __init__(self, redis_url: Optional[str] = None):
        """
        Initialize the queue worker.
        
        Args:
            redis_url: Redis connection URL. Defaults to REDIS_URL env var.
        """
        self.redis_url = redis_url or os.getenv("REDIS_URL", "redis://localhost:6379/0")
        self.redis: Optional[Redis] = None
        self.queue_service = get_queue_service()
        
        # Worker state
        self.running = False
        self.tasks: Set[asyncio.Task] = set()
        self.monitored_tenants: Set[str] = set()
        
        # Configuration
        self.worker_poll_interval = float(os.getenv("QUEUE_WORKER_POLL_INTERVAL", "1.0"))
        self.max_concurrent_processing = int(os.getenv("MAX_CONCURRENT_CALL_PROCESSING", "10"))
        
        logger.info(
            f"Queue worker initialized: poll_interval={self.worker_poll_interval}s, "
            f"max_concurrent={self.max_concurrent_processing}"
        )
    
    async def connect(self) -> None:
        """Establish Redis connection for the worker."""
        if self.redis is None:
            self.redis = redis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_keepalive=True,
                socket_keepalive_options={},
                health_check_interval=30
            )
            await self.redis.ping()
            logger.info("Queue worker Redis connection established")
    
    async def disconnect(self) -> None:
        """Close Redis connection."""
        if self.redis:
            await self.redis.close()
            self.redis = None
            logger.info("Queue worker Redis connection closed")
    
    async def start(self) -> None:
        """Start the queue worker."""
        if self.running:
            logger.warning("Queue worker is already running")
            return
        
        await self.connect()
        self.running = True
        
        # Start the main worker loop
        worker_task = asyncio.create_task(self._worker_loop())
        self.tasks.add(worker_task)
        
        logger.info("Queue worker started")
    
    async def stop(self) -> None:
        """Stop the queue worker gracefully."""
        if not self.running:
            return
        
        self.running = False
        
        # Cancel all tasks
        for task in self.tasks:
            task.cancel()
        
        # Wait for tasks to complete
        if self.tasks:
            await asyncio.gather(*self.tasks, return_exceptions=True)
        
        self.tasks.clear()
        await self.disconnect()
        
        logger.info("Queue worker stopped")
    
    async def _worker_loop(self) -> None:
        """Main worker loop that monitors queues and processes calls."""
        logger.info("Queue worker loop started")
        
        while self.running:
            try:
                # Discover active tenants with queued calls
                await self._discover_tenants()
                
                # Process queued calls for each tenant
                processing_tasks = []
                for tenant_id in list(self.monitored_tenants):
                    if len(processing_tasks) >= self.max_concurrent_processing:
                        break
                    
                    task = asyncio.create_task(self._process_tenant_queue(tenant_id))
                    processing_tasks.append(task)
                
                # Wait for processing tasks to complete or timeout
                if processing_tasks:
                    await asyncio.gather(*processing_tasks, return_exceptions=True)
                
                # Sleep before next iteration
                await asyncio.sleep(self.worker_poll_interval)
                
            except asyncio.CancelledError:
                logger.info("Queue worker loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in queue worker loop: {e}", exc_info=True)
                await asyncio.sleep(self.worker_poll_interval)
    
    async def _discover_tenants(self) -> None:
        """Discover tenants with active queues."""
        if not self.redis:
            return
        
        try:
            # Scan for queue keys
            queue_keys = []
            async for key in self.redis.scan_iter(match="voice:queue:*"):
                queue_keys.append(key)
            
            # Extract tenant IDs and check if queues have messages
            current_tenants = set()
            for key in queue_keys:
                tenant_id = key.replace("voice:queue:", "")
                queue_length = await self.redis.xlen(key)
                if queue_length > 0:
                    current_tenants.add(tenant_id)
            
            # Update monitored tenants
            self.monitored_tenants = current_tenants
            
            if current_tenants:
                logger.debug(f"Monitoring queues for tenants: {current_tenants}")
                
        except Exception as e:
            logger.error(f"Failed to discover tenants: {e}")
    
    async def _process_tenant_queue(self, tenant_id: str) -> None:
        """
        Process queued calls for a specific tenant.
        
        Args:
            tenant_id: Tenant identifier
        """
        try:
            # Check if tenant has capacity for more calls
            active_count = await self.queue_service.get_active_count(tenant_id)
            if active_count >= self.queue_service.max_active_calls_per_tenant:
                logger.debug(f"Tenant {tenant_id} at capacity: {active_count}")
                return
            
            # Dequeue next call
            call_meta = await self.queue_service.dequeue_next(tenant_id)
            if not call_meta:
                # No calls in queue, remove from monitoring
                self.monitored_tenants.discard(tenant_id)
                return
            
            # Increment active count
            await self.queue_service.increment_active(tenant_id)
            
            # Start the voice session
            await self._start_voice_session(call_meta)
            
            logger.info(
                f"Successfully processed queued call {call_meta.call_id} for tenant {tenant_id}"
            )
            
        except Exception as e:
            logger.error(
                f"Failed to process queue for tenant {tenant_id}: {e}",
                exc_info=True
            )
    
    async def _start_voice_session(self, call_meta: CallMeta) -> None:
        """
        Start a voice session for a dequeued call.
        
        This function reinjets the call into the same pipeline used by
        direct WebSocket connections.
        
        Args:
            call_meta: Call metadata from the queue
        """
        try:
            # Import here to avoid circular imports
            from routes.api.voice_session import start_voice_session
            
            # Reconstruct the session arguments
            session_args = {
                "call_control_id": call_meta.call_control_id,
                "telnyx_rtc_session_id": call_meta.telnyx_rtc_session_id,
                "lang": call_meta.lang,
                "tenant_id": call_meta.tenant_id,
                "call_id": call_meta.call_id,
                "phone_number": call_meta.phone_number,
                "caller_name": call_meta.caller_name,
                "from_queue": True  # Flag to indicate this came from queue
            }
            
            # Start the voice session in a background task
            asyncio.create_task(start_voice_session(**session_args))
            
            logger.info(f"Voice session started for queued call {call_meta.call_id}")
            
        except ImportError:
            logger.error("Voice session module not available")
            # Decrement active count since we couldn't start the session
            await self.queue_service.decrement_active(call_meta.tenant_id)
        except Exception as e:
            logger.error(f"Failed to start voice session for call {call_meta.call_id}: {e}")
            # Decrement active count since we couldn't start the session
            await self.queue_service.decrement_active(call_meta.tenant_id)
    
    async def spawn_next_call(self, tenant_id: str) -> None:
        """
        Spawn the next queued call for a tenant (called when a call ends).
        
        Args:
            tenant_id: Tenant identifier
        """
        try:
            # Check if there are queued calls
            queue_length = await self.queue_service.get_queue_length(tenant_id)
            if queue_length == 0:
                logger.debug(f"No queued calls for tenant {tenant_id}")
                return
            
            # Check capacity
            active_count = await self.queue_service.get_active_count(tenant_id)
            if active_count >= self.queue_service.max_active_calls_per_tenant:
                logger.debug(f"Tenant {tenant_id} still at capacity: {active_count}")
                return
            
            # Process the next call
            await self._process_tenant_queue(tenant_id)
            
        except Exception as e:
            logger.error(f"Failed to spawn next call for tenant {tenant_id}: {e}")


# Global worker instance
_queue_worker: Optional[QueueWorker] = None


def get_queue_worker() -> QueueWorker:
    """Get the global queue worker instance."""
    global _queue_worker
    if _queue_worker is None:
        _queue_worker = QueueWorker()
    return _queue_worker


@asynccontextmanager
async def queue_worker_lifespan():
    """Context manager for queue worker lifecycle."""
    worker = get_queue_worker()
    try:
        await worker.start()
        yield worker
    finally:
        await worker.stop()


# Signal handlers for graceful shutdown
def setup_signal_handlers():
    """Setup signal handlers for graceful worker shutdown."""
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down queue worker...")
        asyncio.create_task(get_queue_worker().stop())
    
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
