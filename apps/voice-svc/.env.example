# AiLex Voice Service Environment Configuration

# Database Configuration
DATABASE_URL=sqlite+aiosqlite:///./test_voice_svc.db
# For PostgreSQL: postgresql+asyncpg://user:password@localhost/voice_svc

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Queue Configuration
MAX_ACTIVE_CALLS_PER_TENANT=4
MAX_QUEUE_LENGTH_PER_TENANT=10

# Core API Integration
CORE_SUBS_API_URL=http://localhost:8080/api/v1/subscriptions
CALENDAR_SVC_BASE=http://localhost:8001

# Telnyx Configuration
TELNYX_API_KEY=your_telnyx_api_key_here
TELNYX_WEBHOOK_SECRET=your_webhook_secret_here

# AWS S3 Configuration (for call recordings)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_DEFAULT_REGION=us-east-1
S3_BUCKET_NAME=ailex-voice-recordings

# Email Configuration (Resend)
RESEND_API_KEY=your_resend_api_key_here
FROM_EMAIL=<EMAIL>

# Webhook Configuration
IVR_WEBHOOK_URL=http://localhost:9000/ivr/webhook

# Metrics Configuration
# Metrics are exposed at /metrics endpoint by default
# Optional: Use separate port for metrics (uncomment if needed)
# METRICS_PORT=8001

# Logging Configuration
LOG_LEVEL=INFO

# Development/Testing
ENVIRONMENT=development
