"""
Queue management API endpoints.

Provides monitoring and management endpoints for the Redis-based call queue system.
Protected by partner role and voice_intake feature requirements.
"""
import logging
from typing import Dict, Any, Optional, List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel

from services.queue_service import get_queue_service
from workers.queue_worker import get_queue_worker

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/queue", tags=["queue"])


class QueueStatsResponse(BaseModel):
    """Response model for queue statistics."""
    tenant_id: str
    active: int
    queued: int
    max_active: int
    max_queue: int
    capacity_used: float
    queue_used: float
    can_accept_calls: bool
    queue_available: bool
    error: Optional[str] = None


class QueueClearResponse(BaseModel):
    """Response model for queue clear operation."""
    tenant_id: str
    calls_removed: int
    message: str


# TODO: Replace with actual auth dependencies when available
async def get_current_user():
    """Placeholder for user authentication."""
    return {"id": "test-user", "role": "partner"}


async def require_partner_role(current_user: dict = Depends(get_current_user)):
    """Require partner role for queue access."""
    if current_user.get("role") != "partner":
        raise HTTPException(
            status_code=403,
            detail="Partner role required for queue access"
        )
    return current_user


async def require_voice_intake_feature(current_user: dict = Depends(require_partner_role)):
    """Require voice_intake feature for queue access."""
    # TODO: Implement actual feature check when available
    # For now, assume partner role has voice_intake feature
    return current_user


@router.get("/", response_model=QueueStatsResponse)
async def get_queue_stats(
    tenant_id: Optional[str] = Query(None, description="Tenant ID (super-admin only)"),
    current_user: dict = Depends(require_voice_intake_feature)
) -> QueueStatsResponse:
    """
    Get queue statistics for the current tenant or specified tenant.
    
    For regular users, returns stats for their own tenant.
    Super-admin users can specify tenant_id to query any tenant.
    """
    try:
        # Determine which tenant to query
        if tenant_id is None:
            # Use current user's tenant
            query_tenant_id = current_user["id"]  # Assuming user ID acts as tenant ID
        else:
            # Super-admin check (TODO: implement proper super-admin role check)
            if current_user.get("role") != "super-admin":
                # For now, allow partner role to query any tenant for testing
                pass
            query_tenant_id = tenant_id
        
        # Get queue service and fetch stats
        queue_service = get_queue_service()
        stats = await queue_service.get_queue_stats(query_tenant_id)
        
        return QueueStatsResponse(**stats)
        
    except Exception as e:
        logger.error(f"Failed to get queue stats for tenant {query_tenant_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve queue statistics: {str(e)}"
        )


@router.get("/all", response_model=List[QueueStatsResponse])
async def get_all_queue_stats(
    current_user: dict = Depends(require_voice_intake_feature)
) -> List[QueueStatsResponse]:
    """
    Get queue statistics for all tenants (super-admin only).
    
    Returns statistics for all tenants with active queues or calls.
    """
    try:
        # TODO: Implement proper super-admin check
        # For now, allow any authenticated user for testing
        
        queue_service = get_queue_service()
        
        # Get all tenant IDs with active queues or calls
        await queue_service.connect()
        
        tenant_ids = set()
        
        # Scan for queue keys
        async for key in queue_service.redis.scan_iter(match="voice:queue:*"):
            tenant_id = key.replace("voice:queue:", "")
            tenant_ids.add(tenant_id)
        
        # Scan for active call keys
        async for key in queue_service.redis.scan_iter(match="voice:active:*"):
            tenant_id = key.replace("voice:active:", "")
            tenant_ids.add(tenant_id)
        
        # Get stats for each tenant
        all_stats = []
        for tenant_id in tenant_ids:
            stats = await queue_service.get_queue_stats(tenant_id)
            all_stats.append(QueueStatsResponse(**stats))
        
        return all_stats
        
    except Exception as e:
        logger.error(f"Failed to get all queue stats: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve all queue statistics: {str(e)}"
        )


@router.post("/clear", response_model=QueueClearResponse)
async def clear_queue(
    tenant_id: Optional[str] = Query(None, description="Tenant ID (super-admin only)"),
    current_user: dict = Depends(require_voice_intake_feature)
) -> QueueClearResponse:
    """
    Clear all queued calls for a tenant (admin operation).
    
    This is a destructive operation that removes all queued calls.
    Use with caution as it will drop waiting callers.
    """
    try:
        # Determine which tenant to clear
        if tenant_id is None:
            # Use current user's tenant
            query_tenant_id = current_user["id"]
        else:
            # Super-admin check (TODO: implement proper super-admin role check)
            if current_user.get("role") != "super-admin":
                # For now, allow partner role to clear any tenant for testing
                pass
            query_tenant_id = tenant_id
        
        # Clear the queue
        queue_service = get_queue_service()
        calls_removed = await queue_service.clear_tenant_queue(query_tenant_id)
        
        logger.warning(
            f"Queue cleared for tenant {query_tenant_id} by user {current_user['id']}: "
            f"{calls_removed} calls removed"
        )
        
        return QueueClearResponse(
            tenant_id=query_tenant_id,
            calls_removed=calls_removed,
            message=f"Successfully cleared {calls_removed} calls from queue"
        )
        
    except Exception as e:
        logger.error(f"Failed to clear queue for tenant {query_tenant_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear queue: {str(e)}"
        )


@router.get("/health")
async def queue_health_check() -> Dict[str, Any]:
    """
    Health check endpoint for queue system.
    
    Returns the status of queue service and worker components.
    """
    try:
        queue_service = get_queue_service()
        queue_worker = get_queue_worker()
        
        # Test Redis connection
        await queue_service.connect()
        await queue_service.redis.ping()
        
        return {
            "status": "healthy",
            "queue_service": "connected",
            "queue_worker": "running" if queue_worker.running else "stopped",
            "redis_connection": "ok",
            "max_active_per_tenant": queue_service.max_active_calls_per_tenant,
            "max_queue_per_tenant": queue_service.max_queue_length_per_tenant
        }
        
    except Exception as e:
        logger.error(f"Queue health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "queue_service": "error",
            "queue_worker": "unknown",
            "redis_connection": "failed"
        }


@router.post("/worker/start")
async def start_queue_worker(
    current_user: dict = Depends(require_voice_intake_feature)
) -> Dict[str, str]:
    """
    Start the queue worker (admin operation).
    """
    try:
        queue_worker = get_queue_worker()
        
        if queue_worker.running:
            return {"status": "already_running", "message": "Queue worker is already running"}
        
        await queue_worker.start()
        
        logger.info(f"Queue worker started by user {current_user['id']}")
        
        return {"status": "started", "message": "Queue worker started successfully"}
        
    except Exception as e:
        logger.error(f"Failed to start queue worker: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start queue worker: {str(e)}"
        )


@router.post("/worker/stop")
async def stop_queue_worker(
    current_user: dict = Depends(require_voice_intake_feature)
) -> Dict[str, str]:
    """
    Stop the queue worker (admin operation).
    """
    try:
        queue_worker = get_queue_worker()
        
        if not queue_worker.running:
            return {"status": "already_stopped", "message": "Queue worker is not running"}
        
        await queue_worker.stop()
        
        logger.info(f"Queue worker stopped by user {current_user['id']}")
        
        return {"status": "stopped", "message": "Queue worker stopped successfully"}
        
    except Exception as e:
        logger.error(f"Failed to stop queue worker: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to stop queue worker: {str(e)}"
        )
