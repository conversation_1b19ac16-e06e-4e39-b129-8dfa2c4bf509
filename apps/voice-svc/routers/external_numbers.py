"""
FastAPI router for external number registration (bring-your-own-number) endpoints.

Provides endpoints for:
- Registering external phone numbers
- Verifying phone numbers via code entry
- Listing tenant's external numbers
- Getting SIP URI for call forwarding
"""
import logging
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Header
from sqlalchemy.ext.asyncio import AsyncSession

from db.session import get_db
from schemas.external import (
    ExternalNumberCreate,
    ExternalNumberResponse,
    ExternalNumberDTO,
    ExternalNumberListResponse,
    VerificationRequest,
    VerificationResponse,
    SipUriResponse
)
from services.external_number_service import (
    ExternalNumberService,
    DuplicateNumberError,
    NumberNotFoundError,
    InvalidVerificationCodeError,
    ExternalNumberError
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/external-numbers", tags=["external-numbers"])


async def get_tenant_id(x_tenant_id: str = Header(..., alias="X-Tenant-ID")) -> UUID:
    """Extract tenant ID from header."""
    try:
        return UUID(x_tenant_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid tenant ID format")


@router.post("/", response_model=ExternalNumberResponse, status_code=201)
async def create_external_number(
    request: ExternalNumberCreate,
    tenant_id: UUID = Depends(get_tenant_id),
    db: AsyncSession = Depends(get_db)
):
    """
    Register a new external phone number for bring-your-own-number.

    This endpoint:
    1. Validates the phone number format (E.164)
    2. Blocks premium numbers (900, 976, etc.)
    3. Generates a 6-digit verification code
    4. Returns SIP URI for call forwarding setup

    The tenant must then:
    1. Configure their carrier to forward calls to the SIP URI
    2. Place a test call and enter the verification code
    3. Number status will change to 'verified' upon successful code entry
    """
    service = ExternalNumberService(db)

    try:
        external_number = await service.create_external_number(tenant_id, request.did)

        # Generate SIP URI for response
        sip_uri = service.generate_sip_uri(external_number.did)

        return ExternalNumberResponse(
            id=external_number.id,
            tenant_id=external_number.tenant_id,
            did=external_number.did,
            status=external_number.status,
            verification_code=external_number.verification_code,
            sip_uri=sip_uri,
            created_at=external_number.created_at,
            verified_at=external_number.verified_at
        )

    except DuplicateNumberError as e:
        logger.warning(f"Duplicate number registration attempt: {request.did}")
        raise HTTPException(status_code=409, detail=str(e))

    except ExternalNumberError as e:
        logger.error(f"External number creation failed: {e}")
        raise HTTPException(status_code=400, detail=str(e))

    except Exception as e:
        logger.error(f"Unexpected error creating external number: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/{did}/verify", response_model=VerificationResponse)
async def verify_external_number(
    did: str,
    request: VerificationRequest,
    tenant_id: UUID = Depends(get_tenant_id),
    db: AsyncSession = Depends(get_db)
):
    """
    Verify an external number using the 6-digit code.

    This endpoint is called after the tenant:
    1. Sets up call forwarding to the SIP URI
    2. Places a test call to their number
    3. Enters the 6-digit verification code during the call

    Upon successful verification, the number status changes to 'verified'
    and is ready to receive calls through the AI Voice Receptionist.
    """
    service = ExternalNumberService(db)

    try:
        # Verify the code
        success = await service.verify_code(did, request.code)

        if success:
            return VerificationResponse(
                status="verified",
                verified_at=None,  # Will be set by service
                message="Phone number successfully verified and ready for use"
            )
        else:
            raise HTTPException(status_code=400, detail="Verification failed")

    except NumberNotFoundError:
        logger.warning(f"Verification attempt for non-existent number: {did}")
        raise HTTPException(status_code=404, detail="External number not found")

    except InvalidVerificationCodeError:
        logger.warning(f"Invalid verification code for number: {did}")
        raise HTTPException(status_code=400, detail="Invalid verification code")

    except ExternalNumberError as e:
        logger.error(f"Verification error for {did}: {e}")
        raise HTTPException(status_code=400, detail=str(e))

    except Exception as e:
        logger.error(f"Unexpected error during verification: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/", response_model=ExternalNumberListResponse)
async def list_external_numbers(
    tenant_id: UUID = Depends(get_tenant_id),
    db: AsyncSession = Depends(get_db)
):
    """
    List all external numbers for the current tenant.

    Returns all registered external numbers with their current status:
    - pending: Number registered, awaiting verification
    - verifying: Test call received, code entry in progress
    - verified: Successfully verified and ready for use
    - failed: Verification failed
    """
    service = ExternalNumberService(db)

    try:
        external_numbers = await service.get_external_numbers(tenant_id)

        numbers_dto = [
            ExternalNumberDTO(
                id=num.id,
                did=num.did,
                status=num.status,
                created_at=num.created_at,
                verified_at=num.verified_at
            )
            for num in external_numbers
        ]

        return ExternalNumberListResponse(
            numbers=numbers_dto,
            total=len(numbers_dto)
        )

    except Exception as e:
        logger.error(f"Error listing external numbers for tenant {tenant_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{did}/sip-uri", response_model=SipUriResponse)
async def get_sip_uri(
    did: str,
    tenant_id: UUID = Depends(get_tenant_id),
    db: AsyncSession = Depends(get_db)
):
    """
    Get SIP URI and verification code for an external number.

    This endpoint provides the information needed to set up call forwarding:
    - SIP URI to forward calls to
    - 6-digit verification code for testing
    - Setup instructions
    """
    service = ExternalNumberService(db)

    try:
        # Get the external number to verify ownership
        external_number = await service.get_external_number_by_did(did)

        if not external_number:
            raise HTTPException(status_code=404, detail="External number not found")

        if external_number.tenant_id != tenant_id:
            raise HTTPException(status_code=403, detail="Access denied")

        sip_uri = service.generate_sip_uri(did)

        return SipUriResponse(
            sip_uri=sip_uri,
            verification_code=external_number.verification_code,
            instructions=(
                "1. Configure your phone carrier to forward calls to the SIP URI above\n"
                "2. Place a test call to your number\n"
                "3. When prompted, enter the 6-digit verification code\n"
                "4. Your number will be verified and ready for use"
            )
        )

    except HTTPException:
        raise

    except ExternalNumberError as e:
        logger.error(f"Error getting SIP URI for {did}: {e}")
        raise HTTPException(status_code=400, detail=str(e))

    except Exception as e:
        logger.error(f"Unexpected error getting SIP URI: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
