<mjml>
  <mj-head>
    <mj-title>📞 New Voice Intake - {{ caller_name or caller_number }}</mj-title>
    <mj-preview>New call from {{ caller_name or caller_number }} - {{ intent or "General inquiry" }}</mj-preview>
    <mj-attributes>
      <mj-all font-family="'Helvetica Neue', Helvetica, Arial, sans-serif"></mj-all>
      <mj-text font-weight="400" font-size="16px" color="#000000" line-height="24px" font-family="'Helvetica Neue', Helvetica, Arial, sans-serif"></mj-text>
    </mj-attributes>
    <mj-style inline="inline">
      .link-nostyle { color: inherit; text-decoration: none }
      .call-summary { background-color: #f8f9fa; border-left: 4px solid #007bff; padding: 16px; margin: 16px 0; }
      .booking-info { background-color: #e8f5e8; border-left: 4px solid #28a745; padding: 16px; margin: 16px 0; }
      .transcript-box { background-color: #f1f3f4; border-radius: 8px; padding: 16px; margin: 16px 0; font-family: monospace; }
    </mj-style>
  </mj-head>
  <mj-body background-color="#ffffff">
    <!-- Header -->
    <mj-section background-color="#007bff" padding="20px 0">
      <mj-column>
        <mj-text align="center" color="#ffffff" font-size="28px" font-weight="bold" line-height="36px">
          📞 New Voice Intake
        </mj-text>
        <mj-text align="center" color="#ffffff" font-size="16px" line-height="24px">
          {{ created_at.strftime('%B %d, %Y at %I:%M %p UTC') }}
        </mj-text>
      </mj-column>
    </mj-section>

    <!-- Call Summary -->
    <mj-section padding="20px">
      <mj-column>
        <mj-text font-size="20px" font-weight="bold" color="#333333">
          Call Summary
        </mj-text>
        
        <mj-raw>
          <div class="call-summary">
        </mj-raw>
        
        <mj-text font-size="16px" line-height="24px" padding="0">
          <strong>Caller:</strong> {{ caller_name or "Unknown" }}<br>
          <strong>Phone:</strong> <a href="tel:{{ caller_number }}" class="link-nostyle">{{ caller_number }}</a><br>
          <strong>Call ID:</strong> {{ call_id }}<br>
          {% if intent %}
          <strong>Intent:</strong> <span style="background-color: #e3f2fd; padding: 2px 8px; border-radius: 4px;">{{ intent }}</span><br>
          {% endif %}
          <strong>Duration:</strong> {{ call_duration or "Unknown" }}
        </mj-text>
        
        <mj-raw>
          </div>
        </mj-raw>
      </mj-column>
    </mj-section>

    <!-- Booking Information (if present) -->
    {% if booking %}
    <mj-section padding="20px">
      <mj-column>
        <mj-text font-size="20px" font-weight="bold" color="#333333">
          📅 Booking Details
        </mj-text>
        
        <mj-raw>
          <div class="booking-info">
        </mj-raw>
        
        <mj-text font-size="16px" line-height="24px" padding="0">
          <strong>Date & Time:</strong> {{ booking.slot_start.strftime('%B %d, %Y at %I:%M %p') }}<br>
          <strong>Duration:</strong> {{ ((booking.slot_end - booking.slot_start).total_seconds() / 60) | int }} minutes<br>
          <strong>Provider:</strong> {{ booking.provider | title }}<br>
          {% if booking.external_id %}
          <strong>External ID:</strong> {{ booking.external_id }}<br>
          {% endif %}
          {% if booking.provider_event_link %}
          <strong>Calendar Link:</strong> <a href="{{ booking.provider_event_link }}" style="color: #007bff;">View in {{ booking.provider | title }}</a>
          {% endif %}
        </mj-text>
        
        <mj-raw>
          </div>
        </mj-raw>
      </mj-column>
    </mj-section>
    {% endif %}

    <!-- Transcript -->
    <mj-section padding="20px">
      <mj-column>
        <mj-text font-size="20px" font-weight="bold" color="#333333">
          💬 Conversation Transcript
        </mj-text>
        
        <mj-raw>
          <div class="transcript-box">
        </mj-raw>
        
        <mj-text font-size="14px" line-height="20px" font-family="'Courier New', monospace" padding="0">
          {{ transcript | truncate(500) }}
          {% if transcript | length > 500 %}
          <br><br><em>... transcript truncated. View full transcript in admin panel.</em>
          {% endif %}
        </mj-text>
        
        <mj-raw>
          </div>
        </mj-raw>
      </mj-column>
    </mj-section>

    <!-- Recording (if available) -->
    {% if recording_url %}
    <mj-section padding="20px">
      <mj-column>
        <mj-text font-size="20px" font-weight="bold" color="#333333">
          🎵 Call Recording
        </mj-text>
        
        <mj-button background-color="#28a745" color="#ffffff" font-size="16px" font-weight="bold" href="{{ recording_url }}" padding="12px 24px">
          🎧 Listen to Recording
        </mj-button>
        
        <mj-text font-size="12px" color="#666666" align="center">
          Recording expires in 30 days
        </mj-text>
      </mj-column>
    </mj-section>
    {% endif %}

    <!-- Action Buttons -->
    <mj-section padding="20px" background-color="#f8f9fa">
      <mj-column>
        <mj-text font-size="20px" font-weight="bold" color="#333333" align="center">
          Quick Actions
        </mj-text>
        
        <mj-button background-color="#007bff" color="#ffffff" font-size="16px" font-weight="bold" href="{{ admin_url }}/voice/calls/{{ call_id }}" padding="12px 24px">
          📋 View in Admin Panel
        </mj-button>
        
        {% if caller_number %}
        <mj-button background-color="#17a2b8" color="#ffffff" font-size="16px" font-weight="bold" href="tel:{{ caller_number }}" padding="12px 24px">
          📞 Call Back
        </mj-button>
        {% endif %}
        
        {% if booking %}
        <mj-button background-color="#28a745" color="#ffffff" font-size="16px" font-weight="bold" href="{{ admin_url }}/calendar/bookings/{{ booking.booking_id or booking.external_id }}" padding="12px 24px">
          📅 Manage Booking
        </mj-button>
        {% endif %}
      </mj-column>
    </mj-section>

    <!-- Footer -->
    <mj-section padding="20px" background-color="#333333">
      <mj-column>
        <mj-text align="center" color="#ffffff" font-size="14px" line-height="20px">
          This email was sent because your tenant does not have Core webhook integration enabled.<br>
          To receive these notifications via API instead, contact support to enable the <strong>core_intake_sync</strong> feature.
        </mj-text>
        
        <mj-divider border-color="#666666" border-width="1px" padding="16px 0"></mj-divider>
        
        <mj-text align="center" color="#cccccc" font-size="12px" line-height="18px">
          AiLex Voice Service | Tenant: {{ tenant_id }}<br>
          Generated at {{ created_at.strftime('%Y-%m-%d %H:%M:%S UTC') }}
        </mj-text>
      </mj-column>
    </mj-section>
  </mj-body>
</mjml>
