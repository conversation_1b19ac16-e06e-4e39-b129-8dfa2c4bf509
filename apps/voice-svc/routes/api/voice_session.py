"""
Voice session handling with queue integration.

This module provides the main entry point for voice calls, integrating
the Redis-based queue system for load-shedding and FIFO call management.
"""
import asyncio
import logging
import os
import time
from typing import Dict, Any, Optional, List
from uuid import uuid4

import httpx
from fastapi import HTTPException

from services.queue_service import get_queue_service
from workers.queue_worker import get_queue_worker
from services.intake_webhook import get_intake_webhook_service
from services.intake_event_builder import get_intake_event_builder
from services.email_fallback import get_email_fallback_service

logger = logging.getLogger(__name__)


async def start_voice_session(
    call_control_id: str,
    telnyx_rtc_session_id: str,
    tenant_id: str,
    lang: str = "en",
    call_id: Optional[str] = None,
    phone_number: Optional[str] = None,
    caller_name: Optional[str] = None,
    from_queue: bool = False,
    **kwargs
) -> Dict[str, Any]:
    """
    Start a voice session with queue integration.
    
    This is the main entry point for all voice calls, whether they come
    directly from webhooks or are dequeued from the Redis queue.
    
    Args:
        call_control_id: Telnyx call control ID
        telnyx_rtc_session_id: Telnyx RTC session ID
        tenant_id: Tenant identifier
        lang: Language code (default: "en")
        call_id: Unique call identifier (generated if not provided)
        phone_number: Caller's phone number
        caller_name: Caller's name
        from_queue: Whether this call came from the queue
        **kwargs: Additional arguments
        
    Returns:
        Dictionary with session start result
    """
    if not call_id:
        call_id = f"call_{int(time.time() * 1000)}_{str(uuid4())[:8]}"
    
    logger.info(
        f"Starting voice session: call_id={call_id}, tenant_id={tenant_id}, "
        f"from_queue={from_queue}, lang={lang}"
    )
    
    try:
        # If this call is not from the queue, check capacity and potentially queue it
        if not from_queue:
            queue_service = get_queue_service()
            
            # Check if tenant is at capacity
            active_count = await queue_service.increment_active(tenant_id)
            
            if active_count > queue_service.max_active_calls_per_tenant:
                # Over capacity, decrement and try to queue
                await queue_service.decrement_active(tenant_id)
                
                # Prepare metadata for queueing
                ws_meta = {
                    "call_control_id": call_control_id,
                    "telnyx_rtc_session_id": telnyx_rtc_session_id,
                    "lang": lang,
                    "phone_number": phone_number,
                    "caller_name": caller_name
                }
                
                # Try to enqueue the call
                enqueued = await queue_service.enqueue_call(tenant_id, call_id, ws_meta)
                
                if not enqueued:
                    # Queue is full, play busy tone and hang up
                    logger.warning(
                        f"Call {call_id} rejected: tenant {tenant_id} at capacity and queue full"
                    )
                    await play_busy_and_hangup(call_control_id)
                    return {
                        "status": "rejected",
                        "reason": "capacity_exceeded",
                        "call_id": call_id,
                        "tenant_id": tenant_id
                    }
                else:
                    # Successfully queued, play hold message
                    logger.info(f"Call {call_id} queued for tenant {tenant_id}")
                    await play_hold_message(call_control_id)
                    return {
                        "status": "queued",
                        "call_id": call_id,
                        "tenant_id": tenant_id,
                        "queue_position": await queue_service.get_queue_length(tenant_id)
                    }
        
        # Start the actual voice pipeline
        result = await _start_pipecat_pipeline(
            call_control_id=call_control_id,
            telnyx_rtc_session_id=telnyx_rtc_session_id,
            tenant_id=tenant_id,
            call_id=call_id,
            lang=lang,
            phone_number=phone_number,
            caller_name=caller_name
        )
        
        logger.info(f"Voice session started successfully for call {call_id}")
        
        return {
            "status": "started",
            "call_id": call_id,
            "tenant_id": tenant_id,
            "pipeline_result": result
        }
        
    except Exception as e:
        logger.error(f"Failed to start voice session for call {call_id}: {e}", exc_info=True)
        
        # If we incremented active count but failed to start, decrement it
        if not from_queue:
            try:
                queue_service = get_queue_service()
                await queue_service.decrement_active(tenant_id)
            except Exception as cleanup_error:
                logger.error(f"Failed to cleanup active count for {tenant_id}: {cleanup_error}")
        
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start voice session: {str(e)}"
        )


async def end_voice_session(
    tenant_id: str,
    call_id: str,
    caller_number: Optional[str] = None,
    caller_name: Optional[str] = None,
    transcript: Optional[str] = None,
    intent: Optional[str] = None,
    pipeline_data: Optional[Dict[str, Any]] = None
) -> None:
    """
    End a voice session and potentially spawn the next queued call.

    This should be called when a call ends to:
    1. Send intake event to Core AiLex
    2. Decrement the active call count
    3. Trigger processing of the next queued call if any

    Args:
        tenant_id: Tenant identifier
        call_id: Call identifier that ended
        caller_number: Caller's phone number
        caller_name: Caller's name if available
        transcript: Full conversation transcript
        intent: Detected intent from the conversation
        pipeline_data: Additional data from the voice pipeline
    """
    logger.info(f"Ending voice session: call_id={call_id}, tenant_id={tenant_id}")

    try:
        # Send intake event to Core AiLex
        await _send_intake_event_for_call_end(
            call_id=call_id,
            tenant_id=tenant_id,
            caller_number=caller_number,
            caller_name=caller_name,
            transcript=transcript,
            intent=intent,
            pipeline_data=pipeline_data
        )

        # Handle queue management
        queue_service = get_queue_service()
        queue_worker = get_queue_worker()

        # Decrement active count
        active_count = await queue_service.decrement_active(tenant_id)

        logger.info(f"Active calls for tenant {tenant_id} after call end: {active_count}")

        # Spawn next call if there are queued calls and capacity
        await queue_worker.spawn_next_call(tenant_id)

    except Exception as e:
        logger.error(f"Error ending voice session for call {call_id}: {e}", exc_info=True)


async def _send_intake_event_for_call_end(
    call_id: str,
    tenant_id: str,
    caller_number: Optional[str] = None,
    caller_name: Optional[str] = None,
    transcript: Optional[str] = None,
    intent: Optional[str] = None,
    pipeline_data: Optional[Dict[str, Any]] = None
) -> None:
    """
    Send intake event to Core AiLex or email fallback for a completed call.

    This function automatically determines whether to use webhook or email
    based on the tenant's core_intake_sync feature flag.

    Args:
        call_id: Call identifier
        tenant_id: Tenant identifier
        caller_number: Caller's phone number
        caller_name: Caller's name if available
        transcript: Full conversation transcript
        intent: Detected intent from the conversation
        pipeline_data: Additional data from the voice pipeline
    """
    try:
        # Get services
        intake_service = get_intake_webhook_service()
        event_builder = get_intake_event_builder()
        email_service = get_email_fallback_service()

        # Extract additional data from pipeline if available
        if pipeline_data:
            if not transcript:
                transcript = event_builder.extract_transcript_from_pipeline_data(pipeline_data)
            if not intent:
                intent = event_builder.extract_intent_from_pipeline_data(pipeline_data)

        # Get recording URL if available
        recording_url = event_builder.get_recording_url_for_call(call_id, tenant_id)

        # Build intake event
        intake_event = event_builder.build_call_completion_event(
            call_id=call_id,
            tenant_id=tenant_id,
            caller_number=caller_number or "unknown",
            caller_name=caller_name,
            transcript=transcript or "No transcript available",
            intent=intent,
            recording_url=recording_url
        )

        # Check if tenant has core_intake_sync feature by querying Core API
        has_core_sync = await _check_tenant_has_core_sync(tenant_id)

        if has_core_sync:
            # Send to Core AiLex via webhook
            result = await intake_service.send_intake_event(intake_event)

            if result.success:
                logger.info(f"Intake event sent successfully for call {call_id}")
            else:
                logger.warning(
                    f"Intake event delivery failed for call {call_id}: {result.error_message}. "
                    f"Retry scheduled: {result.retry_scheduled}"
                )
        else:
            # Send via email fallback for AVR-only tenants
            logger.info(f"Tenant {tenant_id} lacks core_intake_sync feature - using email fallback")

            email_result = await email_service.send_intake_email(intake_event, tenant_id)

            if email_result["success"]:
                if email_result.get("skipped"):
                    logger.info(f"Email fallback skipped for call {call_id} (SKIP_EMAIL=true)")
                else:
                    logger.info(
                        f"Email fallback sent successfully for call {call_id} to "
                        f"{len(email_result['recipients'])} recipients"
                    )
            else:
                logger.error(
                    f"Email fallback failed for call {call_id}: {email_result.get('error')}"
                )

    except Exception as e:
        logger.error(f"Failed to send intake event for call {call_id}: {e}", exc_info=True)


async def _check_tenant_has_core_sync(tenant_id: str) -> bool:
    """
    Check if tenant has core_intake_sync feature flag.

    Args:
        tenant_id: Tenant identifier

    Returns:
        True if tenant has core_intake_sync feature, False otherwise
    """
    try:
        # Import here to avoid circular imports
        from avr.api.middleware.subscription_check import SubscriptionCheckMiddleware

        # Create a temporary middleware instance to fetch features
        core_api_url = os.getenv("CORE_SUBS_API_URL")
        redis_url = os.getenv("REDIS_URL")

        if not core_api_url or not redis_url:
            logger.warning("Core API URL or Redis URL not configured - assuming no core_intake_sync")
            return False

        middleware = SubscriptionCheckMiddleware(
            app=None,  # Not needed for feature fetching
            core_api_url=core_api_url,
            redis_url=redis_url
        )

        # Fetch tenant features
        features = await middleware._get_tenant_features(tenant_id)

        # Check if core_intake_sync is in features
        has_core_sync = "core_intake_sync" in features

        logger.debug(f"Tenant {tenant_id} core_intake_sync check: {has_core_sync}")

        return has_core_sync

    except Exception as e:
        logger.error(f"Error checking core_intake_sync feature for tenant {tenant_id}: {e}")
        # Default to email fallback on error
        return False


async def play_hold_message(call_control_id: str) -> None:
    """
    Play hold message to caller while they wait in queue.
    
    Args:
        call_control_id: Telnyx call control ID
    """
    try:
        hold_message_url = os.getenv(
            "HOLD_MESSAGE_URL",
            "https://cdn.example.com/hold-music.mp3"  # Default placeholder
        )
        
        # Use Telnyx API to play hold message
        telnyx_api_key = os.getenv("TELNYX_API_KEY")
        if not telnyx_api_key:
            logger.warning("TELNYX_API_KEY not set, cannot play hold message")
            return
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"https://api.telnyx.com/v2/calls/{call_control_id}/actions/playback_start",
                headers={
                    "Authorization": f"Bearer {telnyx_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "audio_url": hold_message_url,
                    "loop": True,
                    "overlay": False
                },
                timeout=10.0
            )
            
            if response.status_code == 200:
                logger.info(f"Hold message started for call {call_control_id}")
            else:
                logger.error(
                    f"Failed to start hold message for call {call_control_id}: "
                    f"{response.status_code} {response.text}"
                )
                
    except Exception as e:
        logger.error(f"Error playing hold message for call {call_control_id}: {e}")


async def play_busy_and_hangup(call_control_id: str) -> None:
    """
    Play busy tone and hang up the call.
    
    Args:
        call_control_id: Telnyx call control ID
    """
    try:
        telnyx_api_key = os.getenv("TELNYX_API_KEY")
        if not telnyx_api_key:
            logger.warning("TELNYX_API_KEY not set, cannot play busy tone")
            return
        
        async with httpx.AsyncClient() as client:
            # Play busy tone
            await client.post(
                f"https://api.telnyx.com/v2/calls/{call_control_id}/actions/playback_start",
                headers={
                    "Authorization": f"Bearer {telnyx_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "audio_url": "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",  # Placeholder
                    "loop": False,
                    "overlay": False
                },
                timeout=10.0
            )
            
            # Wait a moment then hang up
            await asyncio.sleep(2.0)
            
            # Hang up the call
            await client.post(
                f"https://api.telnyx.com/v2/calls/{call_control_id}/actions/hangup",
                headers={
                    "Authorization": f"Bearer {telnyx_api_key}",
                    "Content-Type": "application/json"
                },
                timeout=10.0
            )
            
            logger.info(f"Busy tone played and call hung up: {call_control_id}")
            
    except Exception as e:
        logger.error(f"Error playing busy tone for call {call_control_id}: {e}")


async def _start_pipecat_pipeline(
    call_control_id: str,
    telnyx_rtc_session_id: str,
    tenant_id: str,
    call_id: str,
    lang: str = "en",
    phone_number: Optional[str] = None,
    caller_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    Start the actual Pipecat voice pipeline.
    
    This is a placeholder for the actual pipeline implementation.
    In a real implementation, this would start the voice agent pipeline.
    
    Args:
        call_control_id: Telnyx call control ID
        telnyx_rtc_session_id: Telnyx RTC session ID
        tenant_id: Tenant identifier
        call_id: Call identifier
        lang: Language code
        phone_number: Caller's phone number
        caller_name: Caller's name
        
    Returns:
        Dictionary with pipeline start result
    """
    # TODO: Implement actual Pipecat pipeline integration
    # This would typically:
    # 1. Create a Telnyx transport
    # 2. Set up the voice agent pipeline
    # 3. Start the conversation
    
    logger.info(
        f"Starting Pipecat pipeline for call {call_id} "
        f"(control_id={call_control_id}, session_id={telnyx_rtc_session_id})"
    )
    
    # Simulate pipeline startup
    await asyncio.sleep(0.1)
    
    # Register cleanup callback for when the call ends
    asyncio.create_task(_monitor_call_end(
        tenant_id=tenant_id,
        call_id=call_id,
        call_control_id=call_control_id,
        caller_number=phone_number,
        caller_name=caller_name
    ))
    
    return {
        "pipeline_started": True,
        "call_control_id": call_control_id,
        "telnyx_rtc_session_id": telnyx_rtc_session_id,
        "language": lang
    }


async def _monitor_call_end(
    tenant_id: str,
    call_id: str,
    call_control_id: str,
    caller_number: Optional[str] = None,
    caller_name: Optional[str] = None
) -> None:
    """
    Monitor for call end and trigger cleanup.

    This is a placeholder implementation. In a real system, this would
    listen for call end events from Telnyx or the voice pipeline.

    Args:
        tenant_id: Tenant identifier
        call_id: Call identifier
        call_control_id: Telnyx call control ID
        caller_number: Caller's phone number
        caller_name: Caller's name if available
    """
    try:
        # TODO: Implement actual call monitoring
        # This could listen for:
        # - Telnyx webhook events
        # - Pipeline completion events
        # - WebSocket disconnections

        # For now, simulate a call duration
        call_duration = 30.0  # Simulate 30 second call
        await asyncio.sleep(call_duration)

        logger.info(f"Call {call_id} ended after {call_duration}s")

        # Simulate some conversation data
        simulated_transcript = "Hello, I'd like to schedule an appointment. Thank you for your help."
        simulated_intent = "book_meeting" if "appointment" in simulated_transcript else None

        # Trigger cleanup with call data
        await end_voice_session(
            tenant_id=tenant_id,
            call_id=call_id,
            caller_number=caller_number,
            caller_name=caller_name,
            transcript=simulated_transcript,
            intent=simulated_intent
        )

    except Exception as e:
        logger.error(f"Error monitoring call end for {call_id}: {e}")
        # Still try to cleanup
        try:
            await end_voice_session(
                tenant_id=tenant_id,
                call_id=call_id,
                caller_number=caller_number,
                caller_name=caller_name
            )
        except Exception as cleanup_error:
            logger.error(f"Failed to cleanup after monitoring error: {cleanup_error}")
