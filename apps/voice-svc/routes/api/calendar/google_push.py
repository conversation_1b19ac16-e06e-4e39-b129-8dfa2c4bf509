import os
from typing import Optional

from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPEx<PERSON>, Depends
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select

from packages.shared import models

DATABASE_URL_ASYNC = os.getenv("DATABASE_URL_ASYNC", "sqlite+aiosqlite:///./test.db")
engine = create_async_engine(DATABASE_URL_ASYNC, future=True)
AsyncSessionLocal = sessionmaker(bind=engine, class_=AsyncSession, expire_on_commit=False)


async def get_async_db() -> AsyncSession:
    async with AsyncSessionLocal() as session:
        yield session


async def fetch_event(event_id: str, status: str) -> dict:
    """Placeholder for Google Calendar API call."""
    return {"id": event_id, "status": status}


router = APIRouter()


@router.post("/calendar/google/push")
async def google_push(
    x_goog_channel_token: str = Header(..., alias="X-Goog-Channel-Token"),
    x_goog_resource_state: str = Header(..., alias="X-Goog-Resource-State"),
    x_goog_resource_uri: Optional[str] = Header(None, alias="X-Goog-Resource-URI"),
    db: AsyncSession = Depends(get_async_db),
):
    secret = os.getenv("GOOGLE_PUSH_SECRET")
    if secret is None or x_goog_channel_token != secret:
        raise HTTPException(status_code=403, detail="Invalid channel token")

    if x_goog_resource_state in {"cancelled", "exists"} and x_goog_resource_uri:
        event_id = x_goog_resource_uri.rstrip("/").split("/")[-1]
        event = await fetch_event(event_id, x_goog_resource_state)
        result = await db.execute(
            select(models.Booking).where(models.Booking.calendar_event_id == event_id)
        )
        booking = result.scalar_one_or_none()
        if booking:
            booking.status = event.get("status")
            await db.commit()

    return {"status": "ok"}
