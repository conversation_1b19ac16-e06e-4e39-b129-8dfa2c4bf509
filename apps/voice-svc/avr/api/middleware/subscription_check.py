"""
Subscription check middleware for AVR service.

This middleware validates tenant subscriptions by:
1. Extracting tenant ID from X-Tenant-ID header
2. Fetching tenant features from Core API
3. Caching features in Redis for 60 seconds
4. Checking if tenant has required feature for the route
5. Injecting features into request.state for downstream use
"""
import asyncio
import json
import logging
import os
from typing import Dict, List, Optional, Set

import httpx
import redis.asyncio as redis
from fastapi import HTTPException, Request, Response, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)

from ..deps import get_current_tenant

logger = logging.getLogger(__name__)

# Route to feature mapping
ROUTE_FEATURES = {
    "/voice/intake": "voice_intake",
    "/calendar/availability": "calendar_booking",
    "/calendar/book": "calendar_booking",
    "/api/v1/numbers/available": "voice_intake",
    "/api/v1/numbers/purchase": "voice_intake",
    "/api/v1/numbers/": "voice_intake",
    "/api/v1/external-numbers": "voice_intake",
    "/api/v1/external-numbers/": "voice_intake",
}


class SubscriptionCheckMiddleware(BaseHTTPMiddleware):
    """
    Middleware to check tenant subscription features.
    """
    
    def __init__(
        self,
        app,
        core_api_url: Optional[str] = None,
        redis_url: Optional[str] = None,
        cache_ttl: int = 60,
    ):
        super().__init__(app)
        self.core_api_url = core_api_url or os.getenv("CORE_SUBS_API_URL")
        self.redis_url = redis_url or os.getenv("REDIS_URL")
        self.cache_ttl = cache_ttl
        self._redis_client: Optional[redis.Redis] = None
        self._http_client: Optional[httpx.AsyncClient] = None
        
        if not self.core_api_url:
            raise ValueError("CORE_SUBS_API_URL environment variable is required")
        
        if not self.redis_url:
            raise ValueError("REDIS_URL environment variable is required")
    
    async def _get_redis_client(self) -> redis.Redis:
        """Get or create Redis client."""
        if self._redis_client is None:
            self._redis_client = redis.from_url(self.redis_url, decode_responses=True)
        return self._redis_client
    
    async def _get_http_client(self) -> httpx.AsyncClient:
        """Get or create HTTP client."""
        if self._http_client is None:
            self._http_client = httpx.AsyncClient(timeout=30.0)
        return self._http_client
    
    async def _get_cache_key(self, tenant_id: str) -> str:
        """Generate cache key for tenant features."""
        return f"tenant_features:{tenant_id}"
    
    async def _get_cached_features(self, tenant_id: str) -> Optional[List[str]]:
        """Get cached features for tenant."""
        try:
            redis_client = await self._get_redis_client()
            cache_key = await self._get_cache_key(tenant_id)
            cached_data = await redis_client.get(cache_key)
            
            if cached_data:
                features_data = json.loads(cached_data)
                logger.debug(f"Cache hit for tenant {tenant_id}")
                return features_data.get("features", [])
                
        except Exception as e:
            logger.warning(f"Redis cache get error for tenant {tenant_id}: {e}")
        
        return None
    
    async def _cache_features(self, tenant_id: str, features: List[str]) -> None:
        """Cache features for tenant."""
        try:
            redis_client = await self._get_redis_client()
            cache_key = await self._get_cache_key(tenant_id)
            cache_data = {"features": features}
            
            await redis_client.setex(
                cache_key,
                self.cache_ttl,
                json.dumps(cache_data)
            )
            logger.debug(f"Cached features for tenant {tenant_id}")
            
        except Exception as e:
            logger.warning(f"Redis cache set error for tenant {tenant_id}: {e}")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((httpx.RequestError, httpx.HTTPStatusError)),
    )
    async def _fetch_tenant_features(self, tenant_id: str) -> List[str]:
        """
        Fetch tenant features from Core API with exponential backoff retry.
        
        Args:
            tenant_id: The tenant identifier
            
        Returns:
            List of feature names
            
        Raises:
            HTTPException: If Core API is unreachable after retries
        """
        try:
            http_client = await self._get_http_client()
            url = f"{self.core_api_url.rstrip('/')}/tenant/{tenant_id}/features"
            
            logger.debug(f"Fetching features for tenant {tenant_id} from {url}")
            
            response = await http_client.get(url)
            response.raise_for_status()
            
            data = response.json()
            features = data.get("features", [])
            
            logger.debug(f"Fetched features for tenant {tenant_id}: {features}")
            return features
            
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                logger.warning(f"Tenant {tenant_id} not found in Core API")
                return []
            logger.error(f"Core API HTTP error for tenant {tenant_id}: {e}")
            raise
        except httpx.RequestError as e:
            logger.error(f"Core API request error for tenant {tenant_id}: {e}")
            raise
    
    async def _get_tenant_features(self, tenant_id: str) -> List[str]:
        """
        Get tenant features from cache or Core API.
        
        Args:
            tenant_id: The tenant identifier
            
        Returns:
            List of feature names
            
        Raises:
            HTTPException: If Core API is unreachable
        """
        # Try cache first
        features = await self._get_cached_features(tenant_id)
        if features is not None:
            return features
        
        # Fetch from Core API
        try:
            features = await self._fetch_tenant_features(tenant_id)
            # Cache the result
            await self._cache_features(tenant_id, features)
            return features
            
        except Exception as e:
            logger.error(f"Failed to fetch features for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Subscription service temporarily unavailable"
            )
    
    def _get_required_feature(self, path: str) -> Optional[str]:
        """
        Get required feature for a given route path.
        
        Args:
            path: The request path
            
        Returns:
            Required feature name or None if no feature required
        """
        return ROUTE_FEATURES.get(path)
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Process the request through subscription check middleware.
        
        Args:
            request: The incoming request
            call_next: The next middleware/handler in the chain
            
        Returns:
            Response from the next handler
            
        Raises:
            HTTPException: For various subscription check failures
        """
        # Skip middleware for health checks and docs
        if request.url.path in ["/health", "/", "/docs", "/redoc", "/openapi.json"]:
            return await call_next(request)
        
        try:
            # Extract tenant ID
            tenant_id = await get_current_tenant(request)
            
            # Get tenant features
            features = await self._get_tenant_features(tenant_id)
            
            # Check if route requires a specific feature
            required_feature = self._get_required_feature(request.url.path)
            
            if required_feature and required_feature not in features:
                logger.warning(
                    f"Tenant {tenant_id} lacks required feature '{required_feature}' "
                    f"for route {request.url.path}"
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Access denied: feature '{required_feature}' not available"
                )
            
            # Inject features into request state
            request.state.features = features
            request.state.tenant_id = tenant_id
            
            logger.debug(
                f"Subscription check passed for tenant {tenant_id} "
                f"on route {request.url.path}"
            )
            
            return await call_next(request)
            
        except HTTPException as e:
            # Convert HTTPException to JSONResponse
            return JSONResponse(
                status_code=e.status_code,
                content={"detail": e.detail}
            )
        except Exception as e:
            logger.exception(f"Unexpected error in subscription middleware: {e}")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"detail": "Internal server error"}
            )
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit - cleanup resources."""
        if self._http_client:
            await self._http_client.aclose()
        if self._redis_client:
            await self._redis_client.aclose()
