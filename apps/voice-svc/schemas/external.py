"""
Pydantic schemas for external number registration (bring-your-own-number).

These schemas handle validation and serialization for:
- External number creation requests
- External number responses
- Verification code requests
- Status updates
"""
import re
from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field, field_validator

from packages.shared.models import ExternalNumberStatus


class ExternalNumberCreate(BaseModel):
    """Schema for creating a new external number registration."""

    did: str = Field(
        ...,
        description="Phone number in E.164 format (e.g., +15558675309)",
        example="+15558675309"
    )

    @field_validator('did')
    @classmethod
    def validate_did(cls, v):
        """Validate DID is in proper E.164 format and not premium."""
        if not v:
            raise ValueError("DID is required")

        # Remove any spaces or formatting
        clean_did = re.sub(r'[^\d+]', '', v)

        # Must start with + and be 10-15 digits
        if not re.match(r'^\+[1-9]\d{9,14}$', clean_did):
            raise ValueError("DID must be in E.164 format (e.g., +15558675309)")

        # Block premium numbers (900, 976, etc.)
        if re.match(r'^\+1(900|976|550)', clean_did):
            raise ValueError("Premium numbers are not allowed")

        return clean_did


class ExternalNumberResponse(BaseModel):
    """Schema for external number API responses."""

    id: UUID
    tenant_id: UUID
    did: str
    status: ExternalNumberStatus
    verification_code: Optional[str] = Field(
        None,
        description="6-digit verification code (only returned on creation)"
    )
    sip_uri: Optional[str] = Field(
        None,
        description="SIP URI for forwarding calls"
    )
    created_at: datetime
    verified_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ExternalNumberDTO(BaseModel):
    """Data transfer object for external numbers (without sensitive data)."""

    id: UUID
    did: str
    status: ExternalNumberStatus
    created_at: datetime
    verified_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class VerificationRequest(BaseModel):
    """Schema for verification code submission."""

    code: str = Field(
        ...,
        description="6-digit verification code",
        example="123456",
        min_length=6,
        max_length=6
    )

    @field_validator('code')
    @classmethod
    def validate_code(cls, v):
        """Validate verification code is 6 digits."""
        if not v:
            raise ValueError("Verification code is required")

        if not re.match(r'^\d{6}$', v):
            raise ValueError("Verification code must be exactly 6 digits")

        return v


class VerificationResponse(BaseModel):
    """Schema for verification response."""

    status: ExternalNumberStatus
    verified_at: Optional[datetime] = None
    message: str = Field(
        ...,
        description="Human-readable status message"
    )


class ExternalNumberListResponse(BaseModel):
    """Schema for listing external numbers."""

    numbers: list[ExternalNumberDTO]
    total: int = Field(
        ...,
        description="Total number of external numbers for the tenant"
    )


class SipUriResponse(BaseModel):
    """Schema for SIP URI generation response."""

    sip_uri: str = Field(
        ...,
        description="SIP URI for call forwarding",
        example="sip:+15558675309@voice-svc:5060"
    )
    verification_code: str = Field(
        ...,
        description="6-digit verification code to enter during test call",
        example="123456"
    )
    instructions: str = Field(
        ...,
        description="Instructions for setting up call forwarding"
    )
