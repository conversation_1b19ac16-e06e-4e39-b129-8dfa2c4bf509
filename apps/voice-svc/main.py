"""
Voice Service FastAPI Application
"""
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, Any

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, Response
import asyncio
import httpx
from prometheus_client import make_asgi_app

from avr.api.middleware.subscription_check import SubscriptionCheckMiddleware

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="AI Lex Voice Service",
    description="Voice service for AI Lex Receptionist",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add subscription check middleware
# Only add if environment variables are set (for production/staging)
core_api_url = os.getenv("CORE_SUBS_API_URL")
redis_url = os.getenv("REDIS_URL")

if core_api_url and redis_url:
    app.add_middleware(
        SubscriptionCheckMiddleware,
        core_api_url=core_api_url,
        redis_url=redis_url,
        cache_ttl=60
    )
    logger.info("Subscription check middleware enabled")
else:
    logger.warning("Subscription check middleware disabled - missing CORE_SUBS_API_URL or REDIS_URL")


# Initialize metrics service
try:
    from services.metrics_service import get_metrics_service
    metrics_service = get_metrics_service()
    logger.info("Metrics service initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize metrics service: {e}")

# Mount Prometheus metrics endpoint
try:
    # Use the metrics service registry
    metrics_app = make_asgi_app(registry=metrics_service.registry)
    app.mount("/metrics", metrics_app)
    logger.info("Metrics endpoint mounted at /metrics")
except Exception as e:
    logger.error(f"Failed to mount metrics endpoint: {e}")

# Startup and shutdown events
@app.on_event("startup")
async def startup_event():
    """Initialize services on startup."""
    try:
        # Start the purge scheduler
        from schedulers.purge import purge_scheduler
        await purge_scheduler.start()
        logger.info("Purge scheduler started successfully")
    except Exception as e:
        logger.error(f"Failed to start purge scheduler: {e}")

    try:
        # Start the queue worker
        from workers.queue_worker import get_queue_worker
        queue_worker = get_queue_worker()
        await queue_worker.start()
        logger.info("Queue worker started successfully")
    except Exception as e:
        logger.error(f"Failed to start queue worker: {e}")


@app.on_event("shutdown")
async def shutdown_event():
    """Clean up services on shutdown."""
    try:
        # Stop the queue worker
        from workers.queue_worker import get_queue_worker
        queue_worker = get_queue_worker()
        await queue_worker.stop()
        logger.info("Queue worker stopped successfully")
    except Exception as e:
        logger.error(f"Failed to stop queue worker: {e}")

    try:
        # Stop the purge scheduler
        from schedulers.purge import purge_scheduler
        await purge_scheduler.stop()
        logger.info("Purge scheduler stopped successfully")
    except Exception as e:
        logger.error(f"Failed to stop purge scheduler: {e}")


# Include existing router
try:
    from routes.api.calendar.google_push import router as google_push_router
    app.include_router(google_push_router, prefix="/api")
except ImportError:
    # Router not available, skip it
    logger.warning("Google push router not available")

# Include Telnyx routers
try:
    from routers.telnyx_numbers import router as telnyx_numbers_router
    from routers.telnyx_webhooks import router as telnyx_webhooks_router

    app.include_router(telnyx_numbers_router)
    app.include_router(telnyx_webhooks_router)
    logger.info("Telnyx routers loaded successfully")
except ImportError as e:
    logger.warning(f"Telnyx routers not available: {e}")

# Include recording router
try:
    from routers.recording import router as recording_router
    app.include_router(recording_router)
    logger.info("Recording router loaded successfully")
except ImportError as e:
    logger.warning(f"Recording router not available: {e}")

# Include External Numbers router
try:
    from routers.external_numbers import router as external_numbers_router

    app.include_router(external_numbers_router)
    logger.info("External numbers router loaded successfully")
except ImportError as e:
    logger.warning(f"External numbers router not available: {e}")

# Include Queue router
try:
    from routers.queue import router as queue_router
    app.include_router(queue_router)
    logger.info("Queue router loaded successfully")
except ImportError as e:
    logger.warning(f"Queue router not available: {e}")


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "ok",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "AI Lex Voice Service",
        "version": "1.0.0"
    }


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "Welcome to AI Lex Voice Service API",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.post("/ivr/call")
async def ivr_call(request: Request) -> Dict[str, Any]:
    """
    IVR call endpoint for voice interactions.

    This endpoint processes IVR calls and can optionally call Core AiLex
    calendar services for booking intents.
    """
    start_time = datetime.utcnow()

    try:
        # Get request body
        body = await request.json() if request.headers.get("content-type") == "application/json" else {}

        call_id = body.get("call_id", f"call_{int(start_time.timestamp() * 1000)}")
        intent = body.get("intent")
        provider = body.get("provider")
        tenant_id = body.get("tenant_id")

        # If this is a booking intent, call Core AiLex services
        if intent == "book_meeting" and provider and tenant_id:
            booking_result = await _handle_booking_intent(call_id, provider, tenant_id)
            return {
                "call_id": call_id,
                "status": "processed",
                "intent": intent,
                "provider": provider,
                "tenant_id": tenant_id,
                "booking_result": booking_result,
                "timestamp": start_time.isoformat()
            }

        # Default simulation for other intents
        processing_time = 0.1 + (hash(str(body)) % 100) / 1000  # 100-200ms
        await asyncio.sleep(processing_time)

        response_data = {
            "call_id": call_id,
            "status": "processed",
            "message": "Call processed successfully",
            "processing_time_ms": int(processing_time * 1000),
            "timestamp": start_time.isoformat(),
            "flow": "intake",
            "language": body.get("language", "en"),
            "caller_info": {
                "phone": body.get("phone", "+**********"),
                "session_id": body.get("session_id", f"session_{int(start_time.timestamp())}")
            }
        }

        logger.info(f"Processed IVR call in {processing_time*1000:.1f}ms")
        return response_data

    except Exception as e:
        logger.error(f"Error processing IVR call: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


async def _handle_booking_intent(call_id: str, provider: str, tenant_id: str) -> Dict[str, Any]:
    """
    Handle booking intent by calling Core AiLex calendar services.

    Args:
        call_id: Unique call identifier
        provider: Calendar provider (e.g., 'outlook', 'google')
        tenant_id: Tenant/firm identifier

    Returns:
        Dictionary containing booking results and webhook response
    """
    calendar_svc_base = os.getenv("CALENDAR_SVC_BASE", "http://localhost:8001")
    webhook_url = os.getenv("IVR_WEBHOOK_URL", "http://localhost:9000/ivr/webhook")

    try:
        # Step 1: Get availability
        availability_result = await _get_availability(calendar_svc_base, provider, tenant_id)

        # Step 2: Book first available slot
        booking_result = await _book_slot(calendar_svc_base, provider, tenant_id, call_id)

        # Step 3: Send webhook
        webhook_result = await _send_webhook(webhook_url, call_id, booking_result)

        # Step 4: Send intake event to Core AiLex
        intake_result = await _send_intake_event_for_booking(call_id, provider, tenant_id, booking_result)

        return {
            "availability_called": True,
            "booking_called": True,
            "webhook_called": True,
            "intake_sent": True,
            "availability_result": availability_result,
            "booking_result": booking_result,
            "webhook_result": webhook_result,
            "intake_result": intake_result
        }

    except Exception as e:
        logger.error(f"Error in booking intent: {e}")
        raise


async def _get_availability(calendar_svc_base: str, provider: str, tenant_id: str) -> Dict[str, Any]:
    """Get availability from calendar service."""
    now = datetime.utcnow()
    time_min = now.replace(hour=9, minute=0, second=0, microsecond=0)
    time_max = time_min + timedelta(hours=8)  # 9 AM to 5 PM

    payload = {
        "calendar_ids": ["primary"],
        "time_min": time_min.isoformat() + "Z",
        "time_max": time_max.isoformat() + "Z",
        "time_zone": "UTC"
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{calendar_svc_base}/api/v1/availability",
            json=payload,
            params={"provider": provider},
            headers={"Authorization": f"Bearer test-token-{tenant_id}"}
        )
        response.raise_for_status()
        return response.json()


async def _book_slot(calendar_svc_base: str, provider: str, tenant_id: str, call_id: str) -> Dict[str, Any]:
    """Book a time slot."""
    # Book a 30-minute slot starting in 1 hour
    start_time = datetime.utcnow() + timedelta(hours=1)
    end_time = start_time + timedelta(minutes=30)

    payload = {
        "provider": provider,
        "calendar_id": "primary",
        "summary": "Voice Consultation",
        "start_at": start_time.isoformat() + "Z",
        "end_at": end_time.isoformat() + "Z"
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{calendar_svc_base}/api/v1/book",
            json=payload,
            headers={
                "Authorization": f"Bearer test-token-{tenant_id}",
                "X-Call-ID": call_id
            }
        )
        response.raise_for_status()
        return response.json()


async def _send_webhook(webhook_url: str, call_id: str, booking_data: Dict[str, Any]) -> Dict[str, Any]:
    """Send webhook to IVR system."""
    webhook_payload = {
        "event": "booking_confirmed",
        "call_id": call_id,
        "booking_id": booking_data.get("id"),
        "provider_event_link": booking_data.get("provider_event_link"),
        "start_at": booking_data.get("start_at"),
        "booked_at": booking_data.get("booked_at"),
        "metadata": {
            "caller_source": "voice_service",
            "provider": booking_data.get("provider")
        }
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(
            webhook_url,
            json=webhook_payload,
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        return response.json()


async def _send_intake_event_for_booking(call_id: str, provider: str, tenant_id: str, booking_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Send intake event to Core AiLex for a booking webhook.

    Args:
        call_id: Call identifier
        provider: Calendar provider
        tenant_id: Tenant identifier
        booking_data: Booking information

    Returns:
        Dictionary with intake delivery result
    """
    try:
        # Import services (lazy import to avoid circular dependencies)
        from services.intake_webhook import get_intake_webhook_service
        from services.intake_event_builder import get_intake_event_builder

        intake_service = get_intake_webhook_service()
        event_builder = get_intake_event_builder()

        # Build intake event for booking
        intake_event = event_builder.build_booking_webhook_event(
            call_id=call_id,
            tenant_id=tenant_id,
            caller_number="+**********",  # Placeholder - would come from booking data
            booking_data=booking_data,
            caller_name=None,  # Would extract from booking data if available
            transcript="Booking created via voice service"
        )

        # Send to Core AiLex
        result = await intake_service.send_intake_event(intake_event)

        return {
            "success": result.success,
            "status_code": result.status_code,
            "retry_scheduled": result.retry_scheduled,
            "error_message": result.error_message
        }

    except Exception as e:
        logger.error(f"Failed to send intake event for booking {call_id}: {e}", exc_info=True)
        return {
            "success": False,
            "error_message": str(e),
            "retry_scheduled": False
        }


# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail},
    )


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Handle all other exceptions."""
    logger.exception("Unhandled exception")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"},
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
