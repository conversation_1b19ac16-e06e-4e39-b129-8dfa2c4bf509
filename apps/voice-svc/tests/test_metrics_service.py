"""
Unit tests for the metrics service.
"""
import pytest
from unittest.mock import patch
from prometheus_client import CollectorRegistry, CONTENT_TYPE_LATEST

from services.metrics_service import MetricsService, get_metrics_service, reset_metrics_service


class TestMetricsService:
    """Test cases for MetricsService."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Use a custom registry for testing to avoid conflicts
        self.registry = CollectorRegistry()
        self.metrics_service = MetricsService(registry=self.registry)
    
    def test_metrics_service_initialization(self):
        """Test that metrics service initializes correctly."""
        assert self.metrics_service.registry is not None
        assert hasattr(self.metrics_service, 'call_latency')
        assert hasattr(self.metrics_service, 'queue_depth')
        assert hasattr(self.metrics_service, 'active_calls')
        assert hasattr(self.metrics_service, 'calls_rejected')
        assert hasattr(self.metrics_service, 'recordings_purged')
        assert hasattr(self.metrics_service, 'webhook_retries')
        assert hasattr(self.metrics_service, 'job_duration')
    
    def test_record_call_latency(self):
        """Test recording call latency metrics."""
        tenant_id = "test-tenant"
        latency = 0.5
        
        self.metrics_service.record_call_latency(tenant_id, latency)
        
        # Verify metric was recorded
        metric_families = list(self.registry.collect())
        latency_metric = next(
            (mf for mf in metric_families if mf.name == 'avr_call_latency_seconds'), 
            None
        )
        assert latency_metric is not None
        assert len(latency_metric.samples) > 0
    
    def test_update_queue_depth(self):
        """Test updating queue depth metrics."""
        tenant_id = "test-tenant"
        depth = 5
        
        self.metrics_service.update_queue_depth(tenant_id, depth)
        
        # Verify metric was updated
        metric_families = list(self.registry.collect())
        queue_metric = next(
            (mf for mf in metric_families if mf.name == 'avr_queue_depth'), 
            None
        )
        assert queue_metric is not None
        assert len(queue_metric.samples) > 0
        
        # Find the sample for our tenant
        tenant_sample = next(
            (s for s in queue_metric.samples if s.labels.get('tenant_id') == tenant_id),
            None
        )
        assert tenant_sample is not None
        assert tenant_sample.value == depth
    
    def test_update_active_calls(self):
        """Test updating active calls metrics."""
        tenant_id = "test-tenant"
        count = 3
        
        self.metrics_service.update_active_calls(tenant_id, count)
        
        # Verify metric was updated
        metric_families = list(self.registry.collect())
        active_metric = next(
            (mf for mf in metric_families if mf.name == 'avr_active_calls'), 
            None
        )
        assert active_metric is not None
        
        # Find the sample for our tenant
        tenant_sample = next(
            (s for s in active_metric.samples if s.labels.get('tenant_id') == tenant_id),
            None
        )
        assert tenant_sample is not None
        assert tenant_sample.value == count
    
    def test_increment_calls_rejected(self):
        """Test incrementing rejected calls counter."""
        tenant_id = "test-tenant"
        reason = "queue_full"

        # Increment twice to test counter behavior
        self.metrics_service.increment_calls_rejected(tenant_id, reason)
        self.metrics_service.increment_calls_rejected(tenant_id, reason)

        # Verify counter was incremented
        metric_families = list(self.registry.collect())
        rejected_metric = next(
            (mf for mf in metric_families if mf.name == 'avr_calls_rejected'),
            None
        )
        assert rejected_metric is not None

        # Find the sample for our tenant and reason (counter adds _total suffix)
        tenant_sample = next(
            (s for s in rejected_metric.samples
             if s.name == 'avr_calls_rejected_total' and
                s.labels.get('tenant_id') == tenant_id and s.labels.get('reason') == reason),
            None
        )
        assert tenant_sample is not None
        assert tenant_sample.value == 2.0
    
    def test_increment_recordings_purged(self):
        """Test incrementing purged recordings counter."""
        tenant_id = "test-tenant"
        count = 5

        self.metrics_service.increment_recordings_purged(tenant_id, count)

        # Verify counter was incremented
        metric_families = list(self.registry.collect())
        purged_metric = next(
            (mf for mf in metric_families if mf.name == 'avr_call_recordings_purged'),
            None
        )
        assert purged_metric is not None

        # Find the sample for our tenant (counter adds _total suffix)
        tenant_sample = next(
            (s for s in purged_metric.samples
             if s.name == 'avr_call_recordings_purged_total' and s.labels.get('tenant_id') == tenant_id),
            None
        )
        assert tenant_sample is not None
        assert tenant_sample.value == count
    
    def test_increment_webhook_retries(self):
        """Test incrementing webhook retry counter."""
        tenant_id = "test-tenant"
        webhook_type = "booking"

        self.metrics_service.increment_webhook_retries(tenant_id, webhook_type)

        # Verify counter was incremented
        metric_families = list(self.registry.collect())
        retry_metric = next(
            (mf for mf in metric_families if mf.name == 'avr_webhook_retry'),
            None
        )
        assert retry_metric is not None

        # Find the sample for our tenant and webhook type (counter adds _total suffix)
        tenant_sample = next(
            (s for s in retry_metric.samples
             if s.name == 'avr_webhook_retry_total' and
                s.labels.get('tenant_id') == tenant_id and s.labels.get('webhook_type') == webhook_type),
            None
        )
        assert tenant_sample is not None
        assert tenant_sample.value == 1.0
    
    def test_record_job_duration(self):
        """Test recording job duration metrics."""
        job_name = "test_job"
        duration = 2.5
        
        self.metrics_service.record_job_duration(job_name, duration)
        
        # Verify metric was recorded
        metric_families = list(self.registry.collect())
        duration_metric = next(
            (mf for mf in metric_families if mf.name == 'avr_job_duration_seconds'), 
            None
        )
        assert duration_metric is not None
        assert len(duration_metric.samples) > 0
    
    def test_time_job_context_manager(self):
        """Test the time_job context manager."""
        job_name = "test_context_job"
        
        with self.metrics_service.time_job(job_name):
            # Simulate some work
            import time
            time.sleep(0.01)
        
        # Verify metric was recorded
        metric_families = list(self.registry.collect())
        duration_metric = next(
            (mf for mf in metric_families if mf.name == 'avr_job_duration_seconds'), 
            None
        )
        assert duration_metric is not None
        
        # Find samples for our job
        job_samples = [
            s for s in duration_metric.samples 
            if s.labels.get('job') == job_name
        ]
        assert len(job_samples) > 0
    
    def test_time_call_latency_context_manager(self):
        """Test the time_call_latency context manager."""
        tenant_id = "test-tenant"
        
        with self.metrics_service.time_call_latency(tenant_id):
            # Simulate some work
            import time
            time.sleep(0.01)
        
        # Verify metric was recorded
        metric_families = list(self.registry.collect())
        latency_metric = next(
            (mf for mf in metric_families if mf.name == 'avr_call_latency_seconds'), 
            None
        )
        assert latency_metric is not None
        assert len(latency_metric.samples) > 0
    
    def test_get_metrics_data(self):
        """Test getting Prometheus metrics data."""
        # Record some metrics first
        self.metrics_service.update_queue_depth("test-tenant", 5)
        
        metrics_data = self.metrics_service.get_metrics_data()
        
        assert isinstance(metrics_data, bytes)
        assert b'avr_queue_depth' in metrics_data
        assert b'test-tenant' in metrics_data
    
    def test_get_content_type(self):
        """Test getting Prometheus content type."""
        content_type = self.metrics_service.get_content_type()
        assert content_type == CONTENT_TYPE_LATEST
    
    def test_get_stats_summary(self):
        """Test getting metrics summary."""
        summary = self.metrics_service.get_stats_summary()
        
        assert isinstance(summary, dict)
        assert summary['metrics_initialized'] is True
        assert 'registry_collectors' in summary
        assert 'available_metrics' in summary
        assert len(summary['available_metrics']) == 7


class TestMetricsServiceGlobal:
    """Test cases for global metrics service functions."""
    
    def setup_method(self):
        """Reset global state before each test."""
        reset_metrics_service()
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_metrics_service()
    
    def test_get_metrics_service_singleton(self):
        """Test that get_metrics_service returns a singleton."""
        service1 = get_metrics_service()
        service2 = get_metrics_service()
        
        assert service1 is service2
        assert isinstance(service1, MetricsService)
    
    def test_reset_metrics_service(self):
        """Test resetting the global metrics service."""
        service1 = get_metrics_service()
        reset_metrics_service()
        service2 = get_metrics_service()
        
        assert service1 is not service2
