"""
Tests for Telnyx forwarding webhook functionality.
"""
import pytest
import json
import hmac
import hashlib
from datetime import datetime, timezone
from uuid import uuid4

from packages.shared.models import TelnyxNumber, TelnyxNumberStatus, TelnyxConnectionStatus


class TestForwardingWebhooks:
    """Test forwarding webhook handlers."""

    @pytest.fixture
    def webhook_secret(self):
        """Test webhook secret."""
        return "test_webhook_secret"

    @pytest.fixture
    def tenant_id(self):
        """Generate a test tenant ID."""
        return str(uuid4())

    @pytest.fixture
    async def test_number(self, db_session, tenant_id):
        """Create a test phone number."""
        number = TelnyxNumber(
            tenant_id=tenant_id,
            did="+18325550123",
            state="TX",
            status=TelnyxNumberStatus.ACTIVE,
            connection_status=TelnyxConnectionStatus.SIP,
            verification_required=False,
            telnyx_number_id="telnyx_123",
            metadata_={}
        )
        db_session.add(number)
        await db_session.commit()
        await db_session.refresh(number)
        return number

    def create_webhook_signature(self, payload: str, secret: str) -> str:
        """Create a valid webhook signature."""
        return hmac.new(
            secret.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    async def test_number_configuration_webhook_forwarding(
        self, test_client, test_number, webhook_secret, db_session
    ):
        """Test webhook for number configuration update to forwarding."""
        webhook_payload = {
            "event_type": "phone_number.updated",
            "id": "webhook_123",
            "occurred_at": datetime.now(timezone.utc).isoformat(),
            "data": {
                "phone_number": "+18325550123",
                "connection_id": "connection_456",
                "call_forwarding": {
                    "phone_number": "+15558675309"
                },
                "status": "active"
            }
        }
        
        payload_str = json.dumps(webhook_payload)
        signature = self.create_webhook_signature(payload_str, webhook_secret)
        
        # Mock environment variable
        with pytest.MonkeyPatch().context() as m:
            m.setenv("TELNYX_WEBHOOK_SECRET", webhook_secret)
            
            response = test_client.post(
                "/webhooks/telnyx/number-configuration",
                content=payload_str,
                headers={
                    "Content-Type": "application/json",
                    "X-Telnyx-Signature": signature
                }
            )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "processed"
        assert data["event_type"] == "phone_number.updated"
        
        # Verify database update
        await db_session.refresh(test_number)
        assert test_number.connection_status == TelnyxConnectionStatus.FORWARDING
        assert test_number.forwarding_number == "+15558675309"
        assert test_number.verification_required is True
        assert test_number.telnyx_connection_id == "connection_456"

    async def test_number_configuration_webhook_sip(
        self, test_client, test_number, webhook_secret, db_session
    ):
        """Test webhook for number configuration update to SIP."""
        webhook_payload = {
            "event_type": "phone_number.updated",
            "id": "webhook_124",
            "occurred_at": datetime.now(timezone.utc).isoformat(),
            "data": {
                "phone_number": "+18325550123",
                "connection_id": "app_789",
                "call_forwarding": None,
                "status": "active"
            }
        }
        
        payload_str = json.dumps(webhook_payload)
        signature = self.create_webhook_signature(payload_str, webhook_secret)
        
        # Mock environment variable
        with pytest.MonkeyPatch().context() as m:
            m.setenv("TELNYX_WEBHOOK_SECRET", webhook_secret)
            
            response = test_client.post(
                "/webhooks/telnyx/number-configuration",
                content=payload_str,
                headers={
                    "Content-Type": "application/json",
                    "X-Telnyx-Signature": signature
                }
            )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "processed"
        
        # Verify database update
        await db_session.refresh(test_number)
        assert test_number.connection_status == TelnyxConnectionStatus.SIP
        assert test_number.forwarding_number is None
        assert test_number.verification_required is False
        assert test_number.telnyx_connection_id == "app_789"

    async def test_number_configuration_webhook_provisioned(
        self, test_client, test_number, webhook_secret, db_session
    ):
        """Test webhook for number in provisioned state."""
        webhook_payload = {
            "event_type": "phone_number.updated",
            "id": "webhook_125",
            "occurred_at": datetime.now(timezone.utc).isoformat(),
            "data": {
                "phone_number": "+18325550123",
                "connection_id": None,
                "call_forwarding": None,
                "status": "provisioned"
            }
        }
        
        payload_str = json.dumps(webhook_payload)
        signature = self.create_webhook_signature(payload_str, webhook_secret)
        
        # Mock environment variable
        with pytest.MonkeyPatch().context() as m:
            m.setenv("TELNYX_WEBHOOK_SECRET", webhook_secret)
            
            response = test_client.post(
                "/webhooks/telnyx/number-configuration",
                content=payload_str,
                headers={
                    "Content-Type": "application/json",
                    "X-Telnyx-Signature": signature
                }
            )
        
        assert response.status_code == 200
        
        # Verify database update
        await db_session.refresh(test_number)
        assert test_number.connection_status == TelnyxConnectionStatus.PROVISIONED
        assert test_number.verification_required is False

    async def test_connection_change_webhook(
        self, test_client, test_number, webhook_secret, db_session
    ):
        """Test webhook for connection status changes."""
        webhook_payload = {
            "event_type": "phone_number.connection_changed",
            "id": "webhook_126",
            "occurred_at": datetime.now(timezone.utc).isoformat(),
            "data": {
                "phone_number": "+18325550123",
                "connection_status": "active",
                "previous_status": "provisioned"
            }
        }
        
        payload_str = json.dumps(webhook_payload)
        signature = self.create_webhook_signature(payload_str, webhook_secret)
        
        # Mock environment variable
        with pytest.MonkeyPatch().context() as m:
            m.setenv("TELNYX_WEBHOOK_SECRET", webhook_secret)
            
            response = test_client.post(
                "/webhooks/telnyx/number-configuration",
                content=payload_str,
                headers={
                    "Content-Type": "application/json",
                    "X-Telnyx-Signature": signature
                }
            )
        
        assert response.status_code == 200
        
        # Verify metadata update
        await db_session.refresh(test_number)
        assert "connection_change_event" in test_number.metadata_

    async def test_webhook_number_not_found(self, test_client, webhook_secret):
        """Test webhook for number not in our database."""
        webhook_payload = {
            "event_type": "phone_number.updated",
            "id": "webhook_127",
            "occurred_at": datetime.now(timezone.utc).isoformat(),
            "data": {
                "phone_number": "+19995551234",  # Not in our database
                "connection_id": "connection_456",
                "call_forwarding": {
                    "phone_number": "+15558675309"
                }
            }
        }
        
        payload_str = json.dumps(webhook_payload)
        signature = self.create_webhook_signature(payload_str, webhook_secret)
        
        # Mock environment variable
        with pytest.MonkeyPatch().context() as m:
            m.setenv("TELNYX_WEBHOOK_SECRET", webhook_secret)
            
            response = test_client.post(
                "/webhooks/telnyx/number-configuration",
                content=payload_str,
                headers={
                    "Content-Type": "application/json",
                    "X-Telnyx-Signature": signature
                }
            )
        
        # Should still return 200 but log warning
        assert response.status_code == 200

    async def test_webhook_invalid_signature(self, test_client, webhook_secret):
        """Test webhook with invalid signature."""
        webhook_payload = {
            "event_type": "phone_number.updated",
            "id": "webhook_128",
            "occurred_at": datetime.now(timezone.utc).isoformat(),
            "data": {
                "phone_number": "+18325550123"
            }
        }
        
        payload_str = json.dumps(webhook_payload)
        invalid_signature = "invalid_signature"
        
        # Mock environment variable
        with pytest.MonkeyPatch().context() as m:
            m.setenv("TELNYX_WEBHOOK_SECRET", webhook_secret)
            
            response = test_client.post(
                "/webhooks/telnyx/number-configuration",
                content=payload_str,
                headers={
                    "Content-Type": "application/json",
                    "X-Telnyx-Signature": invalid_signature
                }
            )
        
        assert response.status_code == 400
        assert "Invalid signature" in response.json()["detail"]

    async def test_webhook_unhandled_event_type(self, test_client, webhook_secret):
        """Test webhook with unhandled event type."""
        webhook_payload = {
            "event_type": "phone_number.unknown_event",
            "id": "webhook_129",
            "occurred_at": datetime.now(timezone.utc).isoformat(),
            "data": {
                "phone_number": "+18325550123"
            }
        }
        
        payload_str = json.dumps(webhook_payload)
        signature = self.create_webhook_signature(payload_str, webhook_secret)
        
        # Mock environment variable
        with pytest.MonkeyPatch().context() as m:
            m.setenv("TELNYX_WEBHOOK_SECRET", webhook_secret)
            
            response = test_client.post(
                "/webhooks/telnyx/number-configuration",
                content=payload_str,
                headers={
                    "Content-Type": "application/json",
                    "X-Telnyx-Signature": signature
                }
            )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "ignored"
        assert "Unhandled event type" in data["reason"]

    async def test_webhook_invalid_json(self, test_client, webhook_secret):
        """Test webhook with invalid JSON payload."""
        invalid_payload = "invalid json"
        signature = self.create_webhook_signature(invalid_payload, webhook_secret)
        
        # Mock environment variable
        with pytest.MonkeyPatch().context() as m:
            m.setenv("TELNYX_WEBHOOK_SECRET", webhook_secret)
            
            response = test_client.post(
                "/webhooks/telnyx/number-configuration",
                content=invalid_payload,
                headers={
                    "Content-Type": "application/json",
                    "X-Telnyx-Signature": signature
                }
            )
        
        assert response.status_code == 400
        assert "Invalid JSON payload" in response.json()["detail"]

    async def test_webhook_missing_signature(self, test_client, webhook_secret):
        """Test webhook without signature header."""
        webhook_payload = {
            "event_type": "phone_number.updated",
            "id": "webhook_130",
            "occurred_at": datetime.now(timezone.utc).isoformat(),
            "data": {
                "phone_number": "+18325550123"
            }
        }
        
        payload_str = json.dumps(webhook_payload)
        
        # Mock environment variable
        with pytest.MonkeyPatch().context() as m:
            m.setenv("TELNYX_WEBHOOK_SECRET", webhook_secret)
            
            response = test_client.post(
                "/webhooks/telnyx/number-configuration",
                content=payload_str,
                headers={"Content-Type": "application/json"}
                # No X-Telnyx-Signature header
            )
        
        assert response.status_code == 400
        assert "Invalid signature" in response.json()["detail"]
