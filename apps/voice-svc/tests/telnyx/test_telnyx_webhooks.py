"""
Tests for Telnyx webhook handlers.
"""
import json
import hmac
import hashlib
import pytest
from uuid import UUID

from packages.shared.models import TelnyxNumber, TelnyxNumberStatus


class TestTelnyxWebhooks:
    """Test cases for Telnyx webhook handlers."""
    
    def create_webhook_signature(self, payload: bytes, secret: str) -> str:
        """Create a valid webhook signature for testing."""
        signature = hmac.new(
            secret.encode('utf-8'),
            payload,
            hashlib.sha256
        ).hexdigest()
        return f"t=1234567890,v1={signature}"
    
    async def test_number_activated_webhook_success(self, test_client, test_db, tenant_id, sample_webhook_payload):
        """Test successful number activation webhook."""
        # Create pending number in database
        pending_number = TelnyxNumber(
            tenant_id=UUID(tenant_id),
            did="+18325550123",
            state="TX",
            status=TelnyxNumberStatus.PENDING
        )
        test_db.add(pending_number)
        await test_db.commit()
        
        # Prepare webhook payload
        payload_json = json.dumps(sample_webhook_payload)
        payload_bytes = payload_json.encode('utf-8')
        signature = self.create_webhook_signature(payload_bytes, "test_webhook_secret")
        
        # Send webhook
        response = test_client.post(
            "/webhooks/telnyx/number-activated",
            content=payload_bytes,
            headers={
                "Content-Type": "application/json",
                "X-Telnyx-Signature": signature
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "ok"
        assert data["event_type"] == "phone_number.created"
        
        # Verify database was updated
        await test_db.refresh(pending_number)
        assert pending_number.status == TelnyxNumberStatus.ACTIVE
        assert pending_number.telnyx_number_id == "telnyx_number_123"
    
    def test_webhook_invalid_signature(self, test_client, sample_webhook_payload):
        """Test webhook with invalid signature."""
        payload_json = json.dumps(sample_webhook_payload)
        payload_bytes = payload_json.encode('utf-8')
        
        response = test_client.post(
            "/webhooks/telnyx/number-activated",
            content=payload_bytes,
            headers={
                "Content-Type": "application/json",
                "X-Telnyx-Signature": "t=1234567890,v1=invalid_signature"
            }
        )
        
        assert response.status_code == 400
        assert "Invalid signature" in response.json()["detail"]
    
    def test_webhook_missing_signature(self, test_client, sample_webhook_payload):
        """Test webhook without signature header."""
        payload_json = json.dumps(sample_webhook_payload)
        payload_bytes = payload_json.encode('utf-8')
        
        response = test_client.post(
            "/webhooks/telnyx/number-activated",
            content=payload_bytes,
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 400
        assert "Invalid signature" in response.json()["detail"]
    
    async def test_webhook_unknown_number(self, test_client, sample_webhook_payload):
        """Test webhook for number not in our database."""
        # Modify payload to use unknown number
        payload = sample_webhook_payload.copy()
        payload["data"]["phone_number"] = "+19995550999"
        
        payload_json = json.dumps(payload)
        payload_bytes = payload_json.encode('utf-8')
        signature = self.create_webhook_signature(payload_bytes, "test_webhook_secret")
        
        response = test_client.post(
            "/webhooks/telnyx/number-activated",
            content=payload_bytes,
            headers={
                "Content-Type": "application/json",
                "X-Telnyx-Signature": signature
            }
        )
        
        # Should still return 200 but log warning
        assert response.status_code == 200
    
    def test_webhook_invalid_json(self, test_client):
        """Test webhook with invalid JSON payload."""
        payload_bytes = b"invalid json"
        signature = self.create_webhook_signature(payload_bytes, "test_webhook_secret")
        
        response = test_client.post(
            "/webhooks/telnyx/number-activated",
            content=payload_bytes,
            headers={
                "Content-Type": "application/json",
                "X-Telnyx-Signature": signature
            }
        )
        
        assert response.status_code == 400
        assert "Invalid JSON" in response.json()["detail"]
    
    async def test_phone_number_updated_webhook(self, test_client, test_db, tenant_id):
        """Test phone number updated webhook."""
        # Create active number in database
        active_number = TelnyxNumber(
            tenant_id=UUID(tenant_id),
            did="+18325550123",
            state="TX",
            status=TelnyxNumberStatus.ACTIVE
        )
        test_db.add(active_number)
        await test_db.commit()
        
        # Create update webhook payload
        update_payload = {
            "data": {
                "id": "telnyx_number_123",
                "phone_number": "+18325550123",
                "status": "inactive",
                "connection_id": None,
                "customer_reference": tenant_id,
                "created_at": "2024-12-19T10:00:00Z",
                "updated_at": "2024-12-19T11:00:00Z"
            },
            "event_type": "phone_number.updated",
            "id": "webhook_event_124",
            "occurred_at": "2024-12-19T11:00:00Z",
            "record_type": "event"
        }
        
        payload_json = json.dumps(update_payload)
        payload_bytes = payload_json.encode('utf-8')
        signature = self.create_webhook_signature(payload_bytes, "test_webhook_secret")
        
        response = test_client.post(
            "/webhooks/telnyx/number-activated",
            content=payload_bytes,
            headers={
                "Content-Type": "application/json",
                "X-Telnyx-Signature": signature
            }
        )
        
        assert response.status_code == 200
        
        # Verify database was updated
        await test_db.refresh(active_number)
        assert active_number.status == TelnyxNumberStatus.INACTIVE
    
    async def test_phone_number_deleted_webhook(self, test_client, test_db, tenant_id):
        """Test phone number deleted webhook."""
        # Create active number in database
        active_number = TelnyxNumber(
            tenant_id=UUID(tenant_id),
            did="+18325550123",
            state="TX",
            status=TelnyxNumberStatus.ACTIVE
        )
        test_db.add(active_number)
        await test_db.commit()
        
        # Create deletion webhook payload
        delete_payload = {
            "data": {
                "id": "telnyx_number_123",
                "phone_number": "+18325550123",
                "status": "deleted",
                "connection_id": None,
                "customer_reference": tenant_id,
                "created_at": "2024-12-19T10:00:00Z",
                "updated_at": "2024-12-19T12:00:00Z"
            },
            "event_type": "phone_number.deleted",
            "id": "webhook_event_125",
            "occurred_at": "2024-12-19T12:00:00Z",
            "record_type": "event"
        }
        
        payload_json = json.dumps(delete_payload)
        payload_bytes = payload_json.encode('utf-8')
        signature = self.create_webhook_signature(payload_bytes, "test_webhook_secret")
        
        response = test_client.post(
            "/webhooks/telnyx/number-activated",
            content=payload_bytes,
            headers={
                "Content-Type": "application/json",
                "X-Telnyx-Signature": signature
            }
        )
        
        assert response.status_code == 200
        
        # Verify database was updated (marked as inactive, not deleted)
        await test_db.refresh(active_number)
        assert active_number.status == TelnyxNumberStatus.INACTIVE
    
    def test_unhandled_event_type(self, test_client, sample_webhook_payload):
        """Test webhook with unhandled event type."""
        # Modify payload to use unhandled event type
        payload = sample_webhook_payload.copy()
        payload["event_type"] = "phone_number.unknown_event"
        
        payload_json = json.dumps(payload)
        payload_bytes = payload_json.encode('utf-8')
        signature = self.create_webhook_signature(payload_bytes, "test_webhook_secret")
        
        response = test_client.post(
            "/webhooks/telnyx/number-activated",
            content=payload_bytes,
            headers={
                "Content-Type": "application/json",
                "X-Telnyx-Signature": signature
            }
        )
        
        # Should still return 200 for unhandled events
        assert response.status_code == 200
        data = response.json()
        assert data["event_type"] == "phone_number.unknown_event"
