"""
Unit tests for Telnyx forwarding service functionality.
"""
import pytest
import respx
import httpx
from unittest.mock import AsyncMock, patch

from services.telnyx_service import TelnyxService, TelnyxAPIError


class TestTelnyxForwardingService:
    """Test Telnyx service forwarding functionality."""

    @pytest.fixture
    def telnyx_service(self):
        """Create TelnyxService instance for testing."""
        with patch.dict('os.environ', {
            'TELNYX_API_KEY': 'test_api_key',
            'TELNYX_APP_ID': 'test_app_id'
        }):
            return TelnyxService()

    @respx.mock
    async def test_attach_forwarding_success(self, telnyx_service):
        """Test successful call forwarding configuration."""
        did = "+18325550123"
        forward_to = "+15558675309"
        
        # Mock phone number search response
        search_response = {
            "data": [
                {
                    "id": "phone_number_123",
                    "phone_number": did,
                    "status": "active"
                }
            ]
        }
        
        # Mock configuration update response
        config_response = {
            "data": {
                "id": "phone_number_123",
                "phone_number": did,
                "connection_id": "connection_456",
                "call_forwarding": {
                    "phone_number": forward_to
                }
            }
        }
        
        # Mock API calls
        respx.get(
            "https://api.telnyx.com/v2/phone_numbers"
        ).mock(return_value=httpx.Response(200, json=search_response))
        
        respx.patch(
            "https://api.telnyx.com/v2/phone_numbers/phone_number_123"
        ).mock(return_value=httpx.Response(200, json=config_response))
        
        # Test the forwarding configuration
        result = await telnyx_service.attach_forwarding(did, forward_to)
        
        assert result["id"] == "phone_number_123"
        assert result["call_forwarding"]["phone_number"] == forward_to

    @respx.mock
    async def test_attach_forwarding_number_not_found(self, telnyx_service):
        """Test forwarding configuration when number is not found."""
        did = "+18325550123"
        forward_to = "+15558675309"
        
        # Mock empty search response
        search_response = {"data": []}
        
        respx.get(
            "https://api.telnyx.com/v2/phone_numbers"
        ).mock(return_value=httpx.Response(200, json=search_response))
        
        # Test should raise TelnyxAPIError
        with pytest.raises(TelnyxAPIError) as exc_info:
            await telnyx_service.attach_forwarding(did, forward_to)
        
        assert "not found in Telnyx" in str(exc_info.value)

    async def test_attach_forwarding_invalid_did(self, telnyx_service):
        """Test forwarding with invalid DID format."""
        invalid_did = "8325550123"  # Missing +
        forward_to = "+15558675309"
        
        with pytest.raises(ValueError) as exc_info:
            await telnyx_service.attach_forwarding(invalid_did, forward_to)
        
        assert "DID must be in E.164 format" in str(exc_info.value)

    async def test_attach_forwarding_invalid_forward_to(self, telnyx_service):
        """Test forwarding with invalid forward-to number."""
        did = "+18325550123"
        invalid_forward_to = "5558675309"  # Missing +
        
        with pytest.raises(ValueError) as exc_info:
            await telnyx_service.attach_forwarding(did, invalid_forward_to)
        
        assert "Forward-to number must be in E.164 format" in str(exc_info.value)

    async def test_attach_forwarding_self_forwarding(self, telnyx_service):
        """Test prevention of self-forwarding."""
        did = "+18325550123"
        
        with pytest.raises(ValueError) as exc_info:
            await telnyx_service.attach_forwarding(did, did)
        
        assert "Cannot forward a number to itself" in str(exc_info.value)

    async def test_attach_forwarding_premium_rate_blocked(self, telnyx_service):
        """Test blocking of premium rate numbers."""
        did = "+18325550123"
        premium_number = "+19005551234"
        
        with pytest.raises(ValueError) as exc_info:
            await telnyx_service.attach_forwarding(did, premium_number)
        
        assert "Cannot forward to premium rate numbers" in str(exc_info.value)

    @respx.mock
    async def test_attach_sip_app_success(self, telnyx_service):
        """Test successful SIP application attachment."""
        did = "+18325550123"
        
        # Mock phone number search response
        search_response = {
            "data": [
                {
                    "id": "phone_number_123",
                    "phone_number": did,
                    "status": "active"
                }
            ]
        }
        
        # Mock configuration update response
        config_response = {
            "data": {
                "id": "phone_number_123",
                "phone_number": did,
                "connection_id": "test_app_id",
                "call_forwarding": None
            }
        }
        
        # Mock API calls
        respx.get(
            "https://api.telnyx.com/v2/phone_numbers"
        ).mock(return_value=httpx.Response(200, json=search_response))
        
        respx.patch(
            "https://api.telnyx.com/v2/phone_numbers/phone_number_123"
        ).mock(return_value=httpx.Response(200, json=config_response))
        
        # Test the SIP configuration
        result = await telnyx_service.attach_sip_app(did)
        
        assert result["id"] == "phone_number_123"
        assert result["connection_id"] == "test_app_id"
        assert result["call_forwarding"] is None

    async def test_attach_sip_app_no_app_id(self):
        """Test SIP attachment without TELNYX_APP_ID."""
        with patch.dict('os.environ', {
            'TELNYX_API_KEY': 'test_api_key'
        }, clear=True):
            telnyx_service = TelnyxService()
            
            with pytest.raises(ValueError) as exc_info:
                await telnyx_service.attach_sip_app("+18325550123")
            
            assert "TELNYX_APP_ID environment variable is required" in str(exc_info.value)

    async def test_attach_sip_app_invalid_did(self, telnyx_service):
        """Test SIP attachment with invalid DID format."""
        invalid_did = "8325550123"  # Missing +
        
        with pytest.raises(ValueError) as exc_info:
            await telnyx_service.attach_sip_app(invalid_did)
        
        assert "DID must be in E.164 format" in str(exc_info.value)

    @respx.mock
    async def test_attach_forwarding_api_error(self, telnyx_service):
        """Test handling of Telnyx API errors during forwarding."""
        did = "+18325550123"
        forward_to = "+15558675309"
        
        # Mock phone number search response
        search_response = {
            "data": [
                {
                    "id": "phone_number_123",
                    "phone_number": did,
                    "status": "active"
                }
            ]
        }
        
        # Mock API calls - search succeeds, config fails
        respx.get(
            "https://api.telnyx.com/v2/phone_numbers"
        ).mock(return_value=httpx.Response(200, json=search_response))
        
        respx.patch(
            "https://api.telnyx.com/v2/phone_numbers/phone_number_123"
        ).mock(return_value=httpx.Response(400, json={
            "errors": [{"detail": "Invalid configuration"}]
        }))
        
        # Test should raise TelnyxAPIError
        with pytest.raises(TelnyxAPIError) as exc_info:
            await telnyx_service.attach_forwarding(did, forward_to)
        
        assert "Failed to configure forwarding" in str(exc_info.value)
        assert "Invalid configuration" in str(exc_info.value)

    @respx.mock
    async def test_attach_sip_app_api_error(self, telnyx_service):
        """Test handling of Telnyx API errors during SIP attachment."""
        did = "+18325550123"
        
        # Mock phone number search response
        search_response = {
            "data": [
                {
                    "id": "phone_number_123",
                    "phone_number": did,
                    "status": "active"
                }
            ]
        }
        
        # Mock API calls - search succeeds, config fails
        respx.get(
            "https://api.telnyx.com/v2/phone_numbers"
        ).mock(return_value=httpx.Response(200, json=search_response))
        
        respx.patch(
            "https://api.telnyx.com/v2/phone_numbers/phone_number_123"
        ).mock(return_value=httpx.Response(500, text="Internal Server Error"))
        
        # Test should raise TelnyxAPIError
        with pytest.raises(TelnyxAPIError) as exc_info:
            await telnyx_service.attach_sip_app(did)
        
        assert "Failed to attach SIP application" in str(exc_info.value)
        assert "500" in str(exc_info.value)
