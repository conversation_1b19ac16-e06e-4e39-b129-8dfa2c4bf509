"""
Integration tests for Telnyx forwarding API endpoints.
"""
import pytest
import respx
import httpx
from uuid import uuid4
from unittest.mock import patch

from packages.shared.models import TelnyxNumber, TelnyxNumberStatus, TelnyxConnectionStatus


class TestForwardingEndpoints:
    """Test forwarding API endpoints."""

    @pytest.fixture
    def tenant_id(self):
        """Generate a test tenant ID."""
        return str(uuid4())

    @pytest.fixture
    async def active_number(self, db_session, tenant_id):
        """Create an active phone number for testing."""
        number = TelnyxNumber(
            tenant_id=tenant_id,
            did="+18325550123",
            state="TX",
            status=TelnyxNumberStatus.ACTIVE,
            connection_status=TelnyxConnectionStatus.SIP,
            verification_required=False,
            telnyx_number_id="telnyx_123",
            metadata_={}
        )
        db_session.add(number)
        await db_session.commit()
        await db_session.refresh(number)
        return number

    @respx.mock
    async def test_configure_forwarding_success(self, test_client, active_number, tenant_id):
        """Test successful forwarding configuration."""
        # Mock Telnyx API responses
        search_response = {
            "data": [
                {
                    "id": "telnyx_123",
                    "phone_number": "+18325550123",
                    "status": "active"
                }
            ]
        }
        
        config_response = {
            "data": {
                "id": "telnyx_123",
                "phone_number": "+18325550123",
                "connection_id": "connection_456",
                "call_forwarding": {
                    "phone_number": "+15558675309"
                }
            }
        }
        
        respx.get(
            "https://api.telnyx.com/v2/phone_numbers"
        ).mock(return_value=httpx.Response(200, json=search_response))
        
        respx.patch(
            "https://api.telnyx.com/v2/phone_numbers/telnyx_123"
        ).mock(return_value=httpx.Response(200, json=config_response))
        
        # Make request
        response = test_client.patch(
            f"/api/v1/numbers/{active_number.id}/forward",
            json={"forward_to": "+15558675309"},
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["connection_status"] == "forwarding"
        assert data["verification_required"] is True
        assert data["forwarding_number"] == "+15558675309"

    async def test_configure_forwarding_number_not_found(self, test_client, tenant_id):
        """Test forwarding configuration for non-existent number."""
        fake_id = str(uuid4())
        
        response = test_client.patch(
            f"/api/v1/numbers/{fake_id}/forward",
            json={"forward_to": "+15558675309"},
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 404
        assert "Phone number not found" in response.json()["detail"]

    async def test_configure_forwarding_wrong_tenant(self, test_client, active_number):
        """Test forwarding configuration with wrong tenant ID."""
        wrong_tenant_id = str(uuid4())
        
        response = test_client.patch(
            f"/api/v1/numbers/{active_number.id}/forward",
            json={"forward_to": "+15558675309"},
            headers={"X-Tenant-ID": wrong_tenant_id}
        )
        
        assert response.status_code == 404
        assert "Phone number not found" in response.json()["detail"]

    async def test_configure_forwarding_inactive_number(self, test_client, db_session, tenant_id):
        """Test forwarding configuration for inactive number."""
        # Create inactive number
        number = TelnyxNumber(
            tenant_id=tenant_id,
            did="+18325550124",
            state="TX",
            status=TelnyxNumberStatus.PENDING,
            connection_status=TelnyxConnectionStatus.PROVISIONED,
            verification_required=False,
            telnyx_number_id="telnyx_124",
            metadata_={}
        )
        db_session.add(number)
        await db_session.commit()
        await db_session.refresh(number)
        
        response = test_client.patch(
            f"/api/v1/numbers/{number.id}/forward",
            json={"forward_to": "+15558675309"},
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 409
        assert "Number must be active" in response.json()["detail"]

    async def test_configure_forwarding_self_forwarding(self, test_client, active_number, tenant_id):
        """Test prevention of self-forwarding."""
        response = test_client.patch(
            f"/api/v1/numbers/{active_number.id}/forward",
            json={"forward_to": active_number.did},
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 400
        assert "Cannot forward a number to itself" in response.json()["detail"]

    async def test_configure_forwarding_invalid_format(self, test_client, active_number, tenant_id):
        """Test forwarding with invalid number format."""
        response = test_client.patch(
            f"/api/v1/numbers/{active_number.id}/forward",
            json={"forward_to": "5558675309"},  # Missing +
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 422  # Pydantic validation error

    async def test_configure_forwarding_premium_rate(self, test_client, active_number, tenant_id):
        """Test blocking of premium rate numbers."""
        response = test_client.patch(
            f"/api/v1/numbers/{active_number.id}/forward",
            json={"forward_to": "+19005551234"},
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 422  # Pydantic validation error

    @respx.mock
    async def test_configure_sip_success(self, test_client, active_number, tenant_id):
        """Test successful SIP configuration."""
        # Mock Telnyx API responses
        search_response = {
            "data": [
                {
                    "id": "telnyx_123",
                    "phone_number": "+18325550123",
                    "status": "active"
                }
            ]
        }
        
        config_response = {
            "data": {
                "id": "telnyx_123",
                "phone_number": "+18325550123",
                "connection_id": "test_app_id",
                "call_forwarding": None
            }
        }
        
        respx.get(
            "https://api.telnyx.com/v2/phone_numbers"
        ).mock(return_value=httpx.Response(200, json=search_response))
        
        respx.patch(
            "https://api.telnyx.com/v2/phone_numbers/telnyx_123"
        ).mock(return_value=httpx.Response(200, json=config_response))
        
        # Make request
        response = test_client.patch(
            f"/api/v1/numbers/{active_number.id}/sip",
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["connection_status"] == "sip"
        assert data["verification_required"] is False
        assert data["forwarding_number"] is None

    async def test_configure_sip_number_not_found(self, test_client, tenant_id):
        """Test SIP configuration for non-existent number."""
        fake_id = str(uuid4())
        
        response = test_client.patch(
            f"/api/v1/numbers/{fake_id}/sip",
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 404
        assert "Phone number not found" in response.json()["detail"]

    async def test_configure_sip_inactive_number(self, test_client, db_session, tenant_id):
        """Test SIP configuration for inactive number."""
        # Create inactive number
        number = TelnyxNumber(
            tenant_id=tenant_id,
            did="+18325550125",
            state="TX",
            status=TelnyxNumberStatus.PENDING,
            connection_status=TelnyxConnectionStatus.PROVISIONED,
            verification_required=False,
            telnyx_number_id="telnyx_125",
            metadata_={}
        )
        db_session.add(number)
        await db_session.commit()
        await db_session.refresh(number)
        
        response = test_client.patch(
            f"/api/v1/numbers/{number.id}/sip",
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 409
        assert "Number must be active" in response.json()["detail"]

    async def test_get_connection_status_success(self, test_client, active_number, tenant_id):
        """Test successful connection status retrieval."""
        response = test_client.get(
            f"/api/v1/numbers/{active_number.id}/verify",
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["connection_status"] == "sip"
        assert data["verification_required"] is False
        assert data["forwarding_number"] is None
        assert data["telnyx_connection_id"] is None
        assert "last_updated" in data

    async def test_get_connection_status_not_found(self, test_client, tenant_id):
        """Test connection status for non-existent number."""
        fake_id = str(uuid4())
        
        response = test_client.get(
            f"/api/v1/numbers/{fake_id}/verify",
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 404
        assert "Phone number not found" in response.json()["detail"]

    async def test_missing_tenant_id_header(self, test_client, active_number):
        """Test endpoints without tenant ID header."""
        # Test forwarding endpoint
        response = test_client.patch(
            f"/api/v1/numbers/{active_number.id}/forward",
            json={"forward_to": "+15558675309"}
        )
        assert response.status_code == 422
        
        # Test SIP endpoint
        response = test_client.patch(
            f"/api/v1/numbers/{active_number.id}/sip"
        )
        assert response.status_code == 422
        
        # Test verify endpoint
        response = test_client.get(
            f"/api/v1/numbers/{active_number.id}/verify"
        )
        assert response.status_code == 422

    async def test_invalid_tenant_id_format(self, test_client, active_number):
        """Test endpoints with invalid tenant ID format."""
        invalid_tenant_id = "not-a-uuid"
        
        response = test_client.patch(
            f"/api/v1/numbers/{active_number.id}/forward",
            json={"forward_to": "+15558675309"},
            headers={"X-Tenant-ID": invalid_tenant_id}
        )
        
        assert response.status_code == 400
        assert "Invalid tenant ID format" in response.json()["detail"]
