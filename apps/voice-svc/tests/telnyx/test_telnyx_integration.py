"""
Integration tests for complete Telnyx DID provisioning flow.
"""
import json
import hmac
import hashlib
import pytest
import respx
import httpx
from uuid import UUID

from packages.shared.models import TelnyxNumber, TelnyxNumberStatus


class TestTelnyxIntegration:
    """Integration tests for the complete DID provisioning flow."""
    
    def create_webhook_signature(self, payload: bytes, secret: str) -> str:
        """Create a valid webhook signature for testing."""
        signature = hmac.new(
            secret.encode('utf-8'),
            payload,
            hashlib.sha256
        ).hexdigest()
        return f"t=1234567890,v1={signature}"
    
    @respx.mock
    async def test_complete_purchase_and_activation_flow(self, test_client, test_db, tenant_id, sample_telnyx_response):
        """Test the complete flow: search -> purchase -> webhook activation."""
        
        # Step 1: Search for available numbers
        search_response = {
            "data": [
                {
                    "phone_number": "+18325550123",
                    "region_information": [
                        {"region_type": "state", "region_name": "Texas"}
                    ],
                    "features": ["voice", "sms"],
                    "cost_information": {"upfront_cost": "1.00", "monthly_cost": "1.00"},
                    "best_effort": False,
                    "quickship": True,
                    "reservable": True
                }
            ]
        }
        
        respx.get(
            "https://api.telnyx.com/v2/available_phone_numbers"
        ).mock(return_value=httpx.Response(200, json=search_response))
        
        # Search for numbers
        search_result = test_client.get(
            "/api/v1/numbers/available?state=TX&limit=10",
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert search_result.status_code == 200
        search_data = search_result.json()
        assert len(search_data["numbers"]) == 1
        selected_number = search_data["numbers"][0]["phone_number"]
        
        # Step 2: Purchase the number
        respx.post(
            "https://api.telnyx.com/v2/phone_numbers"
        ).mock(return_value=httpx.Response(201, json=sample_telnyx_response))
        
        purchase_result = test_client.post(
            "/api/v1/numbers/purchase",
            json={"state": "TX", "did": selected_number},
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert purchase_result.status_code == 201
        purchase_data = purchase_result.json()
        assert purchase_data["did"] == selected_number
        assert purchase_data["status"] == "pending"
        
        # Verify number is in database with pending status
        from sqlalchemy import select
        query = select(TelnyxNumber).where(TelnyxNumber.did == selected_number)
        result = await test_db.execute(query)
        db_number = result.scalar_one()
        
        assert db_number.status == TelnyxNumberStatus.PENDING
        assert str(db_number.tenant_id) == tenant_id
        
        # Step 3: Simulate webhook activation
        webhook_payload = {
            "data": {
                "id": "telnyx_number_123",
                "phone_number": selected_number,
                "status": "active",
                "connection_id": None,
                "customer_reference": tenant_id,
                "created_at": "2024-12-19T10:00:00Z",
                "updated_at": "2024-12-19T10:00:00Z"
            },
            "event_type": "phone_number.created",
            "id": "webhook_event_123",
            "occurred_at": "2024-12-19T10:00:00Z",
            "record_type": "event"
        }
        
        payload_json = json.dumps(webhook_payload)
        payload_bytes = payload_json.encode('utf-8')
        signature = self.create_webhook_signature(payload_bytes, "test_webhook_secret")
        
        webhook_result = test_client.post(
            "/webhooks/telnyx/number-activated",
            content=payload_bytes,
            headers={
                "Content-Type": "application/json",
                "X-Telnyx-Signature": signature
            }
        )
        
        assert webhook_result.status_code == 200
        
        # Step 4: Verify number is now active
        await test_db.refresh(db_number)
        assert db_number.status == TelnyxNumberStatus.ACTIVE
        assert db_number.telnyx_number_id == "telnyx_number_123"
        
        # Step 5: List tenant numbers to confirm
        list_result = test_client.get(
            "/api/v1/numbers/",
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert list_result.status_code == 200
        list_data = list_result.json()
        assert len(list_data) == 1
        assert list_data[0]["did"] == selected_number
        assert list_data[0]["status"] == "active"
    
    @respx.mock
    async def test_purchase_failure_and_retry(self, test_client, test_db, tenant_id):
        """Test purchase failure handling and retry logic."""
        
        # First attempt fails with 500 error
        respx.post(
            "https://api.telnyx.com/v2/phone_numbers"
        ).mock(return_value=httpx.Response(500, text="Internal Server Error"))
        
        purchase_result = test_client.post(
            "/api/v1/numbers/purchase",
            json={"state": "TX", "did": "+18325550123"},
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert purchase_result.status_code == 500
        
        # Verify no database record was created
        from sqlalchemy import select
        query = select(TelnyxNumber).where(TelnyxNumber.did == "+18325550123")
        result = await test_db.execute(query)
        db_number = result.scalar_one_or_none()
        
        assert db_number is None
    
    @respx.mock
    async def test_duplicate_purchase_prevention(self, test_client, test_db, tenant_id):
        """Test prevention of duplicate number purchases."""
        
        # Create existing number in database
        existing_number = TelnyxNumber(
            tenant_id=UUID(tenant_id),
            did="+18325550123",
            state="TX",
            status=TelnyxNumberStatus.ACTIVE
        )
        test_db.add(existing_number)
        await test_db.commit()
        
        # Try to purchase the same number
        purchase_result = test_client.post(
            "/api/v1/numbers/purchase",
            json={"state": "TX", "did": "+18325550123"},
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert purchase_result.status_code == 409
        assert "already exists" in purchase_result.json()["detail"]
    
    async def test_cross_tenant_isolation(self, test_client, test_db):
        """Test that tenants can only see their own numbers."""
        tenant1_id = "550e8400-e29b-41d4-a716-************"
        tenant2_id = "550e8400-e29b-41d4-a716-************"
        
        # Create numbers for different tenants
        number1 = TelnyxNumber(
            tenant_id=UUID(tenant1_id),
            did="+18325550123",
            state="TX",
            status=TelnyxNumberStatus.ACTIVE
        )
        number2 = TelnyxNumber(
            tenant_id=UUID(tenant2_id),
            did="+18325550124",
            state="TX",
            status=TelnyxNumberStatus.ACTIVE
        )
        
        test_db.add_all([number1, number2])
        await test_db.commit()
        
        # Tenant 1 should only see their number
        list_result1 = test_client.get(
            "/api/v1/numbers/",
            headers={"X-Tenant-ID": tenant1_id}
        )
        
        assert list_result1.status_code == 200
        data1 = list_result1.json()
        assert len(data1) == 1
        assert data1[0]["did"] == "+18325550123"
        
        # Tenant 2 should only see their number
        list_result2 = test_client.get(
            "/api/v1/numbers/",
            headers={"X-Tenant-ID": tenant2_id}
        )
        
        assert list_result2.status_code == 200
        data2 = list_result2.json()
        assert len(data2) == 1
        assert data2[0]["did"] == "+18325550124"
        
        # Tenant 1 should not be able to access tenant 2's number
        get_result = test_client.get(
            f"/api/v1/numbers/{number2.id}",
            headers={"X-Tenant-ID": tenant1_id}
        )
        
        assert get_result.status_code == 404
