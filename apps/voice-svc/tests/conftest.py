"""
Test configuration and fixtures for voice-svc tests.
"""
import asyncio
import os
import sys
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Set test environment
os.environ["ENVIRONMENT"] = "test"
os.environ["DATABASE_URL_ASYNC"] = "sqlite+aiosqlite:///./test_voice_svc.db"
os.environ["TELNYX_API_KEY"] = "test_api_key"
os.environ["TELNYX_WEBHOOK_SECRET"] = "test_webhook_secret"

from packages.shared.models import Base
from main import app
from db.session import get_db


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="function")
async def test_db():
    """Create a test database session."""
    # Create test database engine
    engine = create_async_engine(
        "sqlite+aiosqlite:///./test_voice_svc.db",
        echo=False
    )
    
    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Create session
    TestingSessionLocal = sessionmaker(
        bind=engine,
        class_=AsyncSession,
        expire_on_commit=False
    )
    
    async with TestingSessionLocal() as session:
        yield session
    
    # Clean up
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    
    await engine.dispose()


@pytest.fixture
def test_client(test_db):
    """Create a test client with database dependency override."""
    async def override_get_db():
        yield test_db
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as client:
        yield client
    
    # Clean up
    app.dependency_overrides.clear()


@pytest.fixture
def tenant_id():
    """Sample tenant ID for testing."""
    return "550e8400-e29b-41d4-a716-446655440000"


@pytest.fixture
def sample_did():
    """Sample DID for testing."""
    return "+18325550123"


@pytest.fixture
def sample_telnyx_response():
    """Sample Telnyx API response for number purchase."""
    return {
        "data": {
            "id": "telnyx_number_123",
            "phone_number": "+18325550123",
            "status": "active",
            "connection_id": None,
            "customer_reference": "550e8400-e29b-41d4-a716-446655440000",
            "created_at": "2024-12-19T10:00:00Z",
            "updated_at": "2024-12-19T10:00:00Z",
            "phone_number_type": "local",
            "regulatory_requirements": []
        }
    }


@pytest.fixture
def sample_webhook_payload():
    """Sample Telnyx webhook payload."""
    return {
        "data": {
            "id": "telnyx_number_123",
            "phone_number": "+18325550123",
            "status": "active",
            "connection_id": None,
            "customer_reference": "550e8400-e29b-41d4-a716-446655440000",
            "created_at": "2024-12-19T10:00:00Z",
            "updated_at": "2024-12-19T10:00:00Z"
        },
        "event_type": "phone_number.created",
        "id": "webhook_event_123",
        "occurred_at": "2024-12-19T10:00:00Z",
        "record_type": "event"
    }
