"""
Unit tests for the Redis-based queue service.

Tests cover:
- Basic queue operations (enqueue, dequeue)
- Active call counting
- Queue limits and overflow handling
- Error conditions and edge cases
"""
import json
import pytest
import time
from unittest.mock import AsyncMock, patch

from redis.asyncio import Redis

from services.queue_service import QueueService, CallMeta, get_queue_service

# Mark all tests in this module as async
pytestmark = pytest.mark.asyncio


@pytest.fixture
def mock_redis():
    """Mock Redis client for testing."""
    mock_redis = AsyncMock(spec=Redis)
    mock_redis.ping = AsyncMock()
    mock_redis.xlen = AsyncMock(return_value=0)
    mock_redis.xadd = AsyncMock(return_value="1234567890-0")
    mock_redis.xread = AsyncMock(return_value=[])
    mock_redis.xdel = AsyncMock(return_value=1)
    mock_redis.incr = AsyncMock(return_value=1)
    mock_redis.decr = AsyncMock(return_value=0)
    mock_redis.get = AsyncMock(return_value="0")
    mock_redis.set = AsyncMock()
    mock_redis.expire = AsyncMock()
    mock_redis.delete = AsyncMock(return_value=1)
    mock_redis.scan_iter = AsyncMock()
    mock_redis.close = AsyncMock()
    return mock_redis


@pytest.fixture
def queue_service(mock_redis):
    """Queue service with mocked Redis."""
    service = QueueService("redis://localhost:6379/0")
    service.redis = mock_redis
    service.max_active_calls_per_tenant = 2
    service.max_queue_length_per_tenant = 3
    return service


@pytest.fixture
def sample_call_meta():
    """Sample call metadata for testing."""
    return {
        "call_control_id": "test-control-123",
        "telnyx_rtc_session_id": "test-session-456",
        "lang": "en",
        "phone_number": "+1234567890",
        "caller_name": "Test Caller"
    }


class TestQueueService:
    """Test cases for QueueService."""
    
    async def test_connect(self, queue_service, mock_redis):
        """Test Redis connection establishment."""
        queue_service.redis = None
        
        with patch('redis.asyncio.from_url', return_value=mock_redis):
            await queue_service.connect()
            
        assert queue_service.redis is not None
        mock_redis.ping.assert_called_once()
    
    async def test_disconnect(self, queue_service, mock_redis):
        """Test Redis connection cleanup."""
        await queue_service.disconnect()
        
        mock_redis.close.assert_called_once()
        assert queue_service.redis is None
    
    async def test_enqueue_call_success(self, queue_service, mock_redis, sample_call_meta):
        """Test successful call enqueuing."""
        tenant_id = "tenant-123"
        call_id = "call-456"
        
        # Mock queue length check
        mock_redis.xlen.return_value = 1  # Below limit
        
        result = await queue_service.enqueue_call(tenant_id, call_id, sample_call_meta)
        
        assert result is True
        mock_redis.xlen.assert_called_once_with("voice:queue:tenant-123")
        mock_redis.xadd.assert_called_once()
        
        # Verify call metadata was properly serialized
        call_args = mock_redis.xadd.call_args
        stream_key = call_args[0][0]
        stream_data = call_args[0][1]
        
        assert stream_key == "voice:queue:tenant-123"
        assert "call_meta" in stream_data
        
        # Verify metadata content
        call_meta_dict = json.loads(stream_data["call_meta"])
        assert call_meta_dict["call_id"] == call_id
        assert call_meta_dict["tenant_id"] == tenant_id
        assert call_meta_dict["call_control_id"] == sample_call_meta["call_control_id"]
    
    async def test_enqueue_call_queue_full(self, queue_service, mock_redis, sample_call_meta):
        """Test enqueue rejection when queue is full."""
        tenant_id = "tenant-123"
        call_id = "call-456"
        
        # Mock queue at capacity
        mock_redis.xlen.return_value = queue_service.max_queue_length_per_tenant
        
        result = await queue_service.enqueue_call(tenant_id, call_id, sample_call_meta)
        
        assert result is False
        mock_redis.xlen.assert_called_once()
        mock_redis.xadd.assert_not_called()
    
    async def test_dequeue_next_success(self, queue_service, mock_redis):
        """Test successful call dequeuing."""
        tenant_id = "tenant-123"
        
        # Mock call metadata
        call_meta = CallMeta(
            call_id="call-456",
            tenant_id=tenant_id,
            call_control_id="control-123",
            telnyx_rtc_session_id="session-456",
            lang="en",
            enqueued_at=time.time()
        )
        
        # Mock Redis response
        mock_redis.xread.return_value = [
            ("voice:queue:tenant-123", [
                ("1234567890-0", {"call_meta": json.dumps(call_meta.__dict__)})
            ])
        ]
        
        result = await queue_service.dequeue_next(tenant_id)
        
        assert result is not None
        assert result.call_id == "call-456"
        assert result.tenant_id == tenant_id
        
        mock_redis.xread.assert_called_once()
        mock_redis.xdel.assert_called_once_with("voice:queue:tenant-123", "1234567890-0")
    
    async def test_dequeue_next_empty_queue(self, queue_service, mock_redis):
        """Test dequeue from empty queue."""
        tenant_id = "tenant-123"
        
        # Mock empty queue
        mock_redis.xread.return_value = []
        
        result = await queue_service.dequeue_next(tenant_id)
        
        assert result is None
        mock_redis.xread.assert_called_once()
        mock_redis.xdel.assert_not_called()
    
    async def test_increment_active(self, queue_service, mock_redis):
        """Test active call counter increment."""
        tenant_id = "tenant-123"
        
        mock_redis.incr.return_value = 3
        
        count = await queue_service.increment_active(tenant_id)
        
        assert count == 3
        mock_redis.incr.assert_called_once_with("voice:active:tenant-123")
        mock_redis.expire.assert_called_once_with("voice:active:tenant-123", 86400)
    
    async def test_decrement_active(self, queue_service, mock_redis):
        """Test active call counter decrement."""
        tenant_id = "tenant-123"
        
        mock_redis.decr.return_value = 2
        
        count = await queue_service.decrement_active(tenant_id)
        
        assert count == 2
        mock_redis.decr.assert_called_once_with("voice:active:tenant-123")
    
    async def test_decrement_active_below_zero(self, queue_service, mock_redis):
        """Test active counter doesn't go below zero."""
        tenant_id = "tenant-123"
        
        mock_redis.decr.return_value = -1
        
        count = await queue_service.decrement_active(tenant_id)
        
        assert count == 0
        mock_redis.decr.assert_called_once()
        mock_redis.set.assert_called_once_with("voice:active:tenant-123", 0)
    
    async def test_get_active_count(self, queue_service, mock_redis):
        """Test getting active call count."""
        tenant_id = "tenant-123"
        
        mock_redis.get.return_value = "5"
        
        count = await queue_service.get_active_count(tenant_id)
        
        assert count == 5
        mock_redis.get.assert_called_once_with("voice:active:tenant-123")
    
    async def test_get_active_count_none(self, queue_service, mock_redis):
        """Test getting active count when key doesn't exist."""
        tenant_id = "tenant-123"
        
        mock_redis.get.return_value = None
        
        count = await queue_service.get_active_count(tenant_id)
        
        assert count == 0
    
    async def test_get_queue_length(self, queue_service, mock_redis):
        """Test getting queue length."""
        tenant_id = "tenant-123"
        
        mock_redis.xlen.return_value = 7
        
        length = await queue_service.get_queue_length(tenant_id)
        
        assert length == 7
        mock_redis.xlen.assert_called_once_with("voice:queue:tenant-123")
    
    async def test_get_queue_stats(self, queue_service, mock_redis):
        """Test getting comprehensive queue statistics."""
        tenant_id = "tenant-123"
        
        mock_redis.get.return_value = "2"  # active count
        mock_redis.xlen.return_value = 1   # queue length
        
        stats = await queue_service.get_queue_stats(tenant_id)
        
        assert stats["tenant_id"] == tenant_id
        assert stats["active"] == 2
        assert stats["queued"] == 1
        assert stats["max_active"] == queue_service.max_active_calls_per_tenant
        assert stats["max_queue"] == queue_service.max_queue_length_per_tenant
        assert stats["capacity_used"] == 1.0  # 2/2
        assert stats["queue_used"] == 1/3     # 1/3
        assert stats["can_accept_calls"] is False  # at capacity
        assert stats["queue_available"] is True   # queue not full
    
    async def test_clear_tenant_queue(self, queue_service, mock_redis):
        """Test clearing tenant queue."""
        tenant_id = "tenant-123"
        
        mock_redis.xlen.return_value = 5
        mock_redis.delete.return_value = 1
        
        removed_count = await queue_service.clear_tenant_queue(tenant_id)
        
        assert removed_count == 5
        mock_redis.xlen.assert_called_once_with("voice:queue:tenant-123")
        mock_redis.delete.assert_called_once_with("voice:queue:tenant-123")


class TestCallMeta:
    """Test cases for CallMeta dataclass."""
    
    def test_call_meta_creation(self):
        """Test CallMeta creation with required fields."""
        meta = CallMeta(
            call_id="call-123",
            tenant_id="tenant-456",
            call_control_id="control-789",
            telnyx_rtc_session_id="session-abc"
        )
        
        assert meta.call_id == "call-123"
        assert meta.tenant_id == "tenant-456"
        assert meta.call_control_id == "control-789"
        assert meta.telnyx_rtc_session_id == "session-abc"
        assert meta.lang == "en"  # default
        assert meta.enqueued_at is not None  # auto-set
    
    def test_call_meta_with_optional_fields(self):
        """Test CallMeta creation with optional fields."""
        enqueue_time = time.time()
        
        meta = CallMeta(
            call_id="call-123",
            tenant_id="tenant-456",
            call_control_id="control-789",
            telnyx_rtc_session_id="session-abc",
            lang="es",
            phone_number="+1234567890",
            caller_name="Test Caller",
            enqueued_at=enqueue_time
        )
        
        assert meta.lang == "es"
        assert meta.phone_number == "+1234567890"
        assert meta.caller_name == "Test Caller"
        assert meta.enqueued_at == enqueue_time


class TestGlobalQueueService:
    """Test cases for global queue service instance."""
    
    def test_get_queue_service_singleton(self):
        """Test that get_queue_service returns the same instance."""
        service1 = get_queue_service()
        service2 = get_queue_service()
        
        assert service1 is service2
