"""
Unit tests for the queue worker.

Tests cover:
- Worker lifecycle (start/stop)
- Queue monitoring and processing
- Call spawning and error handling
- Integration with queue service
"""
import asyncio
import pytest
from unittest.mock import AsyncMock, patch

from workers.queue_worker import QueueWorker, get_queue_worker
from services.queue_service import CallMeta

# Mark all tests in this module as async
pytestmark = pytest.mark.asyncio


@pytest.fixture
def mock_redis():
    """Mock Redis client for testing."""
    mock_redis = AsyncMock()
    mock_redis.ping = AsyncMock()
    mock_redis.scan_iter = AsyncMock()
    mock_redis.xlen = AsyncMock(return_value=0)
    mock_redis.close = AsyncMock()
    return mock_redis


@pytest.fixture
def mock_queue_service():
    """Mock queue service for testing."""
    mock_service = AsyncMock()
    mock_service.max_active_calls_per_tenant = 2
    mock_service.max_queue_length_per_tenant = 3
    mock_service.get_active_count = AsyncMock(return_value=1)
    mock_service.get_queue_length = AsyncMock(return_value=1)
    mock_service.dequeue_next = AsyncMock(return_value=None)
    mock_service.increment_active = AsyncMock(return_value=2)
    mock_service.decrement_active = AsyncMock(return_value=1)
    return mock_service


@pytest.fixture
def queue_worker(mock_redis, mock_queue_service):
    """Queue worker with mocked dependencies."""
    worker = QueueWorker("redis://localhost:6379/0")
    worker.redis = mock_redis
    worker.queue_service = mock_queue_service
    worker.worker_poll_interval = 0.1  # Fast polling for tests
    worker.max_concurrent_processing = 2
    return worker


@pytest.fixture
def sample_call_meta():
    """Sample call metadata for testing."""
    return CallMeta(
        call_id="call-123",
        tenant_id="tenant-456",
        call_control_id="control-789",
        telnyx_rtc_session_id="session-abc",
        lang="en",
        phone_number="+1234567890",
        caller_name="Test Caller"
    )


class TestQueueWorker:
    """Test cases for QueueWorker."""
    
    async def test_connect(self, queue_worker, mock_redis):
        """Test Redis connection establishment."""
        queue_worker.redis = None
        
        with patch('redis.asyncio.from_url', return_value=mock_redis):
            await queue_worker.connect()
            
        assert queue_worker.redis is not None
        mock_redis.ping.assert_called_once()
    
    async def test_disconnect(self, queue_worker, mock_redis):
        """Test Redis connection cleanup."""
        await queue_worker.disconnect()
        
        mock_redis.close.assert_called_once()
        assert queue_worker.redis is None
    
    async def test_start_worker(self, queue_worker):
        """Test worker startup."""
        assert not queue_worker.running
        
        # Start worker
        await queue_worker.start()
        
        assert queue_worker.running
        assert len(queue_worker.tasks) == 1
        
        # Stop worker to cleanup
        await queue_worker.stop()
    
    async def test_stop_worker(self, queue_worker):
        """Test worker shutdown."""
        # Start worker first
        await queue_worker.start()
        assert queue_worker.running
        
        # Stop worker
        await queue_worker.stop()
        
        assert not queue_worker.running
        assert len(queue_worker.tasks) == 0
    
    async def test_discover_tenants(self, queue_worker, mock_redis):
        """Test tenant discovery from Redis keys."""
        # Mock Redis scan to return queue keys
        async def mock_scan_iter(match):
            if match == "voice:queue:*":
                for key in ["voice:queue:tenant1", "voice:queue:tenant2"]:
                    yield key
        
        mock_redis.scan_iter.side_effect = mock_scan_iter
        mock_redis.xlen.side_effect = lambda key: 2 if "tenant1" in key else 0
        
        await queue_worker._discover_tenants()
        
        # Should only monitor tenant1 (has messages)
        assert queue_worker.monitored_tenants == {"tenant1"}
    
    async def test_process_tenant_queue_at_capacity(self, queue_worker, mock_queue_service):
        """Test processing when tenant is at capacity."""
        tenant_id = "tenant-123"
        
        # Mock tenant at capacity
        mock_queue_service.get_active_count.return_value = 2  # At max
        
        await queue_worker._process_tenant_queue(tenant_id)
        
        # Should not dequeue or increment
        mock_queue_service.get_active_count.assert_called_once_with(tenant_id)
        mock_queue_service.dequeue_next.assert_not_called()
        mock_queue_service.increment_active.assert_not_called()
    
    async def test_process_tenant_queue_empty(self, queue_worker, mock_queue_service):
        """Test processing when queue is empty."""
        tenant_id = "tenant-123"
        
        # Mock under capacity but empty queue
        mock_queue_service.get_active_count.return_value = 1  # Under max
        mock_queue_service.dequeue_next.return_value = None   # Empty queue
        
        await queue_worker._process_tenant_queue(tenant_id)
        
        # Should remove from monitoring
        assert tenant_id not in queue_worker.monitored_tenants
        mock_queue_service.increment_active.assert_not_called()
    
    async def test_process_tenant_queue_success(self, queue_worker, mock_queue_service, sample_call_meta):
        """Test successful queue processing."""
        tenant_id = "tenant-123"
        queue_worker.monitored_tenants.add(tenant_id)
        
        # Mock under capacity with queued call
        mock_queue_service.get_active_count.return_value = 1  # Under max
        mock_queue_service.dequeue_next.return_value = sample_call_meta
        
        with patch.object(queue_worker, '_start_voice_session', new_callable=AsyncMock) as mock_start:
            await queue_worker._process_tenant_queue(tenant_id)
        
        # Should increment active and start session
        mock_queue_service.increment_active.assert_called_once_with(tenant_id)
        mock_start.assert_called_once_with(sample_call_meta)
    
    async def test_start_voice_session_success(self, queue_worker, sample_call_meta):
        """Test successful voice session start."""
        mock_start_voice_session = AsyncMock()
        
        with patch('routes.api.voice_session.start_voice_session', mock_start_voice_session):
            await queue_worker._start_voice_session(sample_call_meta)
        
        # Should call start_voice_session with correct arguments
        mock_start_voice_session.assert_called_once()
        call_args = mock_start_voice_session.call_args[1]  # kwargs
        
        assert call_args["call_control_id"] == sample_call_meta.call_control_id
        assert call_args["telnyx_rtc_session_id"] == sample_call_meta.telnyx_rtc_session_id
        assert call_args["tenant_id"] == sample_call_meta.tenant_id
        assert call_args["call_id"] == sample_call_meta.call_id
        assert call_args["from_queue"] is True
    
    async def test_start_voice_session_import_error(self, queue_worker, mock_queue_service, sample_call_meta):
        """Test voice session start with import error."""
        with patch('routes.api.voice_session.start_voice_session', side_effect=ImportError("Module not found")):
            await queue_worker._start_voice_session(sample_call_meta)
        
        # Should decrement active count on failure
        mock_queue_service.decrement_active.assert_called_once_with(sample_call_meta.tenant_id)
    
    async def test_start_voice_session_general_error(self, queue_worker, mock_queue_service, sample_call_meta):
        """Test voice session start with general error."""
        mock_start_voice_session = AsyncMock(side_effect=Exception("Pipeline error"))
        
        with patch('routes.api.voice_session.start_voice_session', mock_start_voice_session):
            await queue_worker._start_voice_session(sample_call_meta)
        
        # Should decrement active count on failure
        mock_queue_service.decrement_active.assert_called_once_with(sample_call_meta.tenant_id)
    
    async def test_spawn_next_call_no_queue(self, queue_worker, mock_queue_service):
        """Test spawn next call when no calls are queued."""
        tenant_id = "tenant-123"
        
        mock_queue_service.get_queue_length.return_value = 0
        
        await queue_worker.spawn_next_call(tenant_id)
        
        mock_queue_service.get_queue_length.assert_called_once_with(tenant_id)
        mock_queue_service.get_active_count.assert_not_called()
    
    async def test_spawn_next_call_at_capacity(self, queue_worker, mock_queue_service):
        """Test spawn next call when still at capacity."""
        tenant_id = "tenant-123"
        
        mock_queue_service.get_queue_length.return_value = 2
        mock_queue_service.get_active_count.return_value = 2  # At max
        
        await queue_worker.spawn_next_call(tenant_id)
        
        # Should not process queue
        with patch.object(queue_worker, '_process_tenant_queue') as mock_process:
            mock_process.assert_not_called()
    
    async def test_spawn_next_call_success(self, queue_worker, mock_queue_service):
        """Test successful spawn next call."""
        tenant_id = "tenant-123"
        
        mock_queue_service.get_queue_length.return_value = 2
        mock_queue_service.get_active_count.return_value = 1  # Under max
        
        with patch.object(queue_worker, '_process_tenant_queue', new_callable=AsyncMock) as mock_process:
            await queue_worker.spawn_next_call(tenant_id)
        
        mock_process.assert_called_once_with(tenant_id)
    
    async def test_worker_loop_iteration(self, queue_worker, mock_redis):
        """Test single iteration of worker loop."""
        # Mock discovery to find one tenant
        queue_worker.monitored_tenants = {"tenant-123"}
        
        with patch.object(queue_worker, '_discover_tenants', new_callable=AsyncMock) as mock_discover:
            with patch.object(queue_worker, '_process_tenant_queue', new_callable=AsyncMock) as mock_process:
                # Start worker and let it run one iteration
                await queue_worker.start()
                await asyncio.sleep(0.2)  # Let it run briefly
                await queue_worker.stop()
        
        # Should have called discovery and processing
        mock_discover.assert_called()
        mock_process.assert_called()


class TestGlobalQueueWorker:
    """Test cases for global queue worker instance."""
    
    def test_get_queue_worker_singleton(self):
        """Test that get_queue_worker returns the same instance."""
        worker1 = get_queue_worker()
        worker2 = get_queue_worker()
        
        assert worker1 is worker2
    
    async def test_queue_worker_lifespan(self):
        """Test queue worker lifespan context manager."""
        from workers.queue_worker import queue_worker_lifespan
        
        with patch.object(QueueWorker, 'start', new_callable=AsyncMock) as mock_start:
            with patch.object(QueueWorker, 'stop', new_callable=AsyncMock) as mock_stop:
                async with queue_worker_lifespan() as worker:
                    assert isinstance(worker, QueueWorker)
                    mock_start.assert_called_once()
                
                mock_stop.assert_called_once()
