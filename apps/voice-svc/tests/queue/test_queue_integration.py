"""
Integration tests for the Redis-based queue system.

Tests cover:
- End-to-end queue flow with real Redis
- Load testing with multiple simultaneous calls
- Queue overflow and recovery scenarios
- Performance and timing requirements
"""
import asyncio
import pytest
import time
from typing import Dict, Any
from unittest.mock import patch

import redis.asyncio as redis

from services.queue_service import QueueService
from workers.queue_worker import QueueWorker
from routes.api.voice_session import start_voice_session

# Mark all tests in this module as async
pytestmark = pytest.mark.asyncio


@pytest.fixture(scope="session")
async def redis_client():
    """Real Redis client for integration tests."""
    # Use a test database
    client = redis.from_url("redis://localhost:6379/15", decode_responses=True)
    
    try:
        await client.ping()
        yield client
    except redis.ConnectionError:
        pytest.skip("Redis not available for integration tests")
    finally:
        # Cleanup test data
        await client.flushdb()
        await client.close()


@pytest.fixture
async def queue_service(redis_client):
    """Queue service with real Redis for integration tests."""
    service = QueueService("redis://localhost:6379/15")
    service.redis = redis_client
    service.max_active_calls_per_tenant = 4
    service.max_queue_length_per_tenant = 10
    return service


@pytest.fixture
async def queue_worker(redis_client, queue_service):
    """Queue worker with real Redis for integration tests."""
    worker = QueueWorker("redis://localhost:6379/15")
    worker.redis = redis_client
    worker.queue_service = queue_service
    worker.worker_poll_interval = 0.1  # Fast polling for tests
    worker.max_concurrent_processing = 5
    return worker


@pytest.fixture
def sample_call_metadata():
    """Generate sample call metadata."""
    def _generate(call_id: str, tenant_id: str = "test-tenant") -> Dict[str, Any]:
        return {
            "call_control_id": f"control-{call_id}",
            "telnyx_rtc_session_id": f"session-{call_id}",
            "lang": "en",
            "phone_number": f"+123456{call_id[-4:]}",
            "caller_name": f"Caller {call_id}"
        }
    return _generate


class TestQueueIntegration:
    """Integration tests for the complete queue system."""
    
    async def test_basic_queue_flow(self, queue_service, sample_call_metadata):
        """Test basic enqueue -> dequeue flow."""
        tenant_id = "test-tenant-1"
        call_id = "call-001"
        
        # Enqueue a call
        metadata = sample_call_metadata(call_id, tenant_id)
        result = await queue_service.enqueue_call(tenant_id, call_id, metadata)
        assert result is True
        
        # Verify queue length
        length = await queue_service.get_queue_length(tenant_id)
        assert length == 1
        
        # Dequeue the call
        call_meta = await queue_service.dequeue_next(tenant_id)
        assert call_meta is not None
        assert call_meta.call_id == call_id
        assert call_meta.tenant_id == tenant_id
        
        # Verify queue is empty
        length = await queue_service.get_queue_length(tenant_id)
        assert length == 0
    
    async def test_fifo_ordering(self, queue_service, sample_call_metadata):
        """Test that calls are processed in FIFO order."""
        tenant_id = "test-tenant-2"
        call_ids = ["call-001", "call-002", "call-003"]
        
        # Enqueue multiple calls
        for call_id in call_ids:
            metadata = sample_call_metadata(call_id, tenant_id)
            result = await queue_service.enqueue_call(tenant_id, call_id, metadata)
            assert result is True
            await asyncio.sleep(0.01)  # Small delay to ensure ordering
        
        # Dequeue and verify order
        dequeued_calls = []
        for _ in range(len(call_ids)):
            call_meta = await queue_service.dequeue_next(tenant_id)
            assert call_meta is not None
            dequeued_calls.append(call_meta.call_id)
        
        assert dequeued_calls == call_ids
    
    async def test_active_call_counting(self, queue_service):
        """Test active call counter operations."""
        tenant_id = "test-tenant-3"
        
        # Initial count should be 0
        count = await queue_service.get_active_count(tenant_id)
        assert count == 0
        
        # Increment multiple times
        for expected in range(1, 5):
            count = await queue_service.increment_active(tenant_id)
            assert count == expected
        
        # Decrement multiple times
        for expected in range(3, -1, -1):
            count = await queue_service.decrement_active(tenant_id)
            assert count == expected
        
        # Verify it doesn't go below 0
        count = await queue_service.decrement_active(tenant_id)
        assert count == 0
    
    async def test_queue_overflow_rejection(self, queue_service, sample_call_metadata):
        """Test queue overflow handling."""
        tenant_id = "test-tenant-4"
        max_queue = queue_service.max_queue_length_per_tenant
        
        # Fill the queue to capacity
        for i in range(max_queue):
            call_id = f"call-{i:03d}"
            metadata = sample_call_metadata(call_id, tenant_id)
            result = await queue_service.enqueue_call(tenant_id, call_id, metadata)
            assert result is True
        
        # Verify queue is at capacity
        length = await queue_service.get_queue_length(tenant_id)
        assert length == max_queue
        
        # Try to enqueue one more (should be rejected)
        overflow_metadata = sample_call_metadata("call-overflow", tenant_id)
        result = await queue_service.enqueue_call(tenant_id, "call-overflow", overflow_metadata)
        assert result is False
        
        # Queue length should remain the same
        length = await queue_service.get_queue_length(tenant_id)
        assert length == max_queue
    
    async def test_multi_tenant_isolation(self, queue_service, sample_call_metadata):
        """Test that tenant queues are isolated."""
        tenant1 = "tenant-1"
        tenant2 = "tenant-2"
        
        # Enqueue calls for both tenants
        metadata1 = sample_call_metadata("call-t1", tenant1)
        metadata2 = sample_call_metadata("call-t2", tenant2)
        
        await queue_service.enqueue_call(tenant1, "call-t1", metadata1)
        await queue_service.enqueue_call(tenant2, "call-t2", metadata2)
        
        # Increment active counts
        await queue_service.increment_active(tenant1)
        await queue_service.increment_active(tenant2)
        await queue_service.increment_active(tenant2)
        
        # Verify isolation
        stats1 = await queue_service.get_queue_stats(tenant1)
        stats2 = await queue_service.get_queue_stats(tenant2)
        
        assert stats1["queued"] == 1
        assert stats1["active"] == 1
        assert stats2["queued"] == 1
        assert stats2["active"] == 2
        
        # Dequeue should only affect the specific tenant
        call_meta = await queue_service.dequeue_next(tenant1)
        assert call_meta.tenant_id == tenant1
        
        # Tenant2 queue should be unaffected
        length2 = await queue_service.get_queue_length(tenant2)
        assert length2 == 1
    
    async def test_performance_requirements(self, queue_service, sample_call_metadata):
        """Test that queue operations meet <10ms requirement."""
        tenant_id = "test-tenant-perf"
        
        # Test enqueue performance
        start_time = time.time()
        for i in range(10):
            call_id = f"perf-call-{i}"
            metadata = sample_call_metadata(call_id, tenant_id)
            await queue_service.enqueue_call(tenant_id, call_id, metadata)
        enqueue_time = (time.time() - start_time) * 1000  # Convert to ms
        
        # Average enqueue time should be < 10ms
        avg_enqueue_time = enqueue_time / 10
        assert avg_enqueue_time < 10, f"Average enqueue time {avg_enqueue_time:.2f}ms exceeds 10ms"
        
        # Test dequeue performance
        start_time = time.time()
        for i in range(10):
            await queue_service.dequeue_next(tenant_id)
        dequeue_time = (time.time() - start_time) * 1000
        
        # Average dequeue time should be < 10ms
        avg_dequeue_time = dequeue_time / 10
        assert avg_dequeue_time < 10, f"Average dequeue time {avg_dequeue_time:.2f}ms exceeds 10ms"
    
    async def test_load_scenario_6_calls_4_active_2_queued(self, queue_service, queue_worker, sample_call_metadata):
        """Test the specific load scenario: 6 calls, 4 active, 2 queued."""
        tenant_id = "test-tenant-load"
        
        # Mock voice session to track calls
        started_calls = []
        ended_calls = []
        
        async def mock_start_voice_session(**kwargs):
            started_calls.append(kwargs["call_id"])
            # Simulate call duration
            await asyncio.sleep(0.2)
            ended_calls.append(kwargs["call_id"])
            # Trigger cleanup
            from routes.api.voice_session import end_voice_session
            await end_voice_session(kwargs["tenant_id"], kwargs["call_id"])
        
        with patch('routes.api.voice_session.start_voice_session', side_effect=mock_start_voice_session):
            # Start the queue worker
            await queue_worker.start()
            
            try:
                # Simulate 6 simultaneous calls
                call_tasks = []
                for i in range(6):
                    call_id = f"load-call-{i:02d}"
                    metadata = sample_call_metadata(call_id, tenant_id)
                    
                    # Mock the voice session start to use queue
                    task = asyncio.create_task(
                        start_voice_session(
                            call_control_id=metadata["call_control_id"],
                            telnyx_rtc_session_id=metadata["telnyx_rtc_session_id"],
                            tenant_id=tenant_id,
                            call_id=call_id,
                            lang=metadata["lang"],
                            phone_number=metadata["phone_number"],
                            caller_name=metadata["caller_name"]
                        )
                    )
                    call_tasks.append(task)
                    await asyncio.sleep(0.01)  # Small delay between calls
                
                # Wait a moment for initial processing
                await asyncio.sleep(0.1)
                
                # Check initial state: should have 4 active, 2 queued
                stats = await queue_service.get_queue_stats(tenant_id)
                assert stats["active"] <= 4, f"Too many active calls: {stats['active']}"
                assert stats["queued"] >= 0, f"Negative queue length: {stats['queued']}"
                
                # Wait for some calls to complete
                await asyncio.sleep(0.5)
                
                # Verify that queued calls eventually get processed
                final_stats = await queue_service.get_queue_stats(tenant_id)
                
                # All calls should eventually be processed
                await asyncio.gather(*call_tasks, return_exceptions=True)
                
                # Final verification
                assert len(started_calls) > 0, "No calls were started"
                
            finally:
                await queue_worker.stop()
    
    async def test_queue_recovery_after_worker_restart(self, queue_service, queue_worker, sample_call_metadata):
        """Test that queued calls are processed after worker restart."""
        tenant_id = "test-tenant-recovery"
        
        # Enqueue some calls while worker is stopped
        call_ids = ["recovery-001", "recovery-002", "recovery-003"]
        for call_id in call_ids:
            metadata = sample_call_metadata(call_id, tenant_id)
            await queue_service.enqueue_call(tenant_id, call_id, metadata)
        
        # Verify calls are queued
        length = await queue_service.get_queue_length(tenant_id)
        assert length == 3
        
        # Mock voice session start
        processed_calls = []
        
        async def mock_start_voice_session(**kwargs):
            processed_calls.append(kwargs["call_id"])
            # Simulate quick call
            await asyncio.sleep(0.05)
            from routes.api.voice_session import end_voice_session
            await end_voice_session(kwargs["tenant_id"], kwargs["call_id"])
        
        with patch('routes.api.voice_session.start_voice_session', side_effect=mock_start_voice_session):
            # Start worker and let it process the queue
            await queue_worker.start()
            
            try:
                # Wait for processing
                await asyncio.sleep(0.5)
                
                # Verify all calls were processed
                final_length = await queue_service.get_queue_length(tenant_id)
                assert final_length == 0, f"Queue not empty: {final_length} calls remaining"
                
                # Verify calls were processed in order
                assert len(processed_calls) == 3
                assert processed_calls == call_ids
                
            finally:
                await queue_worker.stop()


class TestQueueErrorHandling:
    """Test error handling and edge cases."""
    
    async def test_redis_connection_failure(self, queue_service):
        """Test handling of Redis connection failures."""
        # Close the Redis connection to simulate failure
        await queue_service.redis.close()
        
        # Operations should handle the error gracefully
        with pytest.raises(redis.ConnectionError):
            await queue_service.enqueue_call("tenant", "call", {})
    
    async def test_malformed_queue_data(self, queue_service, redis_client):
        """Test handling of malformed data in queue."""
        tenant_id = "test-tenant-malformed"
        queue_key = f"voice:queue:{tenant_id}"
        
        # Add malformed data directly to Redis
        await redis_client.xadd(queue_key, {"invalid": "data"})
        
        # Dequeue should handle malformed data gracefully
        result = await queue_service.dequeue_next(tenant_id)
        assert result is None  # Should return None for invalid data
        
        # Queue should be cleaned up
        length = await queue_service.get_queue_length(tenant_id)
        assert length == 0
