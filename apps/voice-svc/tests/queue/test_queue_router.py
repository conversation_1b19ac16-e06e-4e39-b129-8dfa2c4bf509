"""
Tests for the queue management API endpoints.

Tests cover:
- Queue statistics endpoints
- Authentication and authorization
- Admin operations (clear queue, worker control)
- Error handling and edge cases
"""
import pytest
from unittest.mock import AsyncMock, patch
from fastapi.testclient import TestClient
from fastapi import FastAP<PERSON>

from routers.queue import router as queue_router


@pytest.fixture
def app():
    """FastAPI app with queue router for testing."""
    app = FastAPI()
    app.include_router(queue_router)
    return app


@pytest.fixture
def client(app):
    """Test client for queue API."""
    return TestClient(app)


@pytest.fixture
def mock_queue_service():
    """Mock queue service for testing."""
    mock_service = AsyncMock()
    mock_service.max_active_calls_per_tenant = 4
    mock_service.max_queue_length_per_tenant = 10
    mock_service.get_queue_stats = AsyncMock(return_value={
        "tenant_id": "test-tenant",
        "active": 2,
        "queued": 1,
        "max_active": 4,
        "max_queue": 10,
        "capacity_used": 0.5,
        "queue_used": 0.1,
        "can_accept_calls": True,
        "queue_available": True
    })
    mock_service.clear_tenant_queue = AsyncMock(return_value=3)
    mock_service.connect = AsyncMock()
    mock_service.redis = AsyncMock()
    mock_service.redis.scan_iter = AsyncMock()
    return mock_service


@pytest.fixture
def mock_queue_worker():
    """Mock queue worker for testing."""
    mock_worker = AsyncMock()
    mock_worker.running = False
    mock_worker.start = AsyncMock()
    mock_worker.stop = AsyncMock()
    return mock_worker


@pytest.fixture
def mock_auth():
    """Mock authentication for testing."""
    return {"id": "test-user", "role": "partner"}


class TestQueueStatsEndpoint:
    """Test cases for queue statistics endpoint."""
    
    def test_get_queue_stats_success(self, client, mock_queue_service, mock_auth):
        """Test successful queue stats retrieval."""
        with patch('routers.queue.get_queue_service', return_value=mock_queue_service):
            with patch('routers.queue.get_current_user', return_value=mock_auth):
                response = client.get("/api/v1/queue/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["tenant_id"] == "test-user"  # Uses user ID as tenant ID
        assert data["active"] == 2
        assert data["queued"] == 1
        assert data["max_active"] == 4
        assert data["max_queue"] == 10
        assert data["capacity_used"] == 0.5
        assert data["queue_used"] == 0.1
        assert data["can_accept_calls"] is True
        assert data["queue_available"] is True
    
    def test_get_queue_stats_with_tenant_id(self, client, mock_queue_service, mock_auth):
        """Test queue stats with specific tenant ID."""
        with patch('routers.queue.get_queue_service', return_value=mock_queue_service):
            with patch('routers.queue.get_current_user', return_value=mock_auth):
                response = client.get("/api/v1/queue/?tenant_id=other-tenant")
        
        assert response.status_code == 200
        # Should call get_queue_stats with the specified tenant ID
        mock_queue_service.get_queue_stats.assert_called_with("other-tenant")
    
    def test_get_queue_stats_unauthorized(self, client):
        """Test queue stats with unauthorized user."""
        mock_auth = {"id": "test-user", "role": "user"}  # Not partner role
        
        with patch('routers.queue.get_current_user', return_value=mock_auth):
            response = client.get("/api/v1/queue/")
        
        assert response.status_code == 403
        assert "Partner role required" in response.json()["detail"]
    
    def test_get_queue_stats_service_error(self, client, mock_queue_service, mock_auth):
        """Test queue stats with service error."""
        mock_queue_service.get_queue_stats.side_effect = Exception("Redis connection failed")
        
        with patch('routers.queue.get_queue_service', return_value=mock_queue_service):
            with patch('routers.queue.get_current_user', return_value=mock_auth):
                response = client.get("/api/v1/queue/")
        
        assert response.status_code == 500
        assert "Failed to retrieve queue statistics" in response.json()["detail"]


class TestAllQueueStatsEndpoint:
    """Test cases for all tenants queue statistics endpoint."""
    
    def test_get_all_queue_stats_success(self, client, mock_queue_service, mock_auth):
        """Test successful retrieval of all queue stats."""
        # Mock Redis scan to return tenant keys
        async def mock_scan_iter(match):
            if "queue" in match:
                for key in ["voice:queue:tenant1", "voice:queue:tenant2"]:
                    yield key
            elif "active" in match:
                for key in ["voice:active:tenant1", "voice:active:tenant3"]:
                    yield key
        
        mock_queue_service.redis.scan_iter.side_effect = mock_scan_iter
        
        # Mock stats for each tenant
        def mock_get_stats(tenant_id):
            return {
                "tenant_id": tenant_id,
                "active": 1,
                "queued": 0,
                "max_active": 4,
                "max_queue": 10,
                "capacity_used": 0.25,
                "queue_used": 0.0,
                "can_accept_calls": True,
                "queue_available": True
            }
        
        mock_queue_service.get_queue_stats.side_effect = mock_get_stats
        
        with patch('routers.queue.get_queue_service', return_value=mock_queue_service):
            with patch('routers.queue.get_current_user', return_value=mock_auth):
                response = client.get("/api/v1/queue/all")
        
        assert response.status_code == 200
        data = response.json()
        
        assert len(data) == 3  # tenant1, tenant2, tenant3
        tenant_ids = [item["tenant_id"] for item in data]
        assert "tenant1" in tenant_ids
        assert "tenant2" in tenant_ids
        assert "tenant3" in tenant_ids
    
    def test_get_all_queue_stats_error(self, client, mock_queue_service, mock_auth):
        """Test all queue stats with service error."""
        mock_queue_service.connect.side_effect = Exception("Connection failed")
        
        with patch('routers.queue.get_queue_service', return_value=mock_queue_service):
            with patch('routers.queue.get_current_user', return_value=mock_auth):
                response = client.get("/api/v1/queue/all")
        
        assert response.status_code == 500
        assert "Failed to retrieve all queue statistics" in response.json()["detail"]


class TestClearQueueEndpoint:
    """Test cases for queue clear endpoint."""
    
    def test_clear_queue_success(self, client, mock_queue_service, mock_auth):
        """Test successful queue clearing."""
        with patch('routers.queue.get_queue_service', return_value=mock_queue_service):
            with patch('routers.queue.get_current_user', return_value=mock_auth):
                response = client.post("/api/v1/queue/clear")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["tenant_id"] == "test-user"
        assert data["calls_removed"] == 3
        assert "Successfully cleared" in data["message"]
        
        mock_queue_service.clear_tenant_queue.assert_called_once_with("test-user")
    
    def test_clear_queue_with_tenant_id(self, client, mock_queue_service, mock_auth):
        """Test queue clearing with specific tenant ID."""
        with patch('routers.queue.get_queue_service', return_value=mock_queue_service):
            with patch('routers.queue.get_current_user', return_value=mock_auth):
                response = client.post("/api/v1/queue/clear?tenant_id=other-tenant")
        
        assert response.status_code == 200
        mock_queue_service.clear_tenant_queue.assert_called_once_with("other-tenant")
    
    def test_clear_queue_service_error(self, client, mock_queue_service, mock_auth):
        """Test queue clearing with service error."""
        mock_queue_service.clear_tenant_queue.side_effect = Exception("Clear failed")
        
        with patch('routers.queue.get_queue_service', return_value=mock_queue_service):
            with patch('routers.queue.get_current_user', return_value=mock_auth):
                response = client.post("/api/v1/queue/clear")
        
        assert response.status_code == 500
        assert "Failed to clear queue" in response.json()["detail"]


class TestQueueHealthEndpoint:
    """Test cases for queue health check endpoint."""
    
    def test_queue_health_success(self, client, mock_queue_service, mock_queue_worker):
        """Test successful health check."""
        mock_queue_worker.running = True
        
        with patch('routers.queue.get_queue_service', return_value=mock_queue_service):
            with patch('routers.queue.get_queue_worker', return_value=mock_queue_worker):
                response = client.get("/api/v1/queue/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "healthy"
        assert data["queue_service"] == "connected"
        assert data["queue_worker"] == "running"
        assert data["redis_connection"] == "ok"
        assert data["max_active_per_tenant"] == 4
        assert data["max_queue_per_tenant"] == 10
    
    def test_queue_health_worker_stopped(self, client, mock_queue_service, mock_queue_worker):
        """Test health check with stopped worker."""
        mock_queue_worker.running = False
        
        with patch('routers.queue.get_queue_service', return_value=mock_queue_service):
            with patch('routers.queue.get_queue_worker', return_value=mock_queue_worker):
                response = client.get("/api/v1/queue/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "healthy"
        assert data["queue_worker"] == "stopped"
    
    def test_queue_health_redis_error(self, client, mock_queue_service, mock_queue_worker):
        """Test health check with Redis error."""
        mock_queue_service.redis.ping.side_effect = Exception("Redis down")
        
        with patch('routers.queue.get_queue_service', return_value=mock_queue_service):
            with patch('routers.queue.get_queue_worker', return_value=mock_queue_worker):
                response = client.get("/api/v1/queue/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "unhealthy"
        assert data["redis_connection"] == "failed"
        assert "Redis down" in data["error"]


class TestWorkerControlEndpoints:
    """Test cases for worker start/stop endpoints."""
    
    def test_start_worker_success(self, client, mock_queue_worker, mock_auth):
        """Test successful worker start."""
        mock_queue_worker.running = False
        
        with patch('routers.queue.get_queue_worker', return_value=mock_queue_worker):
            with patch('routers.queue.get_current_user', return_value=mock_auth):
                response = client.post("/api/v1/queue/worker/start")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "started"
        assert "started successfully" in data["message"]
        mock_queue_worker.start.assert_called_once()
    
    def test_start_worker_already_running(self, client, mock_queue_worker, mock_auth):
        """Test starting worker when already running."""
        mock_queue_worker.running = True
        
        with patch('routers.queue.get_queue_worker', return_value=mock_queue_worker):
            with patch('routers.queue.get_current_user', return_value=mock_auth):
                response = client.post("/api/v1/queue/worker/start")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "already_running"
        mock_queue_worker.start.assert_not_called()
    
    def test_stop_worker_success(self, client, mock_queue_worker, mock_auth):
        """Test successful worker stop."""
        mock_queue_worker.running = True
        
        with patch('routers.queue.get_queue_worker', return_value=mock_queue_worker):
            with patch('routers.queue.get_current_user', return_value=mock_auth):
                response = client.post("/api/v1/queue/worker/stop")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "stopped"
        assert "stopped successfully" in data["message"]
        mock_queue_worker.stop.assert_called_once()
    
    def test_stop_worker_already_stopped(self, client, mock_queue_worker, mock_auth):
        """Test stopping worker when already stopped."""
        mock_queue_worker.running = False
        
        with patch('routers.queue.get_queue_worker', return_value=mock_queue_worker):
            with patch('routers.queue.get_current_user', return_value=mock_auth):
                response = client.post("/api/v1/queue/worker/stop")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "already_stopped"
        mock_queue_worker.stop.assert_not_called()
    
    def test_worker_control_unauthorized(self, client):
        """Test worker control with unauthorized user."""
        mock_auth = {"id": "test-user", "role": "user"}  # Not partner role
        
        with patch('routers.queue.get_current_user', return_value=mock_auth):
            response = client.post("/api/v1/queue/worker/start")
        
        assert response.status_code == 403
    
    def test_worker_start_error(self, client, mock_queue_worker, mock_auth):
        """Test worker start with error."""
        mock_queue_worker.running = False
        mock_queue_worker.start.side_effect = Exception("Start failed")
        
        with patch('routers.queue.get_queue_worker', return_value=mock_queue_worker):
            with patch('routers.queue.get_current_user', return_value=mock_auth):
                response = client.post("/api/v1/queue/worker/start")
        
        assert response.status_code == 500
        assert "Failed to start queue worker" in response.json()["detail"]
