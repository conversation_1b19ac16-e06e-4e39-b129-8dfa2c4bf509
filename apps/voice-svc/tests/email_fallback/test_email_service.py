"""
Unit tests for EmailFallbackService.

Tests:
- MJML template rendering
- Email sending with Resend
- Admin email lookup
- Error handling
- Skip email functionality
"""
import json
import subprocess
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone
from uuid import uuid4

import httpx
import respx

from services.email_fallback import EmailFallbackService
from schemas.intake import IntakeEvent


@pytest.fixture
def sample_intake_event():
    """Create a sample intake event for testing."""
    return IntakeEvent(
        call_id=uuid4(),
        tenant_id=uuid4(),
        caller_number="+**********",
        caller_name="<PERSON>",
        transcript="Hello, I'd like to schedule an appointment for next week.",
        intent="book_meeting",
        created_at=datetime.now(timezone.utc),
        booking=None,
        recording_url=None
    )


@pytest.fixture
def email_service():
    """Create EmailFallbackService instance for testing."""
    return EmailFallbackService(
        resend_api_key="test_key",
        email_from="<EMAIL>",
        core_api_url="https://core-api.example.com",
        admin_url="https://admin.example.com",
        skip_email=True  # Skip actual email sending in tests
    )


@pytest.fixture
def email_service_no_skip():
    """Create EmailFallbackService instance without skip_email for testing actual sending."""
    return EmailFallbackService(
        resend_api_key="test_key",
        email_from="<EMAIL>",
        core_api_url="https://core-api.example.com",
        admin_url="https://admin.example.com",
        skip_email=False
    )


class TestEmailFallbackService:
    """Test EmailFallbackService functionality."""
    
    def test_init_with_defaults(self):
        """Test service initialization with default values."""
        with patch.dict('os.environ', {
            'RESEND_API_KEY': 'env_key',
            'EMAIL_FROM': '<EMAIL>',
            'CORE_SUBS_API_URL': 'https://env-core.com',
            'ADMIN_URL': 'https://env-admin.com',
            'SKIP_EMAIL': 'true'
        }):
            service = EmailFallbackService()
            
            assert service.resend_api_key == 'env_key'
            assert service.email_from == '<EMAIL>'
            assert service.core_api_url == 'https://env-core.com'
            assert service.admin_url == 'https://env-admin.com'
            assert service.skip_email is True
    
    def test_init_with_explicit_values(self):
        """Test service initialization with explicit values."""
        service = EmailFallbackService(
            resend_api_key="explicit_key",
            email_from="<EMAIL>",
            core_api_url="https://explicit-core.com",
            admin_url="https://explicit-admin.com",
            skip_email=False
        )
        
        assert service.resend_api_key == "explicit_key"
        assert service.email_from == "<EMAIL>"
        assert service.core_api_url == "https://explicit-core.com"
        assert service.admin_url == "https://explicit-admin.com"
        assert service.skip_email is False
    
    def test_truncate_filter(self, email_service):
        """Test the Jinja2 truncate filter."""
        # Test normal truncation
        result = email_service._truncate_filter("This is a long text", 10)
        assert result == "This is..."
        
        # Test text shorter than limit
        result = email_service._truncate_filter("Short", 10)
        assert result == "Short"
        
        # Test custom end string
        result = email_service._truncate_filter("This is a long text", 10, " [more]")
        assert result == "Thi [more]"
    
    @pytest.mark.asyncio
    async def test_get_tenant_admin_emails_success(self, email_service):
        """Test successful admin email lookup."""
        tenant_id = "test-tenant-123"
        expected_emails = ["<EMAIL>", "<EMAIL>"]
        
        with respx.mock:
            respx.get(f"https://core-api.example.com/tenant/{tenant_id}/admins").mock(
                return_value=httpx.Response(200, json={
                    "admins": [
                        {"email": "<EMAIL>", "role": "admin"},
                        {"email": "<EMAIL>", "role": "admin"},
                        {"email": None, "role": "admin"}  # Should be filtered out
                    ]
                })
            )
            
            emails = await email_service._get_tenant_admin_emails(tenant_id)
            assert emails == expected_emails
    
    @pytest.mark.asyncio
    async def test_get_tenant_admin_emails_no_admins(self, email_service):
        """Test admin email lookup when no admins found."""
        tenant_id = "test-tenant-123"
        
        with respx.mock:
            respx.get(f"https://core-api.example.com/tenant/{tenant_id}/admins").mock(
                return_value=httpx.Response(200, json={"admins": []})
            )
            
            emails = await email_service._get_tenant_admin_emails(tenant_id)
            assert emails == []
    
    @pytest.mark.asyncio
    async def test_get_tenant_admin_emails_api_error(self, email_service):
        """Test admin email lookup when API returns error."""
        tenant_id = "test-tenant-123"
        
        with respx.mock:
            respx.get(f"https://core-api.example.com/tenant/{tenant_id}/admins").mock(
                return_value=httpx.Response(500, text="Internal Server Error")
            )
            
            emails = await email_service._get_tenant_admin_emails(tenant_id)
            assert emails == []
    
    @pytest.mark.asyncio
    async def test_get_tenant_admin_emails_no_core_url(self):
        """Test admin email lookup when Core API URL not configured."""
        service = EmailFallbackService(core_api_url=None)
        
        emails = await service._get_tenant_admin_emails("test-tenant")
        assert emails == []
    
    @patch('subprocess.run')
    def test_render_mjml_to_html_success(self, mock_run, email_service):
        """Test successful MJML to HTML rendering."""
        mjml_content = "<mjml><mj-body><mj-text>Hello</mj-text></mj-body></mjml>"
        expected_html = "<html><body>Hello</body></html>"
        
        # Mock successful subprocess call
        mock_run.return_value.stdout = expected_html
        mock_run.return_value.returncode = 0
        
        result = email_service._render_mjml_to_html(mjml_content)
        
        assert result == expected_html
        mock_run.assert_called_once()
        
        # Check that mjml command was called correctly
        call_args = mock_run.call_args
        assert call_args[0][0][0] == 'mjml'
        assert '--stdout' in call_args[0][0]
    
    @patch('subprocess.run')
    def test_render_mjml_to_html_failure(self, mock_run, email_service):
        """Test MJML rendering failure."""
        mjml_content = "<mjml><invalid></mjml>"
        
        # Mock failed subprocess call
        mock_run.side_effect = subprocess.CalledProcessError(
            1, 'mjml', stderr="MJML parsing error"
        )
        
        with pytest.raises(RuntimeError, match="MJML rendering failed"):
            email_service._render_mjml_to_html(mjml_content)
    
    @patch('subprocess.run')
    def test_render_mjml_to_html_not_found(self, mock_run, email_service):
        """Test MJML rendering when mjml CLI not found."""
        mjml_content = "<mjml><mj-body><mj-text>Hello</mj-text></mj-body></mjml>"
        
        # Mock FileNotFoundError (mjml not installed)
        mock_run.side_effect = FileNotFoundError("mjml not found")
        
        with pytest.raises(RuntimeError, match="mjml CLI not found"):
            email_service._render_mjml_to_html(mjml_content)
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_send_intake_email_skip_enabled(self, mock_run, email_service, sample_intake_event):
        """Test email sending with skip_email=True."""
        tenant_id = "test-tenant-123"

        # Mock MJML rendering
        mock_run.return_value.stdout = "<html><body>Test email content</body></html>"
        mock_run.return_value.returncode = 0

        with respx.mock:
            respx.get(f"https://core-api.example.com/tenant/{tenant_id}/admins").mock(
                return_value=httpx.Response(200, json={
                    "admins": [{"email": "<EMAIL>", "role": "admin"}]
                })
            )

            result = await email_service.send_intake_email(sample_intake_event, tenant_id)

            assert result["success"] is True
            assert result["skipped"] is True
            assert result["recipients"] == ["<EMAIL>"]
            assert "📞 New intake from John Doe" in result["subject"]
    
    @pytest.mark.asyncio
    async def test_send_intake_email_no_admins(self, email_service, sample_intake_event):
        """Test email sending when no admin emails found."""
        tenant_id = "test-tenant-123"
        
        with respx.mock:
            respx.get(f"https://core-api.example.com/tenant/{tenant_id}/admins").mock(
                return_value=httpx.Response(200, json={"admins": []})
            )
            
            result = await email_service.send_intake_email(sample_intake_event, tenant_id)
            
            assert result["success"] is False
            assert result["error"] == "No admin emails found"
            assert result["tenant_id"] == tenant_id
            assert result["call_id"] == str(sample_intake_event.call_id)
    
    @pytest.mark.asyncio
    @patch('resend.Emails.send')
    @patch('subprocess.run')
    async def test_send_intake_email_success(self, mock_run, mock_resend, email_service_no_skip, sample_intake_event):
        """Test successful email sending via Resend."""
        tenant_id = "test-tenant-123"
        
        # Mock MJML rendering
        mock_run.return_value.stdout = "<html><body>Email content</body></html>"
        mock_run.return_value.returncode = 0
        
        # Mock Resend response
        mock_resend.return_value = {"id": "email_123"}
        
        with respx.mock:
            respx.get(f"https://core-api.example.com/tenant/{tenant_id}/admins").mock(
                return_value=httpx.Response(200, json={
                    "admins": [{"email": "<EMAIL>", "role": "admin"}]
                })
            )
            
            result = await email_service_no_skip.send_intake_email(sample_intake_event, tenant_id)
            
            assert result["success"] is True
            assert result["email_id"] == "email_123"
            assert result["recipients"] == ["<EMAIL>"]
            assert "📞 New intake from John Doe" in result["subject"]
            
            # Verify Resend was called with correct parameters
            mock_resend.assert_called_once()
            call_args = mock_resend.call_args[0][0]
            assert call_args["from"] == "AiLex Voice <<EMAIL>>"
            assert call_args["to"] == ["<EMAIL>"]
            assert "📞 New intake from John Doe" in call_args["subject"]
            assert call_args["html"] == "<html><body>Email content</body></html>"
    
    @pytest.mark.asyncio
    async def test_close(self, email_service):
        """Test service cleanup."""
        # Set up a mock HTTP client
        email_service.http_client = AsyncMock()
        
        await email_service.close()
        
        email_service.http_client.aclose.assert_called_once()
        assert email_service.http_client is None


class TestEmailServiceIntegration:
    """Integration tests for email service."""
    
    @pytest.mark.asyncio
    async def test_template_rendering_with_booking(self, email_service):
        """Test template rendering with booking information."""
        from datetime import datetime, timezone
        
        # Create intake event with booking
        booking_data = {
            "slot_start": datetime.now(timezone.utc),
            "slot_end": datetime.now(timezone.utc),
            "provider": "calendly",
            "external_id": "booking_123",
            "provider_event_link": "https://calendly.com/event/123"
        }
        
        intake_event = IntakeEvent(
            call_id=uuid4(),
            tenant_id=uuid4(),
            caller_number="+**********",
            caller_name="Jane Smith",
            transcript="I need to book an appointment for tomorrow.",
            intent="book_meeting",
            created_at=datetime.now(timezone.utc),
            booking=booking_data,
            recording_url="https://recordings.example.com/call123.mp3"
        )
        
        # Test template context preparation
        context = {
            "call_id": str(intake_event.call_id),
            "tenant_id": "test-tenant",
            "caller_name": intake_event.caller_name,
            "caller_number": intake_event.caller_number,
            "transcript": intake_event.transcript,
            "intent": intake_event.intent,
            "booking": intake_event.booking,
            "recording_url": str(intake_event.recording_url),
            "created_at": intake_event.created_at,
            "admin_url": email_service.admin_url,
            "call_duration": "Unknown"
        }
        
        # Render template
        template = email_service.env.get_template("intake_email.mjml")
        mjml_content = template.render(**context)
        
        # Verify template contains expected content
        assert "Jane Smith" in mjml_content
        assert "+**********" in mjml_content
        assert "book_meeting" in mjml_content
        assert "booking" in mjml_content.lower()
        assert "recording" in mjml_content.lower()
        assert str(intake_event.call_id) in mjml_content
