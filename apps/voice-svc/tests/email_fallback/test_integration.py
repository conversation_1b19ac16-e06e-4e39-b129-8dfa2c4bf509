"""
Integration tests for email fallback functionality.

Tests the complete workflow from call end to email delivery,
including feature flag checks and webhook vs email decision logic.
"""
import pytest
from unittest.mock import AsyncMock, patch
from datetime import datetime, timezone
from uuid import uuid4

import respx
import httpx

from routes.api.voice_session import _send_intake_event_for_call_end, _check_tenant_has_core_sync
from services.email_fallback import get_email_fallback_service
from services.intake_webhook import get_intake_webhook_service


class TestEmailFallbackIntegration:
    """Integration tests for email fallback workflow."""
    
    @pytest.mark.asyncio
    async def test_tenant_with_core_sync_uses_webhook(self):
        """Test that tenants with core_intake_sync use webhook, not email."""
        call_id = "test-call-123"
        tenant_id = "tenant-with-sync"
        
        # Mock feature check to return True (has core_intake_sync)
        with patch('routes.api.voice_session._check_tenant_has_core_sync') as mock_check:
            mock_check.return_value = True
            
            # Mock webhook service
            with patch('services.intake_webhook.get_intake_webhook_service') as mock_webhook_service:
                mock_service = AsyncMock()
                mock_result = AsyncMock()
                mock_result.success = True
                mock_service.send_intake_event.return_value = mock_result
                mock_webhook_service.return_value = mock_service
                
                # Mock email service to ensure it's not called
                with patch('services.email_fallback.get_email_fallback_service') as mock_email_service:
                    mock_email = AsyncMock()
                    mock_email_service.return_value = mock_email
                    
                    # Mock event builder
                    with patch('services.intake_event_builder.get_intake_event_builder') as mock_builder:
                        mock_event_builder = AsyncMock()
                        mock_event = AsyncMock()
                        mock_event_builder.build_call_completion_event.return_value = mock_event
                        mock_event_builder.get_recording_url_for_call.return_value = None
                        mock_event_builder.extract_transcript_from_pipeline_data.return_value = None
                        mock_event_builder.extract_intent_from_pipeline_data.return_value = None
                        mock_builder.return_value = mock_event_builder
                        
                        # Execute the function
                        await _send_intake_event_for_call_end(
                            call_id=call_id,
                            tenant_id=tenant_id,
                            caller_number="+1234567890",
                            caller_name="John Doe",
                            transcript="Test transcript",
                            intent="test_intent"
                        )
                        
                        # Verify webhook was called
                        mock_service.send_intake_event.assert_called_once()
                        
                        # Verify email was NOT called
                        mock_email.send_intake_email.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_tenant_without_core_sync_uses_email(self):
        """Test that tenants without core_intake_sync use email fallback."""
        call_id = "test-call-456"
        tenant_id = "tenant-without-sync"
        
        # Mock feature check to return False (no core_intake_sync)
        with patch('routes.api.voice_session._check_tenant_has_core_sync') as mock_check:
            mock_check.return_value = False
            
            # Mock email service
            with patch('services.email_fallback.get_email_fallback_service') as mock_email_service:
                mock_email = AsyncMock()
                mock_email.send_intake_email.return_value = {
                    "success": True,
                    "email_id": "email_123",
                    "recipients": ["<EMAIL>"],
                    "subject": "📞 New intake from John Doe"
                }
                mock_email_service.return_value = mock_email
                
                # Mock webhook service to ensure it's not called
                with patch('services.intake_webhook.get_intake_webhook_service') as mock_webhook_service:
                    mock_webhook = AsyncMock()
                    mock_webhook_service.return_value = mock_webhook
                    
                    # Mock event builder
                    with patch('services.intake_event_builder.get_intake_event_builder') as mock_builder:
                        mock_event_builder = AsyncMock()
                        mock_event = AsyncMock()
                        mock_event_builder.build_call_completion_event.return_value = mock_event
                        mock_event_builder.get_recording_url_for_call.return_value = None
                        mock_event_builder.extract_transcript_from_pipeline_data.return_value = None
                        mock_event_builder.extract_intent_from_pipeline_data.return_value = None
                        mock_builder.return_value = mock_event_builder
                        
                        # Execute the function
                        await _send_intake_event_for_call_end(
                            call_id=call_id,
                            tenant_id=tenant_id,
                            caller_number="+1234567890",
                            caller_name="John Doe",
                            transcript="Test transcript",
                            intent="test_intent"
                        )
                        
                        # Verify email was called
                        mock_email.send_intake_email.assert_called_once()
                        
                        # Verify webhook was NOT called
                        mock_webhook.send_intake_event.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_feature_check_with_core_sync(self):
        """Test feature flag check when tenant has core_intake_sync."""
        tenant_id = "tenant-with-sync"
        
        # Mock the middleware to return features including core_intake_sync
        with patch('avr.api.middleware.subscription_check.SubscriptionCheckMiddleware') as mock_middleware_class:
            mock_middleware = AsyncMock()
            mock_middleware._get_tenant_features.return_value = ["voice_intake", "core_intake_sync"]
            mock_middleware_class.return_value = mock_middleware
            
            # Mock environment variables
            with patch.dict('os.environ', {
                'CORE_SUBS_API_URL': 'https://core-api.example.com',
                'REDIS_URL': 'redis://localhost:6379/0'
            }):
                result = await _check_tenant_has_core_sync(tenant_id)
                
                assert result is True
                mock_middleware._get_tenant_features.assert_called_once_with(tenant_id)
    
    @pytest.mark.asyncio
    async def test_feature_check_without_core_sync(self):
        """Test feature flag check when tenant lacks core_intake_sync."""
        tenant_id = "tenant-without-sync"
        
        # Mock the middleware to return features without core_intake_sync
        with patch('avr.api.middleware.subscription_check.SubscriptionCheckMiddleware') as mock_middleware_class:
            mock_middleware = AsyncMock()
            mock_middleware._get_tenant_features.return_value = ["voice_intake"]
            mock_middleware_class.return_value = mock_middleware
            
            # Mock environment variables
            with patch.dict('os.environ', {
                'CORE_SUBS_API_URL': 'https://core-api.example.com',
                'REDIS_URL': 'redis://localhost:6379/0'
            }):
                result = await _check_tenant_has_core_sync(tenant_id)
                
                assert result is False
                mock_middleware._get_tenant_features.assert_called_once_with(tenant_id)
    
    @pytest.mark.asyncio
    async def test_feature_check_missing_config(self):
        """Test feature flag check when configuration is missing."""
        tenant_id = "test-tenant"
        
        # Mock missing environment variables
        with patch.dict('os.environ', {}, clear=True):
            result = await _check_tenant_has_core_sync(tenant_id)
            
            # Should default to False (email fallback) when config missing
            assert result is False
    
    @pytest.mark.asyncio
    async def test_feature_check_api_error(self):
        """Test feature flag check when API call fails."""
        tenant_id = "test-tenant"
        
        # Mock the middleware to raise an exception
        with patch('avr.api.middleware.subscription_check.SubscriptionCheckMiddleware') as mock_middleware_class:
            mock_middleware = AsyncMock()
            mock_middleware._get_tenant_features.side_effect = Exception("API Error")
            mock_middleware_class.return_value = mock_middleware
            
            # Mock environment variables
            with patch.dict('os.environ', {
                'CORE_SUBS_API_URL': 'https://core-api.example.com',
                'REDIS_URL': 'redis://localhost:6379/0'
            }):
                result = await _check_tenant_has_core_sync(tenant_id)
                
                # Should default to False (email fallback) on error
                assert result is False
    
    @pytest.mark.asyncio
    async def test_email_fallback_with_recording(self):
        """Test email fallback includes recording URL when available."""
        call_id = "test-call-789"
        tenant_id = "tenant-without-sync"
        recording_url = "https://recordings.example.com/call789.mp3"
        
        # Mock feature check to return False
        with patch('routes.api.voice_session._check_tenant_has_core_sync') as mock_check:
            mock_check.return_value = False
            
            # Mock email service
            with patch('services.email_fallback.get_email_fallback_service') as mock_email_service:
                mock_email = AsyncMock()
                mock_email.send_intake_email.return_value = {
                    "success": True,
                    "email_id": "email_456",
                    "recipients": ["<EMAIL>"]
                }
                mock_email_service.return_value = mock_email
                
                # Mock event builder with recording URL
                with patch('services.intake_event_builder.get_intake_event_builder') as mock_builder:
                    mock_event_builder = AsyncMock()
                    mock_event = AsyncMock()
                    mock_event.recording_url = recording_url
                    mock_event_builder.build_call_completion_event.return_value = mock_event
                    mock_event_builder.get_recording_url_for_call.return_value = recording_url
                    mock_event_builder.extract_transcript_from_pipeline_data.return_value = None
                    mock_event_builder.extract_intent_from_pipeline_data.return_value = None
                    mock_builder.return_value = mock_event_builder
                    
                    # Execute the function
                    await _send_intake_event_for_call_end(
                        call_id=call_id,
                        tenant_id=tenant_id,
                        caller_number="+1234567890",
                        caller_name="Jane Doe"
                    )
                    
                    # Verify email was called with the event containing recording URL
                    mock_email.send_intake_email.assert_called_once()
                    call_args = mock_email.send_intake_email.call_args
                    event_arg = call_args[0][0]  # First positional argument (the event)
                    tenant_arg = call_args[0][1]  # Second positional argument (tenant_id)
                    
                    assert tenant_arg == tenant_id
                    # The event should have the recording URL
                    assert event_arg.recording_url == recording_url
    
    @pytest.mark.asyncio
    async def test_email_fallback_error_handling(self):
        """Test error handling in email fallback."""
        call_id = "test-call-error"
        tenant_id = "tenant-error"
        
        # Mock feature check to return False
        with patch('routes.api.voice_session._check_tenant_has_core_sync') as mock_check:
            mock_check.return_value = False
            
            # Mock email service to raise an error
            with patch('services.email_fallback.get_email_fallback_service') as mock_email_service:
                mock_email = AsyncMock()
                mock_email.send_intake_email.return_value = {
                    "success": False,
                    "error": "Failed to send email"
                }
                mock_email_service.return_value = mock_email
                
                # Mock event builder
                with patch('services.intake_event_builder.get_intake_event_builder') as mock_builder:
                    mock_event_builder = AsyncMock()
                    mock_event = AsyncMock()
                    mock_event_builder.build_call_completion_event.return_value = mock_event
                    mock_event_builder.get_recording_url_for_call.return_value = None
                    mock_event_builder.extract_transcript_from_pipeline_data.return_value = None
                    mock_event_builder.extract_intent_from_pipeline_data.return_value = None
                    mock_builder.return_value = mock_event_builder
                    
                    # Execute the function - should not raise exception
                    await _send_intake_event_for_call_end(
                        call_id=call_id,
                        tenant_id=tenant_id,
                        caller_number="+1234567890",
                        caller_name="Error Test"
                    )
                    
                    # Verify email was attempted
                    mock_email.send_intake_email.assert_called_once()
