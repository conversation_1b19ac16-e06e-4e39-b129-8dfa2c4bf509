"""
Tests for external number API endpoints.

Tests cover:
- POST /api/v1/external-numbers (create)
- POST /api/v1/external-numbers/{did}/verify (verify)
- GET /api/v1/external-numbers (list)
- GET /api/v1/external-numbers/{did}/sip-uri (get SIP URI)
- Authentication and authorization
- Error handling and validation
"""
import pytest
from unittest.mock import AsyncMock, patch
from uuid import uuid4, UUID
from datetime import datetime, timezone

from fastapi.testclient import TestClient
from fastapi import FastAPI

from routers.external_numbers import router
from packages.shared.models import ExternalNumber, ExternalNumberStatus
from services.external_number_service import (
    DuplicateNumberError,
    NumberNotFoundError,
    InvalidVerificationCodeError,
    ExternalNumberError
)


@pytest.fixture
def app():
    """FastAPI test application."""
    test_app = FastAPI()
    test_app.include_router(router)
    return test_app


@pytest.fixture
def client(app):
    """Test client."""
    return TestClient(app)


@pytest.fixture
def tenant_id():
    """Sample tenant ID."""
    return str(uuid4())


@pytest.fixture
def headers(tenant_id):
    """Request headers with tenant ID."""
    return {"X-Tenant-ID": tenant_id}


@pytest.fixture
def sample_external_number(tenant_id):
    """Sample external number."""
    return ExternalNumber(
        id=uuid4(),
        tenant_id=uuid4(),
        did="+15558675309",
        status=ExternalNumberStatus.PENDING,
        verification_code="123456",
        created_at=datetime.now(timezone.utc)
    )


class TestCreateExternalNumber:
    """Tests for POST /api/v1/external-numbers endpoint."""
    
    @patch('routers.external_numbers.get_db')
    @patch('routers.external_numbers.ExternalNumberService')
    def test_create_external_number_success(self, mock_service_class, mock_get_db, client, headers):
        """Test successful external number creation."""
        # Setup
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service
        
        mock_external_number = AsyncMock()
        mock_external_number.id = uuid4()
        mock_external_number.tenant_id = uuid4()
        mock_external_number.did = "+15558675309"
        mock_external_number.status = ExternalNumberStatus.PENDING
        mock_external_number.verification_code = "123456"
        mock_external_number.created_at = datetime.now(timezone.utc)
        mock_external_number.verified_at = None
        
        mock_service.create_external_number.return_value = mock_external_number
        mock_service.generate_sip_uri = lambda x: "sip:+15558675309@voice-svc:5060"
        
        # Execute
        response = client.post(
            "/api/v1/external-numbers/",
            json={"did": "+15558675309"},
            headers=headers
        )
        
        # Verify
        assert response.status_code == 201
        data = response.json()
        assert data["did"] == "+15558675309"
        assert data["status"] == "pending"
        assert data["verification_code"] == "123456"
        assert data["sip_uri"] == "sip:+15558675309@voice-svc:5060"
    
    @patch('routers.external_numbers.get_db')
    @patch('routers.external_numbers.ExternalNumberService')
    def test_create_external_number_duplicate(self, mock_service_class, mock_get_db, client, headers):
        """Test creation with duplicate number."""
        # Setup
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service
        mock_service.create_external_number.side_effect = DuplicateNumberError("Number already exists")
        
        # Execute
        response = client.post(
            "/api/v1/external-numbers/",
            json={"did": "+15558675309"},
            headers=headers
        )
        
        # Verify
        assert response.status_code == 409
        assert "already" in response.json()["detail"].lower()
    
    @pytest.mark.parametrize("invalid_did", [
        "1234567890",  # No +
        "+1900555123",  # Premium number
        "+123",  # Too short
        "",  # Empty
    ])
    def test_create_external_number_invalid_did(self, client, headers, invalid_did):
        """Test creation with invalid DID."""
        response = client.post(
            "/api/v1/external-numbers/",
            json={"did": invalid_did},
            headers=headers
        )
        
        assert response.status_code == 422  # Validation error
    
    def test_create_external_number_missing_tenant_id(self, client):
        """Test creation without tenant ID header."""
        response = client.post(
            "/api/v1/external-numbers/",
            json={"did": "+15558675309"}
        )
        
        assert response.status_code == 422  # Missing header


class TestVerifyExternalNumber:
    """Tests for POST /api/v1/external-numbers/{did}/verify endpoint."""
    
    @patch('routers.external_numbers.get_db')
    @patch('routers.external_numbers.ExternalNumberService')
    def test_verify_external_number_success(self, mock_service_class, mock_get_db, client, headers):
        """Test successful verification."""
        # Setup
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service
        mock_service.verify_code.return_value = True
        
        # Execute
        response = client.post(
            "/api/v1/external-numbers/+15558675309/verify",
            json={"code": "123456"},
            headers=headers
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert "successfully verified" in data["message"].lower()
    
    @patch('routers.external_numbers.get_db')
    @patch('routers.external_numbers.ExternalNumberService')
    def test_verify_external_number_invalid_code(self, mock_service_class, mock_get_db, client, headers):
        """Test verification with invalid code."""
        # Setup
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service
        mock_service.verify_code.side_effect = InvalidVerificationCodeError("Invalid code")
        
        # Execute
        response = client.post(
            "/api/v1/external-numbers/+15558675309/verify",
            json={"code": "123456"},  # Use valid format to bypass Pydantic validation
            headers=headers
        )

        # Verify
        assert response.status_code == 400
    
    @patch('routers.external_numbers.get_db')
    @patch('routers.external_numbers.ExternalNumberService')
    def test_verify_external_number_not_found(self, mock_service_class, mock_get_db, client, headers):
        """Test verification of non-existent number."""
        # Setup
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service
        mock_service.verify_code.side_effect = NumberNotFoundError("Not found")
        
        # Execute
        response = client.post(
            "/api/v1/external-numbers/+15558675309/verify",
            json={"code": "123456"},
            headers=headers
        )
        
        # Verify
        assert response.status_code == 404


class TestListExternalNumbers:
    """Tests for GET /api/v1/external-numbers endpoint."""
    
    @patch('routers.external_numbers.get_db')
    @patch('routers.external_numbers.ExternalNumberService')
    def test_list_external_numbers_success(self, mock_service_class, mock_get_db, client, headers):
        """Test successful listing of external numbers."""
        # Setup
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service
        
        mock_numbers = [
            AsyncMock(
                id=uuid4(),
                did="+15558675309",
                status=ExternalNumberStatus.VERIFIED,
                created_at=datetime.now(timezone.utc),
                verified_at=datetime.now(timezone.utc)
            ),
            AsyncMock(
                id=uuid4(),
                did="+15558675310",
                status=ExternalNumberStatus.PENDING,
                created_at=datetime.now(timezone.utc),
                verified_at=None
            )
        ]
        
        mock_service.get_external_numbers.return_value = mock_numbers
        
        # Execute
        response = client.get("/api/v1/external-numbers/", headers=headers)
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["total"] == 2
        assert len(data["numbers"]) == 2
        assert data["numbers"][0]["status"] == "verified"
        assert data["numbers"][1]["status"] == "pending"
    
    @patch('routers.external_numbers.get_db')
    @patch('routers.external_numbers.ExternalNumberService')
    def test_list_external_numbers_empty(self, mock_service_class, mock_get_db, client, headers):
        """Test listing when no external numbers exist."""
        # Setup
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service
        mock_service.get_external_numbers.return_value = []
        
        # Execute
        response = client.get("/api/v1/external-numbers/", headers=headers)
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["total"] == 0
        assert data["numbers"] == []


class TestGetSipUri:
    """Tests for GET /api/v1/external-numbers/{did}/sip-uri endpoint."""
    
    @patch('routers.external_numbers.get_db')
    @patch('routers.external_numbers.ExternalNumberService')
    def test_get_sip_uri_success(self, mock_service_class, mock_get_db, client, headers, tenant_id):
        """Test successful SIP URI retrieval."""
        # Setup
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service

        mock_external_number = AsyncMock()
        mock_external_number.tenant_id = UUID(tenant_id)  # Use the same tenant_id from headers
        mock_external_number.verification_code = "123456"

        mock_service.get_external_number_by_did.return_value = mock_external_number
        mock_service.generate_sip_uri = lambda x: "sip:+15558675309@voice-svc:5060"
        
        # Execute
        response = client.get("/api/v1/external-numbers/+15558675309/sip-uri", headers=headers)
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["sip_uri"] == "sip:+15558675309@voice-svc:5060"
        assert data["verification_code"] == "123456"
        assert "Configure your phone carrier" in data["instructions"]
    
    @patch('routers.external_numbers.get_db')
    @patch('routers.external_numbers.ExternalNumberService')
    def test_get_sip_uri_not_found(self, mock_service_class, mock_get_db, client, headers):
        """Test SIP URI retrieval for non-existent number."""
        # Setup
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service
        mock_service.get_external_number_by_did.return_value = None
        
        # Execute
        response = client.get("/api/v1/external-numbers/+15558675309/sip-uri", headers=headers)
        
        # Verify
        assert response.status_code == 404
