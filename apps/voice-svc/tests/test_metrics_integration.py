"""
Integration tests for metrics endpoint and instrumentation.
"""
import pytest
import asyncio
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import AsyncMock, patch

from main import app
from services.metrics_service import get_metrics_service, reset_metrics_service
from services.queue_service import get_queue_service


class TestMetricsIntegration:
    """Integration tests for metrics functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        reset_metrics_service()
        self.client = TestClient(app)
    
    def teardown_method(self):
        """Clean up after tests."""
        reset_metrics_service()
    
    def test_metrics_endpoint_exists(self):
        """Test that the /metrics endpoint is available."""
        response = self.client.get("/metrics")
        
        assert response.status_code == 200
        assert "text/plain" in response.headers["content-type"]
        assert "avr_" in response.text
    
    def test_metrics_endpoint_prometheus_format(self):
        """Test that metrics endpoint returns valid Prometheus format."""
        response = self.client.get("/metrics")
        
        assert response.status_code == 200
        
        # Check for Prometheus format elements
        content = response.text
        assert "# HELP" in content
        assert "# TYPE" in content
        
        # Check for our specific metrics
        assert "avr_call_latency_seconds" in content
        assert "avr_queue_depth" in content
        assert "avr_active_calls" in content
        assert "avr_calls_rejected_total" in content
        assert "avr_call_recordings_purged_total" in content
        assert "avr_webhook_retry_total" in content
        assert "avr_job_duration_seconds" in content
    
    def test_metrics_with_queue_operations(self):
        """Test that queue operations update metrics."""
        # Get metrics service and record some queue metrics
        metrics_service = get_metrics_service()
        
        # Simulate queue operations
        tenant_id = "test-tenant-123"
        metrics_service.update_queue_depth(tenant_id, 3)
        metrics_service.update_active_calls(tenant_id, 2)
        metrics_service.increment_calls_rejected(tenant_id, "queue_full")
        
        # Get metrics endpoint
        response = self.client.get("/metrics")
        
        assert response.status_code == 200
        content = response.text
        
        # Check that our metrics are present
        assert f'tenant_id="{tenant_id}"' in content
        assert 'avr_queue_depth{tenant_id="test-tenant-123"} 3.0' in content
        assert 'avr_active_calls{tenant_id="test-tenant-123"} 2.0' in content
        assert 'avr_calls_rejected_total{reason="queue_full",tenant_id="test-tenant-123"} 1.0' in content
    
    def test_metrics_with_job_operations(self):
        """Test that job operations update metrics."""
        metrics_service = get_metrics_service()
        
        # Simulate job execution
        job_name = "test_purge"
        duration = 1.5
        metrics_service.record_job_duration(job_name, duration)
        
        # Get metrics endpoint
        response = self.client.get("/metrics")
        
        assert response.status_code == 200
        content = response.text
        
        # Check that job metrics are present
        assert f'job="{job_name}"' in content
        assert "avr_job_duration_seconds_bucket" in content
    
    def test_metrics_context_managers(self):
        """Test that context managers work correctly."""
        metrics_service = get_metrics_service()
        
        # Test job timing context manager
        with metrics_service.time_job("test_context_job"):
            import time
            time.sleep(0.01)  # Small delay to ensure measurable duration
        
        # Test call latency context manager
        with metrics_service.time_call_latency("test-tenant"):
            import time
            time.sleep(0.01)  # Small delay to ensure measurable duration
        
        # Get metrics endpoint
        response = self.client.get("/metrics")
        
        assert response.status_code == 200
        content = response.text
        
        # Check that timed metrics are present
        assert 'job="test_context_job"' in content
        assert 'tenant_id="test-tenant"' in content
    
    @pytest.mark.asyncio
    async def test_queue_service_metrics_integration(self):
        """Test that queue service operations update metrics correctly."""
        # Mock Redis to avoid actual Redis dependency
        with patch('services.queue_service.redis.from_url') as mock_redis_factory:
            mock_redis = AsyncMock()
            mock_redis_factory.return_value = mock_redis
            mock_redis.ping.return_value = True
            mock_redis.incr.return_value = 2
            mock_redis.expire.return_value = True
            mock_redis.decr.return_value = 1
            
            queue_service = get_queue_service()
            await queue_service.connect()
            
            # Perform queue operations that should update metrics
            tenant_id = "integration-test-tenant"
            
            # Test increment active
            count = await queue_service.increment_active(tenant_id)
            assert count == 2
            
            # Test decrement active
            count = await queue_service.decrement_active(tenant_id)
            assert count == 1
            
            # Get metrics to verify they were updated
            response = self.client.get("/metrics")
            assert response.status_code == 200
            content = response.text
            
            # Check that metrics were updated by queue operations
            assert f'tenant_id="{tenant_id}"' in content
    
    def test_health_endpoint_still_works(self):
        """Test that adding metrics doesn't break existing endpoints."""
        response = self.client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "ok"
        assert "timestamp" in data
    
    def test_root_endpoint_still_works(self):
        """Test that root endpoint still works with metrics enabled."""
        response = self.client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
    
    def test_metrics_endpoint_performance(self):
        """Test that metrics endpoint responds quickly."""
        import time
        
        # Record some metrics first
        metrics_service = get_metrics_service()
        for i in range(10):
            metrics_service.update_queue_depth(f"tenant-{i}", i)
            metrics_service.update_active_calls(f"tenant-{i}", i % 4)
        
        # Time the metrics endpoint
        start_time = time.time()
        response = self.client.get("/metrics")
        end_time = time.time()
        
        assert response.status_code == 200
        
        # Should respond in under 100ms even with multiple metrics
        duration = end_time - start_time
        assert duration < 0.1, f"Metrics endpoint took {duration:.3f}s, expected < 0.1s"
    
    def test_metrics_endpoint_concurrent_access(self):
        """Test that metrics endpoint handles concurrent access."""
        import threading
        import time
        
        results = []
        
        def make_request():
            try:
                response = self.client.get("/metrics")
                results.append(response.status_code)
            except Exception as e:
                results.append(str(e))
        
        # Create multiple threads to access metrics concurrently
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All requests should succeed
        assert len(results) == 5
        assert all(result == 200 for result in results)
    
    def test_metrics_survive_service_restart(self):
        """Test that metrics service can be reset and reinitialized."""
        # Record some initial metrics
        metrics_service = get_metrics_service()
        metrics_service.update_queue_depth("test-tenant", 5)
        
        # Get initial metrics
        response1 = self.client.get("/metrics")
        assert response1.status_code == 200
        assert "test-tenant" in response1.text
        
        # Reset metrics service (simulating restart)
        reset_metrics_service()
        
        # Metrics endpoint should still work
        response2 = self.client.get("/metrics")
        assert response2.status_code == 200
        
        # Old metrics should be gone, but endpoint should work
        # (Note: In a real restart, metrics would be reset anyway)
