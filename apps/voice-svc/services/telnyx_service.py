"""
Telnyx service for DID search and purchase operations.

This service handles:
- Searching for available phone numbers by state
- Purchasing/provisioning phone numbers
- Managing number status and metadata
"""
import logging
import os
from typing import List, Optional
from uuid import UUID
import httpx
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from packages.shared.models import TelnyxNumber, TelnyxNumberStatus
from schemas.telnyx import TelnyxAvailableNumber

logger = logging.getLogger(__name__)


class TelnyxAPIError(Exception):
    """Custom exception for Telnyx API errors."""

    def __init__(
            self,
            message: str,
            status_code: int = None,
            error_code: str = None):
        self.message = message
        self.status_code = status_code
        self.error_code = error_code
        super().__init__(self.message)


class TelnyxService:
    """Service for interacting with Telnyx API and managing DID numbers."""

    def __init__(self):
        self.api_key = os.getenv("TELNYX_API_KEY")
        if not self.api_key:
            raise ValueError("TELNYX_API_KEY environment variable is required")

        self.app_id = os.getenv("TELNYX_APP_ID")
        if not self.app_id:
            logger.warning("TELNYX_APP_ID not set - SIP functionality will be limited")

        self.base_url = "https://api.telnyx.com/v2"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

    @retry(stop=stop_after_attempt(2),
           wait=wait_exponential(multiplier=1,
                                 min=1,
                                 max=10),
           retry=retry_if_exception_type((httpx.HTTPStatusError,
                                          httpx.RequestError)),
           reraise=True)
    async def search_numbers(
            self,
            state: str,
            city: Optional[str] = None,
            limit: int = 10) -> List[TelnyxAvailableNumber]:
        """
        Search for available phone numbers in a specific US state and optionally city.

        Args:
            state: Two-letter US state code (e.g., 'TX', 'CA')
            city: Optional city name (e.g., 'Houston', 'Austin')
            limit: Maximum number of results to return (default: 10, max: 250)

        Returns:
            List of available phone numbers with metadata

        Raises:
            TelnyxAPIError: If the API request fails
        """
        if not state or len(state) != 2:
            raise ValueError("State must be a 2-letter US state code")

        if limit < 1 or limit > 250:
            raise ValueError("Limit must be between 1 and 250")

        if city and (len(city.strip()) < 2 or len(city.strip()) > 50):
            raise ValueError("City must be between 2 and 50 characters")

        params = {
            "filter[country_code]": "US",
            "filter[administrative_area]": state.upper(),
            "filter[number_type]": "local",
            "filter[features][]": ["voice", "sms"],
            "page[size]": min(limit, 250),
        }

        # Add city filter if provided
        if city:
            params["filter[locality]"] = city.strip().title()

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(
                    f"{self.base_url}/available_phone_numbers",
                    headers=self.headers,
                    params=params
                )

                if response.status_code == 404:
                    location = f"state: {state}" + (f", city: {city}" if city else "")
                    logger.warning(f"No numbers available for {location}")
                    return []

                response.raise_for_status()
                data = response.json()

                numbers = []
                for item in data.get("data", []):
                    numbers.append(TelnyxAvailableNumber(
                        phone_number=item["phone_number"],
                        region_information=[{
                            "region_type": info.get("region_type"),
                            "region_name": info.get("region_name")
                        } for info in item.get("region_information", [])],
                        features=item.get("features", []),
                        cost_information=item.get("cost_information", {}),
                        best_effort=item.get("best_effort", False),
                        quickship=item.get("quickship", False),
                        reservable=item.get("reservable", False)
                    ))

                location = f"state {state}" + (f", city {city}" if city else "")
                logger.info(f"Found {len(numbers)} available numbers for {location}")
                return numbers

        except httpx.HTTPStatusError as e:
            error_msg = f"Telnyx API error: {e.response.status_code}"
            try:
                error_data = e.response.json()
                error_msg += f" - {error_data.get('errors', [{}])[0].get('detail', 'Unknown error')}"
            except BaseException:
                error_msg += f" - {e.response.text}"

            logger.error(error_msg)
            raise TelnyxAPIError(error_msg, e.response.status_code)

        except httpx.RequestError as e:
            error_msg = f"Network error communicating with Telnyx: {str(e)}"
            logger.error(error_msg)
            raise TelnyxAPIError(error_msg)

    @retry(stop=stop_after_attempt(2),
           wait=wait_exponential(multiplier=1,
                                 min=1,
                                 max=10),
           retry=retry_if_exception_type((httpx.HTTPStatusError,
                                          httpx.RequestError)),
           reraise=True)
    async def purchase_number(self, tenant_id: UUID, did: str) -> TelnyxNumber:
        """
        Purchase a phone number from Telnyx.

        Args:
            tenant_id: UUID of the tenant purchasing the number
            did: Phone number to purchase (E.164 format, e.g., '+18325550123')

        Returns:
            TelnyxNumber model instance with purchase details

        Raises:
            TelnyxAPIError: If the purchase fails
        """
        if not did.startswith('+'):
            raise ValueError("DID must be in E.164 format (starting with +)")

        # Extract state from DID (simplified - in production you'd use a proper lookup)
        # For now, we'll extract from area code mapping
        state = self._extract_state_from_did(did)

        payload = {
            "phone_number": did,
            "connection_id": None,  # Will be set when configuring forwarding
            "customer_reference": str(tenant_id),
            "billing_group_id": None,  # Optional: for billing organization
        }

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/phone_numbers",
                    headers=self.headers,
                    json=payload
                )

                if response.status_code == 409:
                    # Number already exists/purchased
                    error_msg = f"Phone number {did} is already purchased or unavailable"
                    logger.warning(error_msg)
                    raise TelnyxAPIError(error_msg, 409, "number_unavailable")

                response.raise_for_status()
                data = response.json()

                # Extract Telnyx response data
                telnyx_data = data.get("data", {})
                telnyx_number_id = telnyx_data.get("id")

                # Create TelnyxNumber model instance
                telnyx_number = TelnyxNumber(
                    tenant_id=tenant_id,
                    did=did,
                    state=state,
                    forwarding_number=None,  # Will be set in T-2
                    status=TelnyxNumberStatus.PENDING,
                    telnyx_number_id=telnyx_number_id,
                    metadata_={
                        "purchase_response": telnyx_data,
                        "purchased_at": telnyx_data.get("created_at"),
                        "phone_number_type": telnyx_data.get("phone_number_type"),
                        "regulatory_requirements": telnyx_data.get("regulatory_requirements", []),
                    }
                )

                logger.info(
                    f"Successfully purchased number {did} for tenant {tenant_id}")
                return telnyx_number

        except httpx.HTTPStatusError as e:
            error_msg = f"Failed to purchase number {did}: {e.response.status_code}"
            try:
                error_data = e.response.json()
                errors = error_data.get('errors', [{}])
                if errors:
                    error_detail = errors[0].get('detail', 'Unknown error')
                    error_code = errors[0].get('code', 'unknown')
                    error_msg += f" - {error_detail}"

                    # Map specific Telnyx errors to our error codes
                    if "already exists" in error_detail.lower() or error_code == "10015":
                        raise TelnyxAPIError(
                            error_msg, 409, "number_unavailable")

            except BaseException:
                error_msg += f" - {e.response.text}"

            logger.error(error_msg)
            raise TelnyxAPIError(error_msg, e.response.status_code)

        except httpx.RequestError as e:
            error_msg = f"Network error purchasing number {did}: {str(e)}"
            logger.error(error_msg)
            raise TelnyxAPIError(error_msg)

    def _extract_state_from_did(self, did: str) -> str:
        """
        Extract state code from DID based on area code.
        This is a simplified implementation - in production you'd use a comprehensive lookup.
        """
        # Remove + and country code for US numbers
        if did.startswith('+1'):
            area_code = did[2:5]
        else:
            area_code = did[1:4] if did.startswith('+') else did[:3]

        # Simplified area code to state mapping (just a few examples)
        area_code_map = {
            '832': 'TX', '713': 'TX', '281': 'TX', '409': 'TX', '430': 'TX',
            '214': 'TX', '469': 'TX', '972': 'TX', '945': 'TX',
            '415': 'CA', '510': 'CA', '650': 'CA', '925': 'CA', '408': 'CA',
            '212': 'NY', '646': 'NY', '917': 'NY', '718': 'NY', '347': 'NY',
            '305': 'FL', '786': 'FL', '954': 'FL', '561': 'FL', '772': 'FL',
        }

        return area_code_map.get(area_code, 'TX')  # Default to TX if not found

    @retry(
        stop=stop_after_attempt(2),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((httpx.HTTPStatusError, httpx.RequestError)),
        reraise=True
    )
    async def attach_forwarding(self, did: str, forward_to: str) -> dict:
        """
        Attach call forwarding to a Telnyx phone number.

        Args:
            did: Phone number in E.164 format (e.g., '+18325550123')
            forward_to: Destination number for call forwarding in E.164 format

        Returns:
            Dictionary with connection details from Telnyx API

        Raises:
            TelnyxAPIError: If the API request fails
            ValueError: If parameters are invalid
        """
        if not did.startswith('+'):
            raise ValueError("DID must be in E.164 format (starting with +)")

        if not forward_to.startswith('+'):
            raise ValueError("Forward-to number must be in E.164 format (starting with +)")

        if did == forward_to:
            raise ValueError("Cannot forward a number to itself")

        # Check for premium rate numbers
        if forward_to.startswith('+1900'):
            raise ValueError("Cannot forward to premium rate numbers (1-900)")

        # First, get the phone number details to find the number ID
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Get phone number details
                search_response = await client.get(
                    f"{self.base_url}/phone_numbers",
                    headers=self.headers,
                    params={"filter[phone_number]": did}
                )
                search_response.raise_for_status()
                search_data = search_response.json()

                phone_numbers = search_data.get("data", [])
                if not phone_numbers:
                    raise TelnyxAPIError(f"Phone number {did} not found in Telnyx", 404)

                phone_number_id = phone_numbers[0]["id"]

                # Create or update number configuration for forwarding
                connection_name = f"Forward_{did.replace('+', '')}"
                config_payload = {
                    "connection_name": connection_name,
                    "call_forwarding": {
                        "phone_number": forward_to
                    }
                }

                # Update the phone number configuration
                config_response = await client.patch(
                    f"{self.base_url}/phone_numbers/{phone_number_id}",
                    headers=self.headers,
                    json=config_payload
                )
                config_response.raise_for_status()
                config_data = config_response.json()

                logger.info(f"Successfully configured forwarding for {did} to {forward_to}")
                return config_data.get("data", {})

        except httpx.HTTPStatusError as e:
            error_msg = f"Failed to configure forwarding for {did}: {e.response.status_code}"
            try:
                error_data = e.response.json()
                errors = error_data.get('errors', [{}])
                if errors:
                    error_detail = errors[0].get('detail', 'Unknown error')
                    error_msg += f" - {error_detail}"
            except BaseException:
                error_msg += f" - {e.response.text}"

            logger.error(error_msg)
            raise TelnyxAPIError(error_msg, e.response.status_code)

        except httpx.RequestError as e:
            error_msg = f"Network error configuring forwarding for {did}: {str(e)}"
            logger.error(error_msg)
            raise TelnyxAPIError(error_msg)

    @retry(
        stop=stop_after_attempt(2),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((httpx.HTTPStatusError, httpx.RequestError)),
        reraise=True
    )
    async def attach_sip_app(self, did: str) -> dict:
        """
        Attach a Telnyx phone number to the SIP application.

        Args:
            did: Phone number in E.164 format (e.g., '+18325550123')

        Returns:
            Dictionary with connection details from Telnyx API

        Raises:
            TelnyxAPIError: If the API request fails
            ValueError: If parameters are invalid
        """
        if not did.startswith('+'):
            raise ValueError("DID must be in E.164 format (starting with +)")

        if not self.app_id:
            raise ValueError("TELNYX_APP_ID environment variable is required for SIP functionality")

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Get phone number details
                search_response = await client.get(
                    f"{self.base_url}/phone_numbers",
                    headers=self.headers,
                    params={"filter[phone_number]": did}
                )
                search_response.raise_for_status()
                search_data = search_response.json()

                phone_numbers = search_data.get("data", [])
                if not phone_numbers:
                    raise TelnyxAPIError(f"Phone number {did} not found in Telnyx", 404)

                phone_number_id = phone_numbers[0]["id"]

                # Update the phone number to use SIP application
                config_payload = {
                    "connection_id": self.app_id,
                    "call_forwarding": None  # Remove any existing forwarding
                }

                config_response = await client.patch(
                    f"{self.base_url}/phone_numbers/{phone_number_id}",
                    headers=self.headers,
                    json=config_payload
                )
                config_response.raise_for_status()
                config_data = config_response.json()

                logger.info(f"Successfully attached {did} to SIP application {self.app_id}")
                return config_data.get("data", {})

        except httpx.HTTPStatusError as e:
            error_msg = f"Failed to attach SIP application for {did}: {e.response.status_code}"
            try:
                error_data = e.response.json()
                errors = error_data.get('errors', [{}])
                if errors:
                    error_detail = errors[0].get('detail', 'Unknown error')
                    error_msg += f" - {error_detail}"
            except BaseException:
                error_msg += f" - {e.response.text}"

            logger.error(error_msg)
            raise TelnyxAPIError(error_msg, e.response.status_code)

        except httpx.RequestError as e:
            error_msg = f"Network error attaching SIP application for {did}: {str(e)}"
            logger.error(error_msg)
            raise TelnyxAPIError(error_msg)
