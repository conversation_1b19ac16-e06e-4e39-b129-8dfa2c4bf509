"""
Call Verification Service for external number verification via IVR.

This service handles:
- Detecting calls to unverified external numbers
- Managing IVR verification flow
- Processing verification code entry
- Updating number status based on verification results
"""
import logging
from typing import Optional, Dict, Any


from sqlalchemy.ext.asyncio import AsyncSession

from packages.shared.models import ExternalNumberStatus
from services.external_number_service import ExternalNumberService, NumberNotFoundError

logger = logging.getLogger(__name__)


class CallVerificationService:
    """Service for handling call-based verification of external numbers."""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.external_service = ExternalNumberService(db)

    async def handle_incoming_call(self, from_number: str, to_number: str) -> Dict[str, Any]:
        """
        Handle incoming call and determine if verification is needed.

        Args:
            from_number: Caller's phone number
            to_number: Called number (DID)

        Returns:
            Dictionary with call handling instructions:
            {
                "requires_verification": bool,
                "verification_code": str (if verification needed),
                "status": str,
                "message": str
            }
        """
        try:
            # Check if this is an external number that needs verification
            external_number = await self.external_service.get_external_number_by_did(to_number)

            if not external_number:
                # Not an external number, proceed with normal call flow
                return {
                    "requires_verification": False,
                    "status": "normal_call",
                    "message": "Proceeding with normal call flow"
                }

            # Check status
            if external_number.status == ExternalNumberStatus.VERIFIED:
                # Already verified, proceed with normal call flow
                return {
                    "requires_verification": False,
                    "status": "verified",
                    "message": "Number is verified, proceeding with normal call flow"
                }

            elif external_number.status == ExternalNumberStatus.PENDING:
                # Mark as verifying and start verification flow
                await self.external_service.mark_number_verifying(to_number)

                return {
                    "requires_verification": True,
                    "verification_code": external_number.verification_code,
                    "status": "verification_required",
                    "message": "Please enter your 6-digit verification code",
                    "prompt": self._get_verification_prompt()
                }

            elif external_number.status == ExternalNumberStatus.VERIFYING:
                # Already in verification process
                return {
                    "requires_verification": True,
                    "verification_code": external_number.verification_code,
                    "status": "verification_in_progress",
                    "message": "Please enter your 6-digit verification code",
                    "prompt": self._get_verification_prompt()
                }

            else:  # FAILED status
                return {
                    "requires_verification": False,
                    "status": "verification_failed",
                    "message": "This number has failed verification. Please contact support.",
                    "error": True
                }

        except Exception as e:
            logger.error(f"Error handling incoming call from {from_number} to {to_number}: {e}")
            return {
                "requires_verification": False,
                "status": "error",
                "message": "Error processing call",
                "error": True
            }

    async def process_verification_input(
        self,
        to_number: str,
        entered_code: str
    ) -> Dict[str, Any]:
        """
        Process verification code entered during call.

        Args:
            to_number: Called number (DID)
            entered_code: Code entered by caller

        Returns:
            Dictionary with verification result:
            {
                "verified": bool,
                "status": str,
                "message": str,
                "continue_call": bool
            }
        """
        try:
            # Attempt verification
            success = await self.external_service.verify_code(to_number, entered_code)

            if success:
                logger.info(f"Successfully verified external number {to_number}")
                return {
                    "verified": True,
                    "status": "verified",
                    "message": "Thank you! Your number has been verified. Connecting you now.",
                    "continue_call": True
                }
            else:
                logger.warning(f"Verification failed for {to_number} with code {entered_code}")
                return {
                    "verified": False,
                    "status": "verification_failed",
                    "message": "Invalid code. Please try again or contact support.",
                    "continue_call": False
                }

        except NumberNotFoundError:
            logger.error(f"Verification attempted for non-existent number: {to_number}")
            return {
                "verified": False,
                "status": "number_not_found",
                "message": "Number not found. Please contact support.",
                "continue_call": False
            }

        except Exception as e:
            logger.error(f"Error processing verification for {to_number}: {e}")
            return {
                "verified": False,
                "status": "error",
                "message": "Verification error. Please try again later.",
                "continue_call": False
            }

    def _get_verification_prompt(self) -> str:
        """
        Get the IVR prompt for verification code entry.

        Returns:
            Prompt text for TTS
        """
        return (
            "Welcome to AI Voice Receptionist. "
            "To verify your phone number, please enter your 6-digit verification code "
            "followed by the pound key."
        )

    def get_verification_success_message(self) -> str:
        """
        Get success message after verification.

        Returns:
            Success message for TTS
        """
        return (
            "Thank you! Your phone number has been successfully verified. "
            "You will now be connected to the AI Voice Receptionist. "
            "Please hold while we transfer your call."
        )

    def get_verification_failure_message(self) -> str:
        """
        Get failure message for invalid verification.

        Returns:
            Failure message for TTS
        """
        return (
            "I'm sorry, but the verification code you entered is not correct. "
            "Please check your verification code and try calling again, "
            "or contact support for assistance. Goodbye."
        )

    async def get_verification_status(self, did: str) -> Optional[Dict[str, Any]]:
        """
        Get current verification status for a DID.

        Args:
            did: Phone number to check

        Returns:
            Status dictionary or None if not found
        """
        try:
            external_number = await self.external_service.get_external_number_by_did(did)

            if not external_number:
                return None

            return {
                "did": external_number.did,
                "status": external_number.status.value,
                "created_at": external_number.created_at.isoformat(),
                "verified_at": (external_number.verified_at.isoformat()
                                if external_number.verified_at else None),
                "requires_verification": external_number.status in [
                    ExternalNumberStatus.PENDING,
                    ExternalNumberStatus.VERIFYING
                ]
            }

        except Exception as e:
            logger.error(f"Error getting verification status for {did}: {e}")
            return None
