"""
External Number Service for bring-your-own-number functionality.

This service handles:
- Creating external number registrations
- Generating verification codes and SIP URIs
- Managing verification status
- E.164 validation and premium number blocking
"""
import logging
import os
import random
import re
from datetime import datetime, timezone
from typing import List, Optional
from uuid import UUID

from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from packages.shared.models import ExternalNumber, ExternalNumberStatus

logger = logging.getLogger(__name__)


class ExternalNumberError(Exception):
    """Base exception for external number operations."""
    pass


class DuplicateNumberError(ExternalNumberError):
    """Raised when trying to register a number that already exists."""
    pass


class NumberNotFoundError(ExternalNumberError):
    """Raised when external number is not found."""
    pass


class InvalidVerificationCodeError(ExternalNumberError):
    """Raised when verification code is invalid."""
    pass


class ExternalNumberService:
    """Service for managing external number registrations."""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.voice_svc_domain = os.getenv("VOICE_SVC_DOMAIN", "voice-svc")
        self.voice_svc_port = os.getenv("VOICE_SVC_PORT", "5060")

    async def create_external_number(
        self,
        tenant_id: UUID,
        did: str
    ) -> ExternalNumber:
        """
        Create a new external number registration.

        Args:
            tenant_id: UUID of the tenant
            did: Phone number in E.164 format

        Returns:
            ExternalNumber model instance

        Raises:
            DuplicateNumberError: If number already exists
            ExternalNumberError: If validation fails
        """
        # Validate E.164 format
        validated_did = self._validate_e164(did)

        # Generate 6-digit verification code
        verification_code = self._generate_verification_code()

        # Create external number record
        external_number = ExternalNumber(
            tenant_id=tenant_id,
            did=validated_did,
            status=ExternalNumberStatus.PENDING,
            verification_code=verification_code,
            created_at=datetime.now(timezone.utc)
        )

        try:
            self.db.add(external_number)
            await self.db.commit()
            await self.db.refresh(external_number)

            logger.info(f"Created external number {validated_did} for tenant {tenant_id}")
            return external_number

        except IntegrityError as e:
            await self.db.rollback()
            if "unique constraint" in str(e).lower() or "duplicate" in str(e).lower():
                raise DuplicateNumberError(f"Phone number {validated_did} is already registered")
            raise ExternalNumberError(f"Failed to create external number: {e}")

    async def mark_number_verifying(self, did: str) -> bool:
        """
        Mark an external number as verifying (when call is received).

        Args:
            did: Phone number in E.164 format

        Returns:
            True if status was updated, False if number not found
        """
        validated_did = self._validate_e164(did)

        result = await self.db.execute(
            update(ExternalNumber)
            .where(
                ExternalNumber.did == validated_did,
                ExternalNumber.status == ExternalNumberStatus.PENDING
            )
            .values(status=ExternalNumberStatus.VERIFYING)
        )

        await self.db.commit()

        if result.rowcount > 0:
            logger.info(f"Marked external number {validated_did} as verifying")
            return True

        logger.warning(f"Could not mark {validated_did} as verifying - not found or wrong status")
        return False

    async def verify_code(self, did: str, code: str) -> bool:
        """
        Verify the 6-digit code for an external number.

        Args:
            did: Phone number in E.164 format
            code: 6-digit verification code

        Returns:
            True if verification successful, False otherwise

        Raises:
            NumberNotFoundError: If number not found
            InvalidVerificationCodeError: If code is invalid
        """
        validated_did = self._validate_e164(did)

        # Get the external number
        result = await self.db.execute(
            select(ExternalNumber).where(ExternalNumber.did == validated_did)
        )
        external_number = result.scalar_one_or_none()

        if not external_number:
            raise NumberNotFoundError(f"External number {validated_did} not found")

        # Check if code matches
        if external_number.verification_code != code:
            logger.warning(f"Invalid verification code for {validated_did}")
            raise InvalidVerificationCodeError("Invalid verification code")

        # Update status to verified
        await self.db.execute(
            update(ExternalNumber)
            .where(ExternalNumber.id == external_number.id)
            .values(
                status=ExternalNumberStatus.VERIFIED,
                verified_at=datetime.now(timezone.utc)
            )
        )

        await self.db.commit()

        logger.info(f"Successfully verified external number {validated_did}")
        return True

    async def get_external_numbers(self, tenant_id: UUID) -> List[ExternalNumber]:
        """
        Get all external numbers for a tenant.

        Args:
            tenant_id: UUID of the tenant

        Returns:
            List of ExternalNumber instances
        """
        result = await self.db.execute(
            select(ExternalNumber)
            .where(ExternalNumber.tenant_id == tenant_id)
            .order_by(ExternalNumber.created_at.desc())
        )

        return result.scalars().all()

    async def get_external_number_by_did(self, did: str) -> Optional[ExternalNumber]:
        """
        Get external number by DID.

        Args:
            did: Phone number in E.164 format

        Returns:
            ExternalNumber instance or None
        """
        validated_did = self._validate_e164(did)

        result = await self.db.execute(
            select(ExternalNumber).where(ExternalNumber.did == validated_did)
        )

        return result.scalar_one_or_none()

    def generate_sip_uri(self, did: str) -> str:
        """
        Generate SIP URI for call forwarding.

        Args:
            did: Phone number in E.164 format

        Returns:
            SIP URI string
        """
        validated_did = self._validate_e164(did)
        return f"sip:{validated_did}@{self.voice_svc_domain}:{self.voice_svc_port}"

    def _validate_e164(self, did: str) -> str:
        """
        Validate and normalize phone number to E.164 format.

        Args:
            did: Phone number string

        Returns:
            Normalized E.164 phone number

        Raises:
            ExternalNumberError: If validation fails
        """
        if not did:
            raise ExternalNumberError("Phone number is required")

        # Remove any spaces or formatting
        clean_did = re.sub(r'[^\d+]', '', did)

        # Must start with + and be 10-15 digits
        if not re.match(r'^\+[1-9]\d{9,14}$', clean_did):
            raise ExternalNumberError("Phone number must be in E.164 format (e.g., +15558675309)")

        # Block premium numbers (900, 976, etc.)
        if re.match(r'^\+1(900|976|550)', clean_did):
            raise ExternalNumberError("Premium numbers are not allowed")

        return clean_did

    def _generate_verification_code(self) -> str:
        """
        Generate a 6-digit verification code.

        Returns:
            6-digit string
        """
        return f"{random.randint(100000, 999999):06d}"
