"""
Email fallback service for AVR-only tenants.

This service provides email delivery of intake events when tenants
do not have the core_intake_sync feature flag enabled.

Features:
- MJML template rendering to HTML
- Resend email delivery
- Admin email lookup from Core API
- Audit logging for email fallback usage
- Error handling and retry logic
"""
import logging
import os
import subprocess
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any

import httpx
import resend
from jinja2 import Environment, FileSystemLoader, select_autoescape

from schemas.intake import IntakeEvent

logger = logging.getLogger(__name__)


class EmailFallbackService:
    """Service for sending intake events via email to AVR-only tenants."""
    
    def __init__(
        self,
        resend_api_key: Optional[str] = None,
        email_from: Optional[str] = None,
        core_api_url: Optional[str] = None,
        admin_url: Optional[str] = None,
        skip_email: bool = False
    ):
        """
        Initialize the email fallback service.
        
        Args:
            resend_api_key: Resend API key for sending emails
            email_from: From email address
            core_api_url: Core API URL for admin email lookup
            admin_url: Admin panel URL for action links
            skip_email: Skip actual email sending (for testing)
        """
        self.resend_api_key = resend_api_key or os.getenv("RESEND_API_KEY")
        self.email_from = email_from or os.getenv("EMAIL_FROM", "<EMAIL>")
        self.core_api_url = core_api_url or os.getenv("CORE_SUBS_API_URL")
        self.admin_url = admin_url or os.getenv("ADMIN_URL", "https://admin.ailex.com")
        self.skip_email = skip_email or os.getenv("SKIP_EMAIL", "false").lower() == "true"
        
        # Initialize Resend
        if self.resend_api_key and not self.skip_email:
            resend.api_key = self.resend_api_key
        
        # Initialize Jinja2 environment for templates
        template_dir = Path(__file__).parent.parent / "templates"
        self.env = Environment(
            loader=FileSystemLoader(template_dir),
            autoescape=select_autoescape(["html", "xml"])
        )
        
        # Add custom filters
        self.env.filters['truncate'] = self._truncate_filter
        
        # HTTP client for Core API calls
        self.http_client: Optional[httpx.AsyncClient] = None
        
        if not self.resend_api_key and not self.skip_email:
            logger.warning("RESEND_API_KEY not set - email fallback will not work")
        
        if not self.core_api_url:
            logger.warning("CORE_SUBS_API_URL not set - admin email lookup will fail")
        
        logger.info(
            f"Email fallback service initialized: from={self.email_from}, "
            f"skip_email={self.skip_email}"
        )
    
    def _truncate_filter(self, text: str, length: int = 100, end: str = "...") -> str:
        """Jinja2 filter to truncate text."""
        if len(text) <= length:
            return text
        return text[:length - len(end)] + end
    
    async def _get_http_client(self) -> httpx.AsyncClient:
        """Get or create HTTP client."""
        if self.http_client is None:
            self.http_client = httpx.AsyncClient(
                timeout=httpx.Timeout(30.0),
                limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
            )
        return self.http_client
    
    async def _get_tenant_admin_emails(self, tenant_id: str) -> List[str]:
        """
        Get admin email addresses for a tenant from Core API.
        
        Args:
            tenant_id: Tenant identifier
            
        Returns:
            List of admin email addresses
        """
        if not self.core_api_url:
            logger.error("Core API URL not configured for admin email lookup")
            return []
        
        try:
            client = await self._get_http_client()
            
            # Query Core API for tenant admin users
            response = await client.get(
                f"{self.core_api_url}/tenant/{tenant_id}/admins",
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                admin_emails = [user.get("email") for user in data.get("admins", []) if user.get("email")]
                
                if admin_emails:
                    logger.info(f"Found {len(admin_emails)} admin emails for tenant {tenant_id}")
                    return admin_emails
                else:
                    logger.warning(f"No admin emails found for tenant {tenant_id}")
                    return []
            else:
                logger.error(
                    f"Failed to fetch admin emails for tenant {tenant_id}: "
                    f"HTTP {response.status_code} - {response.text}"
                )
                return []
                
        except Exception as e:
            logger.error(f"Error fetching admin emails for tenant {tenant_id}: {e}")
            return []
    
    def _render_mjml_to_html(self, mjml_content: str) -> str:
        """
        Render MJML template to HTML using mjml CLI.
        
        Args:
            mjml_content: MJML template content
            
        Returns:
            Rendered HTML content
            
        Raises:
            RuntimeError: If MJML rendering fails
        """
        try:
            # Write MJML to temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.mjml', delete=False) as f:
                f.write(mjml_content)
                mjml_file = f.name
            
            try:
                # Run mjml CLI to convert to HTML
                result = subprocess.run(
                    ['mjml', mjml_file, '--stdout'],
                    capture_output=True,
                    text=True,
                    check=True
                )
                
                return result.stdout
                
            finally:
                # Clean up temporary file
                os.unlink(mjml_file)
                
        except subprocess.CalledProcessError as e:
            logger.error(f"MJML rendering failed: {e.stderr}")
            raise RuntimeError(f"MJML rendering failed: {e.stderr}")
        except FileNotFoundError:
            logger.error("mjml CLI not found - install with: npm install -g mjml")
            raise RuntimeError("mjml CLI not found - install with: npm install -g mjml")
        except Exception as e:
            logger.error(f"Unexpected error rendering MJML: {e}")
            raise RuntimeError(f"Unexpected error rendering MJML: {e}")
    
    async def send_intake_email(self, event: IntakeEvent, tenant_id: str) -> Dict[str, Any]:
        """
        Send intake event email to tenant admins.
        
        Args:
            event: The intake event to send
            tenant_id: Tenant identifier
            
        Returns:
            Dictionary with send result
        """
        try:
            # Get admin emails for the tenant
            admin_emails = await self._get_tenant_admin_emails(tenant_id)
            
            if not admin_emails:
                logger.warning(f"No admin emails found for tenant {tenant_id} - cannot send email fallback")
                return {
                    "success": False,
                    "error": "No admin emails found",
                    "tenant_id": tenant_id,
                    "call_id": str(event.call_id)
                }
            
            # Prepare template context
            context = {
                "call_id": str(event.call_id),
                "tenant_id": tenant_id,
                "caller_name": event.caller_name,
                "caller_number": event.caller_number,
                "transcript": event.transcript,
                "intent": event.intent,
                "booking": event.booking,
                "recording_url": str(event.recording_url) if event.recording_url else None,
                "created_at": event.created_at,
                "admin_url": self.admin_url,
                "call_duration": "Unknown"  # TODO: Calculate from call data
            }
            
            # Render MJML template
            template = self.env.get_template("intake_email.mjml")
            mjml_content = template.render(**context)
            
            # Convert MJML to HTML
            html_content = self._render_mjml_to_html(mjml_content)
            
            # Prepare email subject
            caller_display = event.caller_name or event.caller_number
            subject = f"📞 New intake from {caller_display}"
            
            if self.skip_email:
                logger.info(f"SKIP_EMAIL=true - would send email to {admin_emails} with subject: {subject}")
                return {
                    "success": True,
                    "skipped": True,
                    "recipients": admin_emails,
                    "subject": subject,
                    "tenant_id": tenant_id,
                    "call_id": str(event.call_id)
                }
            
            # Send email via Resend
            email_params = {
                "from": f"AiLex Voice <{self.email_from}>",
                "to": admin_emails,
                "subject": subject,
                "html": html_content
            }
            
            response = resend.Emails.send(email_params)
            
            logger.info(
                f"Intake email sent successfully for call {event.call_id} to {len(admin_emails)} recipients. "
                f"Email ID: {response.get('id')}"
            )
            
            return {
                "success": True,
                "email_id": response.get("id"),
                "recipients": admin_emails,
                "subject": subject,
                "tenant_id": tenant_id,
                "call_id": str(event.call_id)
            }
            
        except Exception as e:
            logger.error(f"Failed to send intake email for call {event.call_id}: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "tenant_id": tenant_id,
                "call_id": str(event.call_id)
            }
    
    async def close(self) -> None:
        """Close HTTP client connections."""
        if self.http_client:
            await self.http_client.aclose()
            self.http_client = None


# Global service instance
_email_fallback_service: Optional[EmailFallbackService] = None


def get_email_fallback_service() -> EmailFallbackService:
    """Get the global email fallback service instance."""
    global _email_fallback_service
    if _email_fallback_service is None:
        _email_fallback_service = EmailFallbackService()
    return _email_fallback_service
