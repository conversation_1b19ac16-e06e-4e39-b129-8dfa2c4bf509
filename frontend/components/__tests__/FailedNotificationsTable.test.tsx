import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import FailedNotificationsTable from '../FailedNotificationsTable';
import { useFailedNotifications, useResendNotification } from '../../lib/api/notifications';
import toast from 'react-hot-toast';

// Mock the API hooks
jest.mock('../../lib/api/notifications', () => ({
  useFailedNotifications: jest.fn(),
  useResendNotification: jest.fn(),
}));

// Mock toast
jest.mock('react-hot-toast', () => ({
  __esModule: true,
  default: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock child components
jest.mock('../DateRangePicker', () => {
  return function MockDateRangePicker({ onChange, ...props }: any) {
    return (
      <div data-testid="date-range-picker" onClick={() => onChange({ from: new Date(), to: new Date() })}>
        DateRangePicker Mock
      </div>
    );
  };
});

jest.mock('../TenantSelector', () => {
  return function MockTenantSelector({ onChange, ...props }: any) {
    return (
      <div data-testid="tenant-selector" onClick={() => onChange('tenant-1')}>
        TenantSelector Mock
      </div>
    );
  };
});

const mockUseFailedNotifications = useFailedNotifications as jest.MockedFunction<typeof useFailedNotifications>;
const mockUseResendNotification = useResendNotification as jest.MockedFunction<typeof useResendNotification>;

const mockNotifications = [
  {
    id: 'notif-1',
    time: '2024-01-01T10:00:00Z',
    channel: 'email' as const,
    provider: 'Resend',
    messageId: 'msg-123',
    failureReason: 'Invalid email address',
    tenantId: 'tenant-1',
    tenantName: 'Tenant One',
    retryCount: 1,
    createdAt: '2024-01-01T10:00:00Z',
    updatedAt: '2024-01-01T10:00:00Z',
  },
  {
    id: 'notif-2',
    time: '2024-01-01T11:00:00Z',
    channel: 'sms' as const,
    provider: 'Telnyx',
    messageId: 'msg-456',
    failureReason: 'Phone number not reachable',
    tenantId: 'tenant-2',
    tenantName: 'Tenant Two',
    retryCount: 2,
    createdAt: '2024-01-01T11:00:00Z',
    updatedAt: '2024-01-01T11:00:00Z',
  },
];

const mockPagination = {
  page: 1,
  limit: 50,
  total: 2,
  totalPages: 1,
};

describe('FailedNotificationsTable', () => {
  const mockResendMutation = {
    mutateAsync: jest.fn(),
    isPending: false,
    isError: false,
    error: null,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUseFailedNotifications.mockReturnValue({
      data: {
        data: mockNotifications,
        pagination: mockPagination,
      },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    } as any);

    mockUseResendNotification.mockReturnValue(mockResendMutation as any);
  });

  it('renders the table with notifications', () => {
    render(<FailedNotificationsTable />);

    expect(screen.getByText('Time')).toBeInTheDocument();
    expect(screen.getByText('Channel')).toBeInTheDocument();
    expect(screen.getByText('Provider')).toBeInTheDocument();
    expect(screen.getByText('Message ID')).toBeInTheDocument();
    expect(screen.getByText('Failure Reason')).toBeInTheDocument();
    expect(screen.getByText('Actions')).toBeInTheDocument();

    expect(screen.getAllByTestId('notification-row')).toHaveLength(2);
  });

  it('displays notification data correctly', () => {
    render(<FailedNotificationsTable />);

    expect(screen.getByText('Resend')).toBeInTheDocument();
    expect(screen.getByText('Telnyx')).toBeInTheDocument();
    expect(screen.getByText('msg-123')).toBeInTheDocument();
    expect(screen.getByText('msg-456')).toBeInTheDocument();
    expect(screen.getByText('Invalid email address')).toBeInTheDocument();
    expect(screen.getByText('Phone number not reachable')).toBeInTheDocument();
  });

  it('shows channel badges with correct styling', () => {
    render(<FailedNotificationsTable />);

    const emailBadge = screen.getByText('EMAIL');
    const smsBadge = screen.getByText('SMS');

    expect(emailBadge).toHaveClass('bg-blue-100', 'text-blue-800');
    expect(smsBadge).toHaveClass('bg-green-100', 'text-green-800');
  });

  it('renders filter components', () => {
    render(<FailedNotificationsTable />);

    expect(screen.getByTestId('date-range-picker')).toBeInTheDocument();
    expect(screen.getByTestId('tenant-selector')).toBeInTheDocument();
    expect(screen.getByTestId('channel-filter')).toBeInTheDocument();
    expect(screen.getByTestId('provider-filter')).toBeInTheDocument();
  });

  it('handles channel filter change', async () => {
    const user = userEvent.setup();
    
    render(<FailedNotificationsTable />);

    const channelFilter = screen.getByTestId('channel-filter');
    await user.selectOptions(channelFilter, 'email');

    expect(channelFilter).toHaveValue('email');
  });

  it('handles provider filter change', async () => {
    const user = userEvent.setup();
    
    render(<FailedNotificationsTable />);

    const providerFilter = screen.getByTestId('provider-filter');
    await user.type(providerFilter, 'Resend');

    expect(providerFilter).toHaveValue('Resend');
  });

  it('shows loading state', () => {
    mockUseFailedNotifications.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
      refetch: jest.fn(),
    } as any);

    render(<FailedNotificationsTable />);

    expect(screen.getByText('Loading failed notifications...')).toBeInTheDocument();
  });

  it('shows error state with retry button', async () => {
    const user = userEvent.setup();
    const mockRefetch = jest.fn();
    
    mockUseFailedNotifications.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: new Error('API Error'),
      refetch: mockRefetch,
    } as any);

    render(<FailedNotificationsTable />);

    expect(screen.getByText(/Error loading failed notifications/)).toBeInTheDocument();
    
    const retryButton = screen.getByText('Retry');
    await user.click(retryButton);

    expect(mockRefetch).toHaveBeenCalled();
  });

  it('shows empty state when no notifications', () => {
    mockUseFailedNotifications.mockReturnValue({
      data: {
        data: [],
        pagination: { ...mockPagination, total: 0 },
      },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    } as any);

    render(<FailedNotificationsTable />);

    expect(screen.getByText('No failed notifications found')).toBeInTheDocument();
  });

  it('handles resend notification successfully', async () => {
    const user = userEvent.setup();
    
    mockResendMutation.mutateAsync.mockResolvedValue({
      success: true,
      messageId: 'new-msg-123',
    });

    render(<FailedNotificationsTable />);

    const resendButton = screen.getByTestId('resend-notif-1');
    await user.click(resendButton);

    await waitFor(() => {
      expect(mockResendMutation.mutateAsync).toHaveBeenCalledWith({
        notificationId: 'notif-1',
      });
    });

    expect(toast.success).toHaveBeenCalledWith('Notification resent successfully');
  });

  it('handles resend notification failure', async () => {
    const user = userEvent.setup();
    
    mockResendMutation.mutateAsync.mockResolvedValue({
      success: false,
      error: 'Failed to send',
    });

    render(<FailedNotificationsTable />);

    const resendButton = screen.getByTestId('resend-notif-1');
    await user.click(resendButton);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Failed to send');
    });
  });

  it('handles resend notification exception', async () => {
    const user = userEvent.setup();
    
    mockResendMutation.mutateAsync.mockRejectedValue(new Error('Network error'));

    render(<FailedNotificationsTable />);

    const resendButton = screen.getByTestId('resend-notif-1');
    await user.click(resendButton);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Failed to resend notification');
    });
  });

  it('shows pagination when multiple pages', () => {
    mockUseFailedNotifications.mockReturnValue({
      data: {
        data: mockNotifications,
        pagination: { ...mockPagination, totalPages: 3 },
      },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    } as any);

    render(<FailedNotificationsTable />);

    expect(screen.getByTestId('prev-page')).toBeInTheDocument();
    expect(screen.getByTestId('next-page')).toBeInTheDocument();
    expect(screen.getByText('Page 1 of 3')).toBeInTheDocument();
  });

  it('handles pagination navigation', async () => {
    const user = userEvent.setup();
    
    mockUseFailedNotifications.mockReturnValue({
      data: {
        data: mockNotifications,
        pagination: { ...mockPagination, totalPages: 3 },
      },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    } as any);

    render(<FailedNotificationsTable />);

    const nextButton = screen.getByTestId('next-page');
    await user.click(nextButton);

    // The component should trigger a re-render with new page
    expect(nextButton).toBeInTheDocument();
  });

  it('disables resend button when mutation is pending', () => {
    mockUseResendNotification.mockReturnValue({
      ...mockResendMutation,
      isPending: true,
    } as any);

    render(<FailedNotificationsTable />);

    const resendButton = screen.getByTestId('resend-notif-1');
    expect(resendButton).toBeDisabled();
    expect(resendButton).toHaveTextContent('Sending...');
  });

  it('applies custom className', () => {
    const { container } = render(
      <FailedNotificationsTable className="custom-class" />
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });
});
