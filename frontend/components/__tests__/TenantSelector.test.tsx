import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import TenantSelector from '../TenantSelector';
import { useTenants } from '../../lib/api/notifications';

// Mock the API hook
jest.mock('../../lib/api/notifications', () => ({
  useTenants: jest.fn(),
}));

const mockUseTenants = useTenants as jest.MockedFunction<typeof useTenants>;

const mockTenants = {
  data: [
    { id: 'tenant-1', name: 'Tenant One', status: 'active' as const },
    { id: 'tenant-2', name: 'Tenant Two', status: 'inactive' as const },
    { id: 'tenant-3', name: 'Tenant Three', status: 'active' as const },
  ],
};

describe('TenantSelector', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with placeholder when no tenant is selected', () => {
    mockUseTenants.mockReturnValue({
      data: mockTenants,
      isLoading: false,
      error: null,
    } as any);

    render(
      <TenantSelector
        onChange={mockOnChange}
        placeholder="All tenants"
      />
    );

    expect(screen.getByTestId('tenant-selector-trigger')).toBeInTheDocument();
    expect(screen.getByText('All tenants')).toBeInTheDocument();
  });

  it('displays selected tenant name', () => {
    mockUseTenants.mockReturnValue({
      data: mockTenants,
      isLoading: false,
      error: null,
    } as any);

    render(
      <TenantSelector
        value="tenant-1"
        onChange={mockOnChange}
      />
    );

    expect(screen.getByText('Tenant One')).toBeInTheDocument();
  });

  it('shows loading state', () => {
    mockUseTenants.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
    } as any);

    render(
      <TenantSelector onChange={mockOnChange} />
    );

    expect(screen.getByText('Loading...')).toBeInTheDocument();
    expect(screen.getByTestId('tenant-selector-trigger')).toBeDisabled();
  });

  it('shows error state', () => {
    mockUseTenants.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: new Error('Failed to load'),
    } as any);

    render(
      <TenantSelector onChange={mockOnChange} />
    );

    expect(screen.getByText('Error loading tenants')).toBeInTheDocument();
  });

  it('opens dropdown and shows tenant options', async () => {
    const user = userEvent.setup();
    
    mockUseTenants.mockReturnValue({
      data: mockTenants,
      isLoading: false,
      error: null,
    } as any);

    render(
      <TenantSelector onChange={mockOnChange} />
    );

    const trigger = screen.getByTestId('tenant-selector-trigger');
    await user.click(trigger);

    expect(screen.getByTestId('tenant-option-all')).toBeInTheDocument();
    expect(screen.getByTestId('tenant-option-tenant-1')).toBeInTheDocument();
    expect(screen.getByTestId('tenant-option-tenant-2')).toBeInTheDocument();
    expect(screen.getByTestId('tenant-option-tenant-3')).toBeInTheDocument();

    expect(screen.getByText('Tenant One')).toBeInTheDocument();
    expect(screen.getByText('Tenant Two')).toBeInTheDocument();
    expect(screen.getByText('Tenant Three')).toBeInTheDocument();
  });

  it('shows tenant status badges', async () => {
    const user = userEvent.setup();
    
    mockUseTenants.mockReturnValue({
      data: mockTenants,
      isLoading: false,
      error: null,
    } as any);

    render(
      <TenantSelector onChange={mockOnChange} />
    );

    const trigger = screen.getByTestId('tenant-selector-trigger');
    await user.click(trigger);

    const activeStatuses = screen.getAllByText('active');
    const inactiveStatuses = screen.getAllByText('inactive');
    
    expect(activeStatuses).toHaveLength(2); // tenant-1 and tenant-3
    expect(inactiveStatuses).toHaveLength(1); // tenant-2
  });

  it('calls onChange when tenant is selected', async () => {
    const user = userEvent.setup();
    
    mockUseTenants.mockReturnValue({
      data: mockTenants,
      isLoading: false,
      error: null,
    } as any);

    render(
      <TenantSelector onChange={mockOnChange} />
    );

    const trigger = screen.getByTestId('tenant-selector-trigger');
    await user.click(trigger);

    const tenantOption = screen.getByTestId('tenant-option-tenant-1');
    await user.click(tenantOption);

    expect(mockOnChange).toHaveBeenCalledWith('tenant-1');
  });

  it('calls onChange with undefined when "All tenants" is selected', async () => {
    const user = userEvent.setup();
    
    mockUseTenants.mockReturnValue({
      data: mockTenants,
      isLoading: false,
      error: null,
    } as any);

    render(
      <TenantSelector onChange={mockOnChange} />
    );

    const trigger = screen.getByTestId('tenant-selector-trigger');
    await user.click(trigger);

    const allOption = screen.getByTestId('tenant-option-all');
    await user.click(allOption);

    expect(mockOnChange).toHaveBeenCalledWith(undefined);
  });

  it('closes dropdown when overlay is clicked', async () => {
    const user = userEvent.setup();
    
    mockUseTenants.mockReturnValue({
      data: mockTenants,
      isLoading: false,
      error: null,
    } as any);

    render(
      <TenantSelector onChange={mockOnChange} />
    );

    const trigger = screen.getByTestId('tenant-selector-trigger');
    await user.click(trigger);

    expect(screen.getByTestId('tenant-option-all')).toBeInTheDocument();

    const overlay = screen.getByTestId('tenant-selector-overlay');
    await user.click(overlay);

    await waitFor(() => {
      expect(screen.queryByTestId('tenant-option-all')).not.toBeInTheDocument();
    });
  });

  it('applies custom className', () => {
    mockUseTenants.mockReturnValue({
      data: mockTenants,
      isLoading: false,
      error: null,
    } as any);

    render(
      <TenantSelector
        onChange={mockOnChange}
        className="custom-class"
      />
    );

    const container = screen.getByTestId('tenant-selector-trigger').parentElement;
    expect(container).toHaveClass('custom-class');
  });

  it('highlights selected tenant in dropdown', async () => {
    const user = userEvent.setup();
    
    mockUseTenants.mockReturnValue({
      data: mockTenants,
      isLoading: false,
      error: null,
    } as any);

    render(
      <TenantSelector
        value="tenant-2"
        onChange={mockOnChange}
      />
    );

    const trigger = screen.getByTestId('tenant-selector-trigger');
    await user.click(trigger);

    const selectedOption = screen.getByTestId('tenant-option-tenant-2');
    expect(selectedOption).toHaveClass('bg-blue-50', 'text-blue-700');
  });
});
