import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import DateRangePicker from '../DateRangePicker';
import { DateRange } from 'react-day-picker';

// Mock date-fns functions
jest.mock('date-fns', () => ({
  format: jest.fn((date: Date, formatStr: string) => {
    if (formatStr.includes('MMM')) {
      return 'Jan 01, 2024';
    }
    return '2024-01-01T00:00:00Z';
  }),
  subDays: jest.fn((date: Date, days: number) => {
    const result = new Date(date);
    result.setDate(result.getDate() - days);
    return result;
  }),
  startOfDay: jest.fn((date: Date) => {
    const result = new Date(date);
    result.setHours(0, 0, 0, 0);
    return result;
  }),
  endOfDay: jest.fn((date: Date) => {
    const result = new Date(date);
    result.setHours(23, 59, 59, 999);
    return result;
  }),
}));

describe('DateRangePicker', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with placeholder text', () => {
    render(
      <DateRangePicker
        onChange={mockOnChange}
        placeholder="Select date range"
      />
    );

    expect(screen.getByTestId('date-range-trigger')).toBeInTheDocument();
    expect(screen.getByText('Select date range')).toBeInTheDocument();
  });

  it('displays formatted date range when value is provided', () => {
    const dateRange: DateRange = {
      from: new Date('2024-01-01'),
      to: new Date('2024-01-07'),
    };

    render(
      <DateRangePicker
        value={dateRange}
        onChange={mockOnChange}
      />
    );

    expect(screen.getByText('Jan 01, 2024 - Jan 01, 2024')).toBeInTheDocument();
  });

  it('opens dropdown when trigger is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <DateRangePicker onChange={mockOnChange} />
    );

    const trigger = screen.getByTestId('date-range-trigger');
    await user.click(trigger);

    expect(screen.getByTestId('day-picker')).toBeInTheDocument();
    expect(screen.getByTestId('preset-today')).toBeInTheDocument();
    expect(screen.getByTestId('preset-yesterday')).toBeInTheDocument();
    expect(screen.getByTestId('preset-last-7-days')).toBeInTheDocument();
    expect(screen.getByTestId('preset-last-30-days')).toBeInTheDocument();
  });

  it('calls onChange when preset is selected', async () => {
    const user = userEvent.setup();
    
    render(
      <DateRangePicker onChange={mockOnChange} />
    );

    const trigger = screen.getByTestId('date-range-trigger');
    await user.click(trigger);

    const todayPreset = screen.getByTestId('preset-today');
    await user.click(todayPreset);

    expect(mockOnChange).toHaveBeenCalled();
  });

  it('calls onChange when clear is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <DateRangePicker onChange={mockOnChange} />
    );

    const trigger = screen.getByTestId('date-range-trigger');
    await user.click(trigger);

    const clearButton = screen.getByTestId('clear-range');
    await user.click(clearButton);

    expect(mockOnChange).toHaveBeenCalledWith(undefined);
  });

  it('closes dropdown when overlay is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <DateRangePicker onChange={mockOnChange} />
    );

    const trigger = screen.getByTestId('date-range-trigger');
    await user.click(trigger);

    expect(screen.getByTestId('day-picker')).toBeInTheDocument();

    const overlay = screen.getByTestId('date-picker-overlay');
    await user.click(overlay);

    expect(screen.queryByTestId('day-picker')).not.toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(
      <DateRangePicker
        onChange={mockOnChange}
        className="custom-class"
      />
    );

    const container = screen.getByTestId('date-range-trigger').parentElement;
    expect(container).toHaveClass('custom-class');
  });

  it('handles DayPicker selection', async () => {
    const user = userEvent.setup();
    
    render(
      <DateRangePicker onChange={mockOnChange} />
    );

    const trigger = screen.getByTestId('date-range-trigger');
    await user.click(trigger);

    const dayPicker = screen.getByTestId('day-picker');
    await user.click(dayPicker);

    expect(mockOnChange).toHaveBeenCalled();
  });
});
