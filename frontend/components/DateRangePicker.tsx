import React, { useState } from 'react';
import { DayPicker, DateRange } from 'react-day-picker';
import { format, subDays, startOfDay, endOfDay } from 'date-fns';

export interface DateRangePickerProps {
  value?: DateRange;
  onChange: (range: DateRange | undefined) => void;
  placeholder?: string;
  className?: string;
}

export const DateRangePicker: React.FC<DateRangePickerProps> = ({
  value,
  onChange,
  placeholder = "Select date range",
  className = "",
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const formatRange = (range: DateRange | undefined): string => {
    if (!range?.from) return placeholder;
    if (!range.to) return format(range.from, 'MMM dd, yyyy');
    return `${format(range.from, 'MMM dd, yyyy')} - ${format(range.to, 'MMM dd, yyyy')}`;
  };

  const handleSelect = (range: DateRange | undefined) => {
    if (range?.from && range?.to) {
      // Ensure we capture the full day range
      const adjustedRange = {
        from: startOfDay(range.from),
        to: endOfDay(range.to),
      };
      onChange(adjustedRange);
      setIsOpen(false);
    } else {
      onChange(range);
    }
  };

  const presetRanges = [
    {
      label: 'Today',
      range: { from: startOfDay(new Date()), to: endOfDay(new Date()) },
    },
    {
      label: 'Yesterday',
      range: { 
        from: startOfDay(subDays(new Date(), 1)), 
        to: endOfDay(subDays(new Date(), 1)) 
      },
    },
    {
      label: 'Last 7 days',
      range: { 
        from: startOfDay(subDays(new Date(), 6)), 
        to: endOfDay(new Date()) 
      },
    },
    {
      label: 'Last 30 days',
      range: { 
        from: startOfDay(subDays(new Date(), 29)), 
        to: endOfDay(new Date()) 
      },
    },
  ];

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        data-testid="date-range-trigger"
      >
        <span className={value?.from ? 'text-gray-900' : 'text-gray-500'}>
          {formatRange(value)}
        </span>
        <svg
          className="w-4 h-4 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute z-50 mt-1 bg-white border border-gray-200 rounded-md shadow-lg">
          <div className="flex">
            <div className="p-3 border-r border-gray-200">
              <div className="space-y-1">
                {presetRanges.map((preset) => (
                  <button
                    key={preset.label}
                    type="button"
                    onClick={() => handleSelect(preset.range)}
                    className="block w-full px-3 py-2 text-sm text-left text-gray-700 hover:bg-gray-100 rounded"
                    data-testid={`preset-${preset.label.toLowerCase().replace(/\s+/g, '-')}`}
                  >
                    {preset.label}
                  </button>
                ))}
                <button
                  type="button"
                  onClick={() => handleSelect(undefined)}
                  className="block w-full px-3 py-2 text-sm text-left text-gray-700 hover:bg-gray-100 rounded"
                  data-testid="clear-range"
                >
                  Clear
                </button>
              </div>
            </div>
            <div className="p-3">
              <DayPicker
                mode="range"
                selected={value}
                onSelect={handleSelect}
                numberOfMonths={2}
                data-testid="day-picker"
              />
            </div>
          </div>
        </div>
      )}

      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
          data-testid="date-picker-overlay"
        />
      )}
    </div>
  );
};

export default DateRangePicker;
