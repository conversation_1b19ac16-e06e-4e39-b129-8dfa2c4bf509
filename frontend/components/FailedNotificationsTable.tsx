import React, { useState } from 'react';
import { DateRange } from 'react-day-picker';
import { format } from 'date-fns';
import toast from 'react-hot-toast';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '../lib/shadcn-ui';
import { useFailedNotifications, useResendNotification } from '../lib/api/notifications';
import { FailedNotificationsFilters } from '../lib/types/notifications';
import DateRangePicker from './DateRangePicker';
import TenantSelector from './TenantSelector';

export interface FailedNotificationsTableProps {
  className?: string;
}

export const FailedNotificationsTable: React.FC<FailedNotificationsTableProps> = ({
  className = "",
}) => {
  const [filters, setFilters] = useState<FailedNotificationsFilters>({});
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 50;

  // Prepare filters with date range
  const queryFilters: FailedNotificationsFilters = {
    ...filters,
    from: dateRange?.from ? format(dateRange.from, 'yyyy-MM-dd\'T\'HH:mm:ss\'Z\'') : undefined,
    to: dateRange?.to ? format(dateRange.to, 'yyyy-MM-dd\'T\'HH:mm:ss\'Z\'') : undefined,
  };

  const {
    data: notificationsResponse,
    isLoading,
    error,
    refetch,
  } = useFailedNotifications(queryFilters, currentPage, pageSize);

  const resendMutation = useResendNotification();

  const notifications = notificationsResponse?.data || [];
  const pagination = notificationsResponse?.pagination;

  const handleFilterChange = (key: keyof FailedNotificationsFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);
    setCurrentPage(1); // Reset to first page when date range changes
  };

  const handleResend = async (notificationId: string) => {
    try {
      const result = await resendMutation.mutateAsync({ notificationId });
      if (result.success) {
        toast.success('Notification resent successfully');
      } else {
        toast.error(result.error || 'Failed to resend notification');
      }
    } catch (error) {
      toast.error('Failed to resend notification');
    }
  };

  const formatTime = (timeString: string) => {
    try {
      return format(new Date(timeString), 'MMM dd, yyyy HH:mm');
    } catch {
      return timeString;
    }
  };

  const getChannelBadge = (channel: string) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full";
    switch (channel) {
      case 'email':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'sms':
        return `${baseClasses} bg-green-100 text-green-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  if (error) {
    return (
      <div className={`${className}`}>
        <div className="p-4 text-red-600 bg-red-50 border border-red-200 rounded-md">
          <p>Error loading failed notifications: {error.message}</p>
          <button
            onClick={() => refetch()}
            className="mt-2 px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Filters */}
      <div className="flex flex-wrap gap-4 p-4 bg-gray-50 rounded-lg">
        <div className="flex-1 min-w-64">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Date Range
          </label>
          <DateRangePicker
            value={dateRange}
            onChange={handleDateRangeChange}
            placeholder="Select date range"
            data-testid="date-range-filter"
          />
        </div>
        
        <div className="flex-1 min-w-48">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Tenant
          </label>
          <TenantSelector
            value={filters.tenantId}
            onChange={(tenantId) => handleFilterChange('tenantId', tenantId)}
            placeholder="All tenants"
            data-testid="tenant-filter"
          />
        </div>

        <div className="flex-1 min-w-32">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Channel
          </label>
          <select
            value={filters.channel || ''}
            onChange={(e) => handleFilterChange('channel', e.target.value || undefined)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            data-testid="channel-filter"
          >
            <option value="">All channels</option>
            <option value="email">Email</option>
            <option value="sms">SMS</option>
          </select>
        </div>

        <div className="flex-1 min-w-32">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Provider
          </label>
          <input
            type="text"
            value={filters.provider || ''}
            onChange={(e) => handleFilterChange('provider', e.target.value || undefined)}
            placeholder="All providers"
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            data-testid="provider-filter"
          />
        </div>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <div className="text-gray-500">Loading failed notifications...</div>
        </div>
      )}

      {/* Table */}
      {!isLoading && (
        <>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Time</TableHead>
                  <TableHead>Channel</TableHead>
                  <TableHead>Provider</TableHead>
                  <TableHead>Message ID</TableHead>
                  <TableHead>Failure Reason</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {notifications.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                      No failed notifications found
                    </TableCell>
                  </TableRow>
                ) : (
                  notifications.map((notification) => (
                    <TableRow key={notification.id} data-testid="notification-row">
                      <TableCell>{formatTime(notification.time)}</TableCell>
                      <TableCell>
                        <span className={getChannelBadge(notification.channel)}>
                          {notification.channel.toUpperCase()}
                        </span>
                      </TableCell>
                      <TableCell>{notification.provider}</TableCell>
                      <TableCell className="font-mono text-sm">
                        {notification.messageId}
                      </TableCell>
                      <TableCell className="max-w-xs truncate" title={notification.failureReason}>
                        {notification.failureReason}
                      </TableCell>
                      <TableCell>
                        <button
                          onClick={() => handleResend(notification.id)}
                          disabled={resendMutation.isPending}
                          className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                          data-testid={`resend-${notification.id}`}
                        >
                          {resendMutation.isPending ? 'Sending...' : 'Re-send'}
                        </button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200">
              <div className="flex items-center text-sm text-gray-700">
                Showing {((currentPage - 1) * pageSize) + 1} to{' '}
                {Math.min(currentPage * pageSize, pagination.total)} of{' '}
                {pagination.total} results
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  data-testid="prev-page"
                >
                  Previous
                </button>
                <span className="text-sm text-gray-700">
                  Page {currentPage} of {pagination.totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(pagination.totalPages, prev + 1))}
                  disabled={currentPage === pagination.totalPages}
                  className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  data-testid="next-page"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default FailedNotificationsTable;
