import React, { useState } from 'react';
import { useTenants } from '../lib/api/notifications';
import { Tenant } from '../lib/types/notifications';

export interface TenantSelectorProps {
  value?: string;
  onChange: (tenantId: string | undefined) => void;
  placeholder?: string;
  className?: string;
}

export const TenantSelector: React.FC<TenantSelectorProps> = ({
  value,
  onChange,
  placeholder = "All tenants",
  className = "",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const { data: tenantsResponse, isLoading, error } = useTenants();

  const tenants = tenantsResponse?.data || [];
  const selectedTenant = tenants.find(t => t.id === value);

  const handleSelect = (tenantId: string | undefined) => {
    onChange(tenantId);
    setIsOpen(false);
  };

  if (error) {
    return (
      <div className={`${className}`}>
        <div className="px-3 py-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
          Error loading tenants
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        disabled={isLoading}
        className="flex items-center justify-between w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        data-testid="tenant-selector-trigger"
      >
        <span className={selectedTenant ? 'text-gray-900' : 'text-gray-500'}>
          {isLoading ? 'Loading...' : (selectedTenant?.name || placeholder)}
        </span>
        <svg
          className="w-4 h-4 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {isOpen && !isLoading && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
          <button
            type="button"
            onClick={() => handleSelect(undefined)}
            className={`block w-full px-3 py-2 text-sm text-left hover:bg-gray-100 ${
              !value ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
            }`}
            data-testid="tenant-option-all"
          >
            {placeholder}
          </button>
          {tenants.map((tenant) => (
            <button
              key={tenant.id}
              type="button"
              onClick={() => handleSelect(tenant.id)}
              className={`block w-full px-3 py-2 text-sm text-left hover:bg-gray-100 ${
                value === tenant.id ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
              }`}
              data-testid={`tenant-option-${tenant.id}`}
            >
              <div className="flex items-center justify-between">
                <span>{tenant.name}</span>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  tenant.status === 'active' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {tenant.status}
                </span>
              </div>
            </button>
          ))}
        </div>
      )}

      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
          data-testid="tenant-selector-overlay"
        />
      )}
    </div>
  );
};

export default TenantSelector;
