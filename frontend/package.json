{"name": "frontend", "version": "0.1.0", "private": true, "main": "index.ts", "scripts": {"test": "jest", "lint": "eslint . --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@tanstack/react-query": "^5.0.0", "react-day-picker": "^8.10.0", "date-fns": "^3.0.0", "react-hot-toast": "^2.4.1"}, "devDependencies": {"@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/user-event": "^14.0.0", "@types/jest": "^29.0.0", "jest": "^29.0.0", "ts-jest": "^29.0.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0"}}