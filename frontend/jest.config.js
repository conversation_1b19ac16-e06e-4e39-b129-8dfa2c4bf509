module.exports = {
  ...require('../jest.base'),
  testEnvironment: 'jsdom', // Override for frontend components
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: {
        jsx: 'react',
        esModuleInterop: true,
        allowSyntheticDefaultImports: true,
      },
      isolatedModules: true,
    }],
    '^.+\\.(js|jsx)$': 'babel-jest',
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  moduleNameMapper: {
    '^../lib/shadcn-ui$': '<rootDir>/__mocks__/shadcn-ui.ts',
    '^react-day-picker$': '<rootDir>/__mocks__/react-day-picker.ts',
    '^react-hot-toast$': '<rootDir>/__mocks__/react-hot-toast.ts',
    '^date-fns$': '<rootDir>/__mocks__/date-fns.ts',
    '^@tanstack/react-query$': '<rootDir>/__mocks__/@tanstack/react-query.ts'
  }
};
