import React from 'react';

export const Table = ({ children, ...props }: any) => React.createElement('table', props, children);
export const TableHeader = ({ children, ...props }: any) => React.createElement('thead', props, children);
export const TableRow = ({ children, ...props }: any) => React.createElement('tr', props, children);
export const TableHead = ({ children, ...props }: any) => React.createElement('th', props, children);
export const TableBody = ({ children, ...props }: any) => React.createElement('tbody', props, children);
export const TableCell = ({ children, ...props }: any) => React.createElement('td', props, children);
