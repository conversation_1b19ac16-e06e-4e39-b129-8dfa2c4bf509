export const format = jest.fn((date: Date | string, _formatStr: string) => {
  if (typeof date === 'string') {
    return date;
  }
  return date.toISOString().split('T')[0];
});

export const subDays = jest.fn((date: Date, days: number) => {
  const result = new Date(date);
  result.setDate(result.getDate() - days);
  return result;
});

export const startOfDay = jest.fn((date: Date) => {
  const result = new Date(date);
  result.setHours(0, 0, 0, 0);
  return result;
});

export const endOfDay = jest.fn((date: Date) => {
  const result = new Date(date);
  result.setHours(23, 59, 59, 999);
  return result;
});
