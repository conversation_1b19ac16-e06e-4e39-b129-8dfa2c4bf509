import React from 'react';

export interface DateRange {
  from?: Date;
  to?: Date;
}

export const DayPicker = ({ onSelect, _selected, _numberOfMonths, ...props }: any) => {
  const handleClick = () => {
    if (onSelect) {
      onSelect({ from: new Date('2024-01-01'), to: new Date('2024-01-07') });
    }
  };

  // Filter out props that shouldn't be passed to DOM elements
  const { mode: _mode, ...domProps } = props;

  return React.createElement('div', {
    'data-testid': 'day-picker',
    onClick: handleClick,
    ...domProps
  }, 'DayPicker Mock');
};
