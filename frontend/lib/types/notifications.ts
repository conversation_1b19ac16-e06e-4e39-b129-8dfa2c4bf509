export interface FailedNotification {
  id: string;
  time: string;
  channel: 'email' | 'sms';
  provider: string;
  messageId: string;
  failureReason: string;
  tenantId: string;
  tenantName?: string;
  recipientEmail?: string;
  recipientPhone?: string;
  subject?: string;
  content?: string;
  retryCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface FailedNotificationsResponse {
  data: FailedNotification[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface FailedNotificationsFilters {
  tenantId?: string;
  from?: string;
  to?: string;
  channel?: 'email' | 'sms';
  provider?: string;
}

export interface ResendNotificationRequest {
  notificationId: string;
}

export interface ResendNotificationResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

export interface Tenant {
  id: string;
  name: string;
  status: 'active' | 'inactive';
}

export interface TenantsResponse {
  data: Tenant[];
}
