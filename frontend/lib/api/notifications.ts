import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from './client';
import {
  FailedNotificationsResponse,
  FailedNotificationsFilters,
  ResendNotificationRequest,
  ResendNotificationResponse,
  TenantsResponse,
} from '../types/notifications';

// Query keys
export const notificationKeys = {
  all: ['notifications'] as const,
  failed: () => [...notificationKeys.all, 'failed'] as const,
  failedList: (filters: FailedNotificationsFilters, page: number) =>
    [...notificationKeys.failed(), { filters, page }] as const,
  tenants: () => ['tenants'] as const,
};

// API functions
export const notificationsAPI = {
  getFailedNotifications: async (
    filters: FailedNotificationsFilters,
    page: number = 1,
    limit: number = 50
  ): Promise<FailedNotificationsResponse> => {
    return apiClient.get('/admin/notifications/failed', {
      ...filters,
      page,
      limit,
    });
  },

  resendNotification: async (
    request: ResendNotificationRequest
  ): Promise<ResendNotificationResponse> => {
    return apiClient.post('/admin/notifications/resend', request);
  },

  getTenants: async (): Promise<TenantsResponse> => {
    return apiClient.get('/admin/tenants');
  },
};

// React Query hooks
export const useFailedNotifications = (
  filters: FailedNotificationsFilters,
  page: number = 1,
  limit: number = 50
) => {
  return useQuery({
    queryKey: notificationKeys.failedList(filters, page),
    queryFn: () => notificationsAPI.getFailedNotifications(filters, page, limit),
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });
};

export const useResendNotification = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: notificationsAPI.resendNotification,
    onSuccess: () => {
      // Invalidate and refetch failed notifications
      queryClient.invalidateQueries({
        queryKey: notificationKeys.failed(),
      });
    },
  });
};

export const useTenants = () => {
  return useQuery({
    queryKey: notificationKeys.tenants(),
    queryFn: notificationsAPI.getTenants,
    staleTime: 300000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};
