{"compilerOptions": {"target": "ES2019", "module": "es2015", "moduleResolution": "bundler", "jsx": "react", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "resolveJsonModule": true, "declaration": false, "noEmit": true, "typeRoots": ["./types", "./node_modules/@types"]}, "include": ["components/**/*", "lib/**/*", "types/**/*"], "exclude": ["node_modules", "dist", "build", "**/*.test.*", "**/__tests__/**/*", "__mocks__/**/*"]}