name: Deploy voice-svc to Fly.io

on:
  push:
    branches: [main]
    paths:
      - 'apps/voice-svc/**'
      - 'packages/**'
      - 'fly.toml'
      - '.github/workflows/fly-deploy.yml'
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if no changes detected'
        required: false
        default: false
        type: boolean

env:
  FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

jobs:
  deploy:
    name: Deploy to Fly.io
    runs-on: ubuntu-latest
    environment: production
    
    steps:
      - name: 🛎 Checkout
        uses: actions/checkout@v4

      - name: 🔍 Check for required secrets
        run: |
          if [ -z "${{ secrets.FLY_API_TOKEN }}" ]; then
            echo "::error::FLY_API_TOKEN secret is not configured. Please add it in repository settings."
            exit 1
          fi

      - name: 🏗 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🐳 Log in to Fly Registry
        uses: docker/login-action@v3
        with:
          registry: registry.fly.io
          username: x
          password: ${{ secrets.FLY_API_TOKEN }}

      - name: 🚀 Setup Fly CLI
        uses: superfly/flyctl-actions/setup-flyctl@v1

      - name: 🔧 Validate fly.toml
        run: |
          flyctl config validate
          echo "✅ fly.toml validation passed"

      - name: 📦 Build and Deploy
        run: |
          echo "🚀 Deploying voice-svc to Fly.io..."
          flyctl deploy --remote-only --detach --build-arg BUILDKIT_INLINE_CACHE=1
          echo "✅ Deployment initiated"

      - name: ⏳ Wait for deployment
        run: |
          echo "⏳ Waiting for deployment to complete..."
          sleep 30
          
          # Check deployment status
          flyctl status --app avr-voice-svc
          
          echo "✅ Deployment status checked"

      - name: 🏥 Post-deploy health check
        run: |
          echo "🏥 Running post-deployment health checks..."
          
          # Wait for app to be ready
          for i in {1..20}; do
            echo "Attempt $i/20: Checking /livez endpoint..."
            if curl -sf --max-time 10 https://avr-voice-svc.fly.dev/livez; then
              echo "✅ /livez endpoint is healthy"
              break
            fi
            
            if [ $i -eq 20 ]; then
              echo "❌ /livez endpoint failed after 20 attempts"
              echo "🔍 Checking app logs..."
              flyctl logs --app avr-voice-svc --lines 50
              exit 1
            fi
            
            echo "⏳ Waiting 15 seconds before retry..."
            sleep 15
          done
          
          # Additional health check
          echo "🔍 Checking /health endpoint..."
          if curl -sf --max-time 10 https://avr-voice-svc.fly.dev/health; then
            echo "✅ /health endpoint is healthy"
          else
            echo "⚠️ /health endpoint check failed (non-critical)"
          fi
          
          echo "🎉 Post-deployment health checks completed successfully!"

      - name: 📊 Deployment Summary
        run: |
          echo "📊 Deployment Summary:"
          echo "- App: avr-voice-svc"
          echo "- Region: ord (Chicago)"
          echo "- URL: https://avr-voice-svc.fly.dev"
          echo "- Health: https://avr-voice-svc.fly.dev/livez"
          echo "- Docs: https://avr-voice-svc.fly.dev/docs"
          
          flyctl info --app avr-voice-svc

      - name: 🚨 Notify on failure
        if: failure()
        run: |
          echo "🚨 Deployment failed!"
          echo "📋 Troubleshooting steps:"
          echo "1. Check app logs: flyctl logs --app avr-voice-svc"
          echo "2. Check app status: flyctl status --app avr-voice-svc"
          echo "3. Check secrets: ensure all required environment variables are set"
          echo "4. Manual rollback: flyctl releases --app avr-voice-svc"
