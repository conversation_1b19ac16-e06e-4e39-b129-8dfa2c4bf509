# Call Queue System Architecture

## Overview

The Redis-based call queue system provides load-shedding and FIFO call management for the AI Voice Receptionist. When too many calls hit a tenant simultaneously, new callers are parked in a queue with hold music and automatically processed as capacity becomes available.

## Architecture

```mermaid
graph TB
    A[Incoming Call] --> B{Check Capacity}
    B -->|Under Limit| C[Start Voice Session]
    B -->|At Capacity| D{Queue Available?}
    D -->|Yes| E[Enqueue Call]
    D -->|No| F[Play Busy & Hangup]
    E --> G[Play Hold Message]
    C --> H[Voice Pipeline]
    H --> I[Call Ends]
    I --> J[Decrement Active]
    J --> K[Spawn Next Call]
    K --> L[Queue Worker]
    L --> M[Dequeue Next]
    M --> C
```

## Components

### 1. Queue Service (`services/queue_service.py`)

Core Redis operations with sub-10ms performance:

- **FIFO Queuing**: Uses Redis Streams for ordered message processing
- **Active Counting**: Redis counters track concurrent calls per tenant
- **Capacity Management**: Enforces `MAX_ACTIVE_CALLS_PER_TENANT` limits
- **Queue Limits**: Prevents unbounded growth with `MAX_QUEUE_LENGTH_PER_TENANT`

**Key Methods:**
- `enqueue_call(tenant_id, call_id, ws_meta) -> bool`
- `dequeue_next(tenant_id) -> CallMeta | None`
- `increment_active(tenant_id) / decrement_active(tenant_id)`
- `get_queue_stats(tenant_id) -> Dict[str, Any]`

### 2. Queue Worker (`workers/queue_worker.py`)

Background processor that monitors queues and spawns calls:

- **Stream Monitoring**: Uses `BRXREAD` to monitor all tenant queues
- **Capacity Checking**: Only processes calls when `active < MAX_ACTIVE`
- **Call Injection**: Reinjets queued calls into the voice pipeline
- **Error Recovery**: Handles failures and maintains queue consistency

**Key Features:**
- Automatic tenant discovery via Redis key scanning
- Configurable polling interval and concurrency limits
- Graceful shutdown with task cleanup
- Signal handlers for production deployment

### 3. Voice Session Integration (`routes/api/voice_session.py`)

Main entry point for all voice calls with queue integration:

- **Capacity Check**: Increments active count and checks limits
- **Queue Decision**: Enqueues overflow calls or rejects if queue full
- **Hold Management**: Plays hold music for queued callers
- **Cleanup Handling**: Decrements counters and spawns next calls on end

**Call Flow:**
1. Increment active count
2. If over capacity → try to enqueue
3. If enqueue fails → play busy tone and hangup
4. If enqueue succeeds → play hold message
5. On call end → decrement active and spawn next

### 4. Admin API (`routers/queue.py`)

Management endpoints protected by partner role + voice_intake feature:

- `GET /api/v1/queue/` - Get queue statistics
- `GET /api/v1/queue/all` - Get all tenant statistics (super-admin)
- `POST /api/v1/queue/clear` - Clear tenant queue (destructive)
- `GET /api/v1/queue/health` - System health check
- `POST /api/v1/queue/worker/start|stop` - Worker control

## Redis Schema

### Stream Keys
```
voice:queue:{tenant_id}
```
- Stores queued call metadata as JSON
- FIFO ordering guaranteed by Redis Streams
- Automatic cleanup on dequeue

### Counter Keys
```
voice:active:{tenant_id}
```
- Tracks active calls per tenant
- Atomic increment/decrement operations
- 24-hour TTL to prevent memory leaks

### Message Format
```json
{
  "call_meta": "{
    \"call_id\": \"call_123\",
    \"tenant_id\": \"tenant_456\",
    \"call_control_id\": \"control_789\",
    \"telnyx_rtc_session_id\": \"session_abc\",
    \"lang\": \"en\",
    \"phone_number\": \"+**********\",
    \"caller_name\": \"John Doe\",
    \"enqueued_at\": **********.123
  }"
}
```

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `REDIS_URL` | `redis://localhost:6379/0` | Redis connection URL |
| `MAX_ACTIVE_CALLS_PER_TENANT` | `4` | Maximum concurrent calls per tenant |
| `MAX_QUEUE_LENGTH_PER_TENANT` | `10` | Maximum queued calls per tenant |
| `HOLD_MESSAGE_URL` | - | URL for hold music/message |
| `QUEUE_WORKER_POLL_INTERVAL` | `1.0` | Worker polling interval (seconds) |
| `MAX_CONCURRENT_CALL_PROCESSING` | `10` | Max concurrent call processing |

### Tuning Guidelines

**For High-Volume Tenants:**
- Increase `MAX_ACTIVE_CALLS_PER_TENANT` (4-8 recommended)
- Monitor Redis memory usage
- Consider dedicated Redis instance

**For Low-Latency Requirements:**
- Decrease `QUEUE_WORKER_POLL_INTERVAL` (0.5-1.0s)
- Use Redis with persistent connections
- Monitor P99 queue operation times

**For Memory Optimization:**
- Set appropriate queue length limits
- Monitor stream sizes with `XLEN`
- Use Redis memory policies (allkeys-lru)

## Performance Characteristics

### Latency Requirements
- **Queue Operations**: < 10ms P99
- **Call Spawning**: < 100ms from capacity available
- **Hold Music Start**: < 500ms from queue entry

### Throughput
- **Enqueue Rate**: 1000+ ops/sec per Redis instance
- **Dequeue Rate**: 500+ ops/sec (limited by call setup time)
- **Concurrent Tenants**: 100+ with proper Redis sizing

### Memory Usage
- **Per Queued Call**: ~500 bytes (metadata + Redis overhead)
- **Per Active Counter**: ~100 bytes
- **Estimated**: 1MB per 1000 queued calls

## Monitoring & Alerting

### Key Metrics

1. **Queue Depth**: `voice:queue:{tenant_id}` length
2. **Active Calls**: `voice:active:{tenant_id}` value
3. **Queue Wait Time**: Time from enqueue to dequeue
4. **Rejection Rate**: Calls rejected due to full queue
5. **Worker Health**: Queue worker running status

### Recommended Alerts

```yaml
# Queue depth alert
- alert: QueueDepthHigh
  expr: redis_stream_length{key=~"voice:queue:.*"} > 5
  for: 2m
  labels:
    severity: warning
  annotations:
    summary: "High queue depth for tenant {{ $labels.tenant_id }}"

# Queue worker down
- alert: QueueWorkerDown
  expr: queue_worker_running == 0
  for: 1m
  labels:
    severity: critical
  annotations:
    summary: "Queue worker is not running"

# High rejection rate
- alert: HighCallRejectionRate
  expr: rate(calls_rejected_total[5m]) > 0.1
  for: 2m
  labels:
    severity: warning
  annotations:
    summary: "High call rejection rate: {{ $value }} calls/sec"
```

### Dashboard Queries

```promql
# Queue depth by tenant
redis_stream_length{key=~"voice:queue:.*"}

# Active calls by tenant
redis_key_value{key=~"voice:active:.*"}

# Queue wait time P95
histogram_quantile(0.95, rate(queue_wait_time_seconds_bucket[5m]))

# Call processing rate
rate(calls_processed_total[5m])
```

## Deployment

### Docker Compose
```yaml
services:
  voice-svc:
    environment:
      - REDIS_URL=redis://redis:6379/0
      - MAX_ACTIVE_CALLS_PER_TENANT=4
      - MAX_QUEUE_LENGTH_PER_TENANT=10
    depends_on:
      - redis
  
  redis:
    image: redis:7-alpine
    command: redis-server --maxmemory 256mb --maxmemory-policy allkeys-lru
```

### Fly.io Configuration
```toml
# fly.toml
[env]
  MAX_ACTIVE_CALLS_PER_TENANT = "4"
  MAX_QUEUE_LENGTH_PER_TENANT = "10"
  QUEUE_WORKER_POLL_INTERVAL = "1.0"

# Add Redis addon
# fly redis create --name ailex-redis
# fly secrets set REDIS_URL=redis://...
```

## Testing

### Unit Tests
```bash
# Run queue service tests
pytest tests/queue/test_queue_service.py -v

# Run worker tests
pytest tests/queue/test_queue_worker.py -v

# Run API tests
pytest tests/queue/test_queue_router.py -v
```

### Integration Tests
```bash
# Requires Redis running on localhost:6379
pytest tests/queue/test_queue_integration.py -v

# Load test scenario
pytest tests/queue/test_queue_integration.py::TestQueueIntegration::test_load_scenario_6_calls_4_active_2_queued -v
```

### Performance Testing
```bash
# Benchmark queue operations
python -m pytest tests/queue/test_queue_integration.py::TestQueueIntegration::test_performance_requirements -v -s
```

## Troubleshooting

### Common Issues

1. **Queue Worker Not Processing**
   - Check worker status: `GET /api/v1/queue/health`
   - Verify Redis connectivity
   - Check worker logs for errors

2. **High Queue Depth**
   - Monitor active call limits
   - Check voice pipeline performance
   - Consider increasing capacity limits

3. **Memory Usage Growth**
   - Monitor Redis memory usage
   - Check for stuck queues
   - Verify TTL settings on counters

4. **Call Rejections**
   - Review queue length limits
   - Monitor tenant usage patterns
   - Consider dynamic scaling

### Debug Commands

```bash
# Check queue contents
redis-cli XRANGE voice:queue:tenant123 - +

# Check active counts
redis-cli GET voice:active:tenant123

# Monitor queue operations
redis-cli MONITOR | grep "voice:"

# Check worker status
curl http://localhost:8000/api/v1/queue/health
```
