# Fly.io Deployment Guide for AiLex Voice Service

This guide covers deploying the AiLex Voice Service to Fly.io for production use.

## Overview

The voice-svc is configured for zero-downtime deployment to Fly.io with:
- Automatic deployment on `main` branch pushes
- Health checks and monitoring
- Secrets management
- Rollback capabilities

## Prerequisites

1. **Fly.io Account**: Sign up at [fly.io](https://fly.io)
2. **Fly CLI**: Install locally for manual operations
3. **GitHub Secrets**: Configure required secrets in repository settings

## Initial Setup

### 1. Install Fly CLI

```bash
# macOS
brew install flyctl

# Linux/WSL
curl -L https://fly.io/install.sh | sh

# Windows
iwr https://fly.io/install.ps1 -useb | iex
```

### 2. Authenticate with Fly.io

```bash
flyctl auth login
```

### 3. Create Fly.io App (One-time setup)

```bash
# Navigate to repository root
cd /path/to/ailex-receptionist

# Create app (already configured in fly.toml)
flyctl launch --no-deploy --name avr-voice-svc

# Verify configuration
flyctl config validate
```

## Environment Configuration

### Required Secrets

Set these secrets in your Fly.io app:

```bash
# Core API Integration
flyctl secrets set CORE_SUBS_API_URL="https://your-core-api.com/api/v1/subscriptions"
flyctl secrets set CORE_INTAKE_SECRET="your-intake-webhook-secret"

# Telnyx Configuration
flyctl secrets set TELNYX_API_KEY="your-telnyx-api-key"
flyctl secrets set TELNYX_WEBHOOK_SECRET="your-telnyx-webhook-secret"

# Email Service (Resend)
flyctl secrets set RESEND_API_KEY="your-resend-api-key"
flyctl secrets set EMAIL_FROM="<EMAIL>"

# Redis (Fly Redis add-on)
flyctl secrets set REDIS_URL="redis://your-redis-instance.fly.dev:6379"

# Pipecat Integration
flyctl secrets set PIPECAT_API_KEY="your-pipecat-api-key"

# Monitoring
flyctl secrets set NEXT_PUBLIC_GRAFANA_URL="https://your-grafana-instance.com"

# Admin URL
flyctl secrets set ADMIN_URL="https://admin.yourdomain.com"
```

### Optional Configuration

```bash
# Database (defaults to SQLite if not set)
flyctl secrets set DATABASE_URL="********************************/voice_svc"

# Queue Configuration
flyctl secrets set MAX_ACTIVE_CALLS_PER_TENANT="4"
flyctl secrets set MAX_QUEUE_LENGTH_PER_TENANT="10"

# Calendar Service Integration
flyctl secrets set CALENDAR_SVC_BASE="https://your-calendar-service.com"
```

## Deployment

### Automatic Deployment

Deployments are triggered automatically when:
- Code is pushed to the `main` branch
- Changes are made to `apps/voice-svc/`, `packages/`, or deployment files

The GitHub Action will:
1. Build the Docker image
2. Deploy to Fly.io
3. Run health checks
4. Report deployment status

### Manual Deployment

```bash
# Deploy from local machine
flyctl deploy --remote-only

# Deploy with specific image
flyctl deploy --image registry.fly.io/avr-voice-svc:latest --remote-only

# Deploy and follow logs
flyctl deploy --remote-only && flyctl logs
```

## Scaling and Management

### Scaling Commands

```bash
# Scale to multiple instances
flyctl scale count 3

# Scale to specific regions
flyctl regions add ord cdg lax
flyctl scale count 2 --region ord
flyctl scale count 1 --region cdg
flyctl scale count 1 --region lax

# Scale machine resources
flyctl scale vm shared-cpu-1x --memory 1024

# Auto-scaling (based on load)
flyctl autoscale set min=1 max=5
```

### Monitoring Commands

```bash
# View app status
flyctl status

# View logs (real-time)
flyctl logs

# View logs (historical)
flyctl logs --lines 100

# View metrics
flyctl metrics

# SSH into machine
flyctl ssh console
```

## Health Checks

The service includes multiple health check endpoints:

- **`/livez`**: Liveness check for Fly.io monitoring
- **`/health`**: Detailed health status
- **`/metrics`**: Prometheus metrics

### Manual Health Check

```bash
# Check if service is live
curl https://avr-voice-svc.fly.dev/livez

# Check detailed health
curl https://avr-voice-svc.fly.dev/health

# Check metrics
curl https://avr-voice-svc.fly.dev/metrics
```

## Rollback and Recovery

### View Releases

```bash
# List recent releases
flyctl releases

# View specific release
flyctl releases show v42
```

### Rollback

```bash
# Rollback to previous release
flyctl releases rollback

# Rollback to specific version
flyctl releases rollback v41
```

### Emergency Recovery

```bash
# Restart all machines
flyctl machine restart

# Stop and start app
flyctl apps stop avr-voice-svc
flyctl apps start avr-voice-svc

# View machine status
flyctl machine list
```

## Troubleshooting

### Common Issues

1. **Deployment Fails**
   ```bash
   # Check build logs
   flyctl logs --lines 50
   
   # Validate configuration
   flyctl config validate
   
   # Check secrets
   flyctl secrets list
   ```

2. **Health Checks Fail**
   ```bash
   # Test endpoints manually
   curl -v https://avr-voice-svc.fly.dev/livez
   
   # Check application logs
   flyctl logs --lines 100
   
   # SSH into machine for debugging
   flyctl ssh console
   ```

3. **Performance Issues**
   ```bash
   # Check resource usage
   flyctl metrics
   
   # Scale up resources
   flyctl scale vm shared-cpu-2x --memory 2048
   
   # Add more instances
   flyctl scale count 3
   ```

### Debug Commands

```bash
# Run health check script inside container
flyctl ssh console -C "cd /app && python scripts/health_check.py"

# Check environment variables
flyctl ssh console -C "env | grep -E '(REDIS|TELNYX|RESEND)'"

# Test Redis connectivity
flyctl ssh console -C "redis-cli -u \$REDIS_URL ping"
```

## Security Considerations

1. **Secrets Management**: Never commit secrets to git
2. **Network Security**: Use Fly.io private networking for internal services
3. **Access Control**: Limit SSH access and use Fly.io teams for access management
4. **Monitoring**: Set up alerts for failed deployments and health checks

## Support

- **Fly.io Documentation**: https://fly.io/docs/
- **Fly.io Community**: https://community.fly.io/
- **AiLex Voice Service Issues**: Create GitHub issues in this repository
