# Email Fallback for AVR-Only Tenants - Audit Results

## Phase A - Quick Audit Results

### Existing Email Infrastructure

The AiLex monorepo has robust email infrastructure that can be leveraged for the email fallback system:

#### 1. Calendar Service Email System

**Location**: `apps/calendar-svc/app/services/email.py`

**Features**:
- **Resend Integration**: Full Resend SDK integration with API key configuration
- **Jinja2 Templates**: Template rendering system with custom filters
- **Email Service Class**: Comprehensive `EmailService` with async methods
- **Template Support**: `send_template_email()` method for HTML template rendering
- **Error Handling**: Proper logging and error handling
- **Configuration**: Environment-based settings (EMAILS_ENABLED, EMAILS_FROM_EMAIL, etc.)

**Key Methods**:
- `send_email()` - Core email sending with Resend
- `send_template_email()` - Template-based email sending
- Convenience functions for common email types (reset password, booking confirmation, etc.)

**Template System**:
- Templates stored in `templates/emails/` directory
- Jinja2 environment with autoescape
- Global variables: project_name, support_email, frontend_url
- Custom filters support

#### 2. Notifier Service Email Client

**Location**: `apps/notifier/lib/email.py`

**Features**:
- Lightweight `ResendClient` wrapper for testing
- Mock email functionality for development/testing
- Async email sending interface

#### 3. Notification Service Resend Integration

**Location**: `apps/notification-svc/app/api/webhooks.py`

**Features**:
- Resend webhook handling
- Signature verification for Resend webhooks
- Delivery status tracking
- Test coverage for webhook processing

### Feature Flag Infrastructure

**Location**: `apps/voice-svc/avr/api/middleware/subscription_check.py`

**Features**:
- **SubscriptionCheckMiddleware**: Validates tenant features from Core API
- **Feature Injection**: `request.state.features` available in all routes
- **Redis Caching**: 60-second TTL for feature lookups
- **Route Protection**: Feature-based access control
- **Error Handling**: Graceful degradation when Core API unavailable

**Current Feature Flags**:
- `voice_intake` - Voice intake functionality
- `calendar_booking` - Calendar booking features

**Usage Pattern**:
```python
# Features are injected into request.state by middleware
features = request.state.features  # List[str]
if "core_intake_sync" not in features:
    # Trigger email fallback
```

### Missing Components for Email Fallback

1. **No MJML Support**: Current templates use basic HTML, no MJML found
2. **No Email Fallback Logic**: No existing email fallback for webhook failures
3. **No Intake Email Templates**: No templates specifically for intake events
4. **No Admin Email Lookup**: No system to find tenant admin emails
5. **No Audit Logging**: No tracking of email fallback usage

### Reusable Components

#### 1. Email Service Pattern
The calendar service email system provides an excellent foundation:
- Resend integration is already proven
- Template rendering system is robust
- Error handling patterns are established
- Configuration patterns are consistent

#### 2. Feature Flag System
The subscription middleware provides exactly what we need:
- Feature flags are already injected into `request.state.features`
- Can check for `core_intake_sync` feature presence
- Caching and error handling already implemented

#### 3. Environment Configuration
Existing patterns for email configuration:
- `RESEND_API_KEY` - Already used in calendar service
- `EMAILS_FROM_EMAIL` - Sender email configuration
- `EMAILS_ENABLED` - Global email toggle

### Recommended Implementation Strategy

1. **Reuse Calendar Service Email Infrastructure**:
   - Copy/adapt the `EmailService` class to voice-svc
   - Use the same Resend integration patterns
   - Follow the same template rendering approach

2. **Extend Feature Flag Usage**:
   - Add `core_intake_sync` feature flag check
   - Use existing `request.state.features` injection

3. **Create Voice-Specific Templates**:
   - Add MJML support for better email rendering
   - Create intake-specific email templates
   - Follow existing template directory structure

4. **Add Admin Email Lookup**:
   - Query tenant admin emails from Core API
   - Cache results similar to feature flag caching

5. **Integrate with Existing Audit System**:
   - Use existing audit_log table patterns
   - Track email fallback usage for monitoring

### Environment Variables Already Available

From calendar service:
- `RESEND_API_KEY` - Resend API key
- `EMAILS_FROM_EMAIL` - Sender email address
- `EMAILS_FROM_NAME` - Sender name
- `EMAILS_ENABLED` - Global email toggle

### Dependencies Already Available

- `resend` - Resend SDK
- `jinja2` - Template rendering
- `httpx` - HTTP client for API calls
- `redis.asyncio` - Redis caching

## Conclusion

The monorepo has excellent email infrastructure that can be leveraged for the email fallback system. The main work involves:

1. Adapting the calendar service email system for voice-svc
2. Adding MJML template support
3. Creating intake-specific email templates
4. Implementing admin email lookup
5. Integrating with the existing feature flag system

No major architectural changes are needed - this is primarily about extending existing patterns to support the email fallback use case.
