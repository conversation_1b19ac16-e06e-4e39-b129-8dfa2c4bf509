# Metrics and Monitoring Guide

## Overview

The AiLex Voice Service (voice-svc) provides comprehensive Prometheus metrics for monitoring call performance, queue health, and system operations. This guide covers setup, available metrics, Grafana dashboards, and alerting.

## Quick Start

### 1. Metrics Endpoint

The service exposes Prometheus metrics at:
```
GET http://localhost:8000/metrics
```

### 2. Grafana Dashboard Import

1. Open Grafana (http://localhost:3000)
2. Navigate to **Dashboards** → **Import**
3. Upload `grafana/avr_dashboard.json`
4. Configure Prometheus datasource as `prometheus`
5. Save and view the dashboard

## Available Metrics

### Call Performance

| Metric | Type | Labels | Description |
|--------|------|--------|-------------|
| `avr_call_latency_seconds` | Histogram | `tenant_id` | Dialogue round-trip latency (speech → OpenAI response) |

**Buckets**: 0.1, 0.2, 0.5, 0.8, 1.0, 2.0, 5.0, 10.0 seconds

### Queue Metrics

| Metric | Type | Labels | Description |
|--------|------|--------|-------------|
| `avr_queue_depth` | Gauge | `tenant_id` | Current callers waiting in Redis queue |
| `avr_active_calls` | Gauge | `tenant_id` | Current calls inside Pipecat |
| `avr_calls_rejected_total` | Counter | `tenant_id`, `reason` | Calls dropped because queue full |

### System Operations

| Metric | Type | Labels | Description |
|--------|------|--------|-------------|
| `avr_call_recordings_purged_total` | Counter | `tenant_id` | 30-day auto-deletes |
| `avr_webhook_retry_total` | Counter | `tenant_id`, `webhook_type` | Webhook retry attempts |
| `avr_job_duration_seconds` | Histogram | `job` | Background job execution times |

**Job Types**: `purge_recordings`, `manual_purge_recordings`, `webhook_retry`

## Grafana Dashboard

The included dashboard (`grafana/avr_dashboard.json`) provides:

### Row 1: Overview Cards
- **Total Active Calls**: Single-stat showing system-wide active calls
- **Total Queued Calls**: Single-stat showing system-wide queue depth
- **Active Calls by Tenant**: Pie chart breakdown
- **Queue Depth by Tenant**: Pie chart breakdown

### Row 2: Performance Graphs
- **Call Latency Percentiles**: P95 and P99 latency over time
- **Rejected Calls**: 5-minute rate of call rejections by tenant and reason

### Dashboard Features
- **Auto-refresh**: 5-second intervals
- **Time range**: Last 1 hour (configurable)
- **Dark theme**: Optimized for operations centers

## Sample Queries

### PromQL Examples

```promql
# Average call latency by tenant (5m window)
rate(avr_call_latency_seconds_sum[5m]) / rate(avr_call_latency_seconds_count[5m])

# Queue utilization percentage
(avr_queue_depth / 10) * 100  # Assuming max queue length of 10

# Call rejection rate
rate(avr_calls_rejected_total[5m])

# Active calls capacity utilization
(avr_active_calls / 4) * 100  # Assuming max active calls of 4

# Job failure rate
rate(avr_job_duration_seconds_count{job="purge_recordings"}[5m])
```

### Alerting Rules

```yaml
groups:
  - name: voice-svc-alerts
    rules:
      - alert: HighCallLatency
        expr: histogram_quantile(0.99, rate(avr_call_latency_seconds_bucket[5m])) > 0.8
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High call latency detected"
          description: "P99 call latency is {{ $value }}s, above 800ms threshold"

      - alert: QueueDepthHigh
        expr: avr_queue_depth > 8
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Queue depth high for tenant {{ $labels.tenant_id }}"
          description: "Queue depth is {{ $value }}, approaching limit of 10"

      - alert: CallsRejected
        expr: increase(avr_calls_rejected_total[5m]) > 0
        for: 0m
        labels:
          severity: critical
        annotations:
          summary: "Calls being rejected for tenant {{ $labels.tenant_id }}"
          description: "{{ $value }} calls rejected in last 5 minutes due to {{ $labels.reason }}"

      - alert: PurgeJobFailed
        expr: increase(avr_job_duration_seconds_count{job="purge_recordings"}[1h]) == 0
        for: 25h  # Alert if no purge in 25 hours (should run daily)
        labels:
          severity: warning
        annotations:
          summary: "Recording purge job not running"
          description: "No purge job execution detected in the last 25 hours"
```

## Configuration

### Environment Variables

```bash
# Metrics are enabled by default, no additional config needed
# Optional: Custom metrics port (if using separate server)
METRICS_PORT=8001
```

### Docker Compose Setup

```yaml
version: '3.8'
services:
  voice-svc:
    build: .
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - redis
      - prometheus

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-storage:/var/lib/grafana

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  grafana-storage:
```

### Prometheus Configuration

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'voice-svc'
    static_configs:
      - targets: ['voice-svc:8000']
    metrics_path: '/metrics'
    scrape_interval: 5s
```

## Troubleshooting

### Common Issues

1. **Metrics endpoint returns 404**
   - Verify prometheus_client is installed: `pip list | grep prometheus`
   - Check application logs for metrics service initialization errors

2. **No data in Grafana**
   - Verify Prometheus is scraping: Check `http://localhost:9090/targets`
   - Confirm datasource configuration in Grafana
   - Check time range and refresh interval

3. **Missing tenant-specific metrics**
   - Ensure calls are being processed (check application logs)
   - Verify Redis connectivity for queue metrics
   - Check that tenant_id labels are being set correctly

### Debug Commands

```bash
# Check metrics endpoint
curl http://localhost:8000/metrics

# Verify specific metrics
curl -s http://localhost:8000/metrics | grep avr_

# Check Prometheus targets
curl http://localhost:9090/api/v1/targets

# Test PromQL query
curl 'http://localhost:9090/api/v1/query?query=avr_active_calls'
```

## Performance Impact

- **Metrics collection**: < 1ms overhead per operation
- **Memory usage**: ~10MB additional for metrics storage
- **Network**: ~5KB/scrape (15s intervals = ~20KB/min)
- **Storage**: ~1MB/day for typical workloads

## Security Considerations

- Metrics endpoint exposes operational data (no sensitive information)
- Consider restricting `/metrics` access in production
- Use Grafana authentication for dashboard access
- Prometheus should be on internal network only
