# AI Lex Receptionist - Architecture Documentation

## Overview

The AI Lex Receptionist is a multi-service architecture designed to provide 24/7 AI voice reception services for law firms. The system consists of several microservices that work together to handle voice calls, manage calendars, and provide administrative dashboards.

## Service Architecture

### Core Services

1. **Voice Service (AVR)** - `apps/voice-svc/`
   - <PERSON>les voice interactions and call processing
   - Implements subscription-based feature access control
   - Integrates with Telnyx for telephony

2. **Calendar Service** - `apps/calendar-svc/`
   - Manages calendar integrations (Google Calendar, Calendly)
   - Handles appointment booking and availability
   - Provides unified calendar provider interface

3. **Dashboard** - `apps/dashboard/`
   - Admin interface for managing tenants and settings
   - Built with Next.js and shadcn/ui

## Calendar Integration Architecture

The Calendar Service provides a unified interface for multiple calendar providers through a provider pattern architecture.

### Provider Architecture

```
Voice Agent → CalendarProvider Interface → Specific Provider (Google/Calendly/Outlook)
                      ↓
              Unified Response Format
```

### Supported Providers

#### 1. Google Calendar
- **OAuth 2.0**: Full OAuth flow with refresh token support
- **Features**: Read/write access, event creation, availability checking
- **Rate Limits**: 60 requests/minute
- **Capabilities**: Full calendar management

#### 2. Calendly
- **OAuth 2.0**: OAuth flow with organization-level access
- **Features**: Event type listing, scheduling link creation, availability
- **Rate Limits**: 30 requests/minute with exponential backoff
- **Capabilities**: Scheduling-focused (no direct event creation)
- **Caching**: 30-minute TTL for event types

#### 3. Outlook (Microsoft Graph)
- **OAuth 2.0**: Microsoft Graph API integration
- **Features**: Read/write access, Teams meeting integration
- **Rate Limits**: 60 requests/minute
- **Capabilities**: Full calendar management with meeting links

### Core Components

#### 1. Provider Interface (`packages/calendar_core/interface.py`)
```python
class CalendarProvider(ABC):
    @abstractmethod
    async def list_calendars(self) -> List[Dict[str, Any]]

    @abstractmethod
    async def create_event(self, calendar_id: str, event: Dict[str, Any]) -> Dict[str, Any]

    async def get_availability(self, calendar_ids: List[str], time_min: datetime, time_max: datetime) -> Any
```

#### 2. Provider Factory (`packages/calendar_core/__init__.py`)
```python
def get_provider(firm_id: str, provider_name: str) -> CalendarProvider:
    # Returns appropriate provider instance based on provider_name
```

#### 3. Calendly Provider (`packages/calendar_core/adapters/calendly.py`)
- **Event Types**: Cached retrieval with 30-minute TTL
- **Scheduling Links**: Dynamic link creation for event types
- **Availability**: User availability checking
- **Error Handling**: Comprehensive retry logic with exponential backoff
- **Rate Limiting**: Respects Calendly's 30 req/min limit

### API Endpoints

#### Calendly-Specific Endpoints
- `GET /api/v1/calendly/event-types` - List available event types
- `POST /api/v1/calendly/scheduling-links` - Create scheduling links
- `GET /api/v1/calendly/availability` - Check availability

#### Unified Booking Endpoints
- `POST /api/v1/availability` - Check availability across providers
- `POST /api/v1/book` - Create bookings with any provider

### Authentication & Security

#### OAuth Flow
1. **Authorization**: Redirect to provider's OAuth endpoint
2. **Callback**: Exchange authorization code for tokens
3. **Storage**: Secure token storage with encryption
4. **Refresh**: Automatic token refresh on expiration

#### Feature Gating
- All calendar endpoints require `calendar_booking` feature
- Subscription middleware validates feature access
- Graceful degradation on feature unavailability

### Data Models

#### Calendar Connection
```python
class CalendarConnection:
    id: UUID
    user_id: str
    provider: str  # 'google', 'calendly', 'outlook'
    access_token: str
    refresh_token: str
    token_expiry: datetime
    metadata: Dict[str, Any]  # Provider-specific data
```

#### Booking Response
```python
class BookingResponse:
    id: UUID
    provider: str
    external_id: str  # Provider's event/link ID
    start_at: datetime
    booked_at: datetime
    provider_event_link: str
```

### Caching Strategy

#### Event Types (Calendly)
- **Cache Key**: `calendly_event_types:{firm_id}`
- **TTL**: 30 minutes
- **Storage**: Redis with JSON serialization
- **Invalidation**: Automatic on TTL expiry

#### Availability Data
- **Strategy**: No caching (real-time data required)
- **Fallback**: Empty availability on provider errors
- **Timeout**: 30-second request timeout

### Error Handling

#### Provider-Level Errors
- **Rate Limiting**: Exponential backoff with tenacity
- **Token Expiry**: Automatic refresh with fallback
- **API Errors**: Graceful degradation with logging
- **Network Issues**: Retry with circuit breaker pattern

#### Application-Level Errors
- **Missing Connections**: Clear error messages
- **Invalid Requests**: Pydantic validation
- **Feature Access**: Subscription-based gating
- **Provider Failures**: Fallback to alternative providers

## Subscription Check Middleware

The Voice Service implements a sophisticated subscription check middleware that ensures tenants only access features they're subscribed to.

### Architecture

```
Request → X-Tenant-ID Header → Subscription Middleware → Feature Check → Route Handler
                                      ↓
                               Redis Cache ← → Core API
```

### Components

#### 1. Middleware Flow
- **Header Extraction**: Extracts tenant ID from `X-Tenant-ID` header
- **Feature Lookup**: Checks Redis cache first, then Core API if cache miss
- **Authorization**: Validates tenant has required feature for the requested route
- **State Injection**: Injects features into `request.state` for downstream use

#### 2. Caching Strategy
- **Cache Key**: `tenant_features:{tenant_id}`
- **TTL**: 60 seconds (configurable)
- **Fallback**: Graceful degradation on Redis failures
- **Cache Data**: JSON object containing feature list

#### 3. Route-Feature Mapping
```python
ROUTE_FEATURES = {
    "/voice/intake": "voice_intake",
    "/calendar/availability": "calendar_booking", 
    "/calendar/book": "calendar_booking",
}
```

#### 4. Error Handling
- **400 Bad Request**: Missing or invalid `X-Tenant-ID` header
- **403 Forbidden**: Tenant lacks required feature
- **503 Service Unavailable**: Core API unreachable (fail-closed)

#### 5. Retry Logic
- **Exponential Backoff**: 3 retries with exponential backoff (1s, 2s, 4s)
- **Retry Conditions**: Network errors and HTTP 5xx responses
- **Fail-Closed**: Returns 503 if all retries exhausted

### Configuration

#### Environment Variables
- `CORE_SUBS_API_URL`: Core subscription API base URL
- `REDIS_URL`: Redis connection string for caching

#### Middleware Registration
```python
app.add_middleware(
    SubscriptionCheckMiddleware,
    core_api_url=os.getenv("CORE_SUBS_API_URL"),
    redis_url=os.getenv("REDIS_URL"),
    cache_ttl=60
)
```

### Bypass Routes
The following routes bypass subscription checks:
- `/health` - Health check endpoint
- `/` - Root endpoint
- `/docs` - API documentation
- `/redoc` - Alternative API documentation
- `/openapi.json` - OpenAPI specification

### Dependencies

#### Core Dependencies
- **FastAPI**: Web framework and middleware support
- **httpx**: Async HTTP client for Core API calls
- **redis**: Async Redis client for caching
- **tenacity**: Retry logic with exponential backoff

#### Testing Dependencies
- **pytest**: Test framework
- **pytest-asyncio**: Async test support
- **respx**: HTTP mocking for httpx

### Security Considerations

1. **Fail-Closed**: System denies access when Core API is unavailable
2. **Header Validation**: Strict validation of tenant ID format
3. **Cache Isolation**: Tenant-specific cache keys prevent data leakage
4. **Timeout Protection**: HTTP timeouts prevent hanging requests
5. **Error Logging**: Comprehensive logging for security monitoring

### Performance Characteristics

1. **Cache Hit**: ~1-2ms response time
2. **Cache Miss**: ~50-100ms (network + API processing)
3. **Cache TTL**: 60 seconds balances freshness vs performance
4. **Concurrent Requests**: Async design supports high concurrency

### Monitoring and Observability

#### Logging
- **Debug**: Cache hits/misses, feature checks
- **Info**: Middleware enable/disable status
- **Warning**: Cache failures, tenant not found
- **Error**: API failures, network errors

#### Metrics (Recommended)
- Request count by tenant and route
- Cache hit/miss ratios
- API response times
- Error rates by type

### Testing Strategy

#### Unit Tests
- Middleware initialization and configuration
- Cache operations (get, set, miss scenarios)
- Core API interactions with mocking
- Error handling and retry logic

#### Integration Tests
- End-to-end request flows
- Middleware registration and execution
- Feature authorization scenarios
- Cache integration with Redis

#### Coverage Requirements
- Minimum 90% code coverage for new middleware code
- All error paths must be tested
- Cache scenarios (hit, miss, failure) covered

### Deployment Considerations

1. **Environment Setup**: Ensure `CORE_SUBS_API_URL` and `REDIS_URL` are configured
2. **Redis Availability**: Redis should be highly available for optimal performance
3. **Core API Dependency**: Core API must be accessible from voice service
4. **Graceful Degradation**: Service continues with warnings if middleware disabled

### Future Enhancements

1. **Circuit Breaker**: Add circuit breaker pattern for Core API calls
2. **Metrics Collection**: Implement Prometheus metrics
3. **Feature Flags**: Dynamic feature flag support
4. **Audit Logging**: Enhanced audit trail for compliance
5. **Rate Limiting**: Per-tenant rate limiting based on subscription tier
