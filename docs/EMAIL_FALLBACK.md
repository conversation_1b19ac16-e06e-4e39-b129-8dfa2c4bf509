# Email Fallback for AVR-Only Tenants

## Overview

The email fallback system provides intake notification delivery via email for tenants who do not have the `core_intake_sync` feature flag enabled. This ensures that all tenants receive intake notifications regardless of their subscription level.

## Architecture

```mermaid
graph TD
    A[Call Ends] --> B[_send_intake_event_for_call_end]
    B --> C[Check core_intake_sync Feature]
    C --> D{Has core_intake_sync?}
    D -->|Yes| E[Send Webhook to Core]
    D -->|No| F[Send Email Fallback]
    F --> G[Get Admin Emails from Core API]
    G --> H[Render MJML Template]
    H --> I[Send via Resend]
    E --> J[Log Success/Retry]
    I --> K[Log Email Sent]
```

## Feature Flag Decision Logic

The system automatically determines the delivery method based on the tenant's feature flags:

1. **Query Core API**: Fetch tenant features using the existing `SubscriptionCheckMiddleware` logic
2. **Check for `core_intake_sync`**: If present, use webhook delivery (existing S-1 behavior)
3. **Email Fallback**: If absent, use email delivery to tenant admins

## Email Service Components

### 1. EmailFallbackService (`services/email_fallback.py`)

Core service responsible for:
- Admin email lookup from Core API
- MJML template rendering to HTML
- Email delivery via Resend
- Error handling and logging

**Key Methods**:
- `send_intake_email(event: IntakeEvent, tenant_id: str)` - Main entry point
- `_get_tenant_admin_emails(tenant_id: str)` - Fetch admin emails
- `_render_mjml_to_html(mjml_content: str)` - Convert MJML to HTML

### 2. MJML Email Template (`templates/intake_email.mjml`)

Professional email template featuring:
- **Call Summary**: Caller info, phone number, call ID, intent, duration
- **Booking Details**: Date/time, provider, calendar links (if booking made)
- **Conversation Transcript**: Truncated transcript with full view link
- **Call Recording**: Download link with expiration notice (if available)
- **Action Buttons**: Admin panel, callback, booking management
- **Footer**: Tenant info and feature upgrade notice

### 3. Integration Points

**Voice Session Handler** (`routes/api/voice_session.py`):
- `_send_intake_event_for_call_end()` - Modified to include feature check
- `_check_tenant_has_core_sync()` - Feature flag validation

## Configuration

### Environment Variables

```bash
# Email Service Configuration
RESEND_API_KEY=re_xxxxxxxxxxxx          # Resend API key for email delivery
EMAIL_FROM=<EMAIL>               # From email address
ADMIN_URL=https://admin.ailex.com       # Admin panel URL for action links
SKIP_EMAIL=false                        # Skip actual email sending (dev/test)

# Core API Configuration (existing)
CORE_SUBS_API_URL=https://core.internal/api/v1  # For feature flags & admin lookup
REDIS_URL=redis://localhost:6379/0              # For feature flag caching
```

### Dependencies

Added to `requirements.txt`:
```
resend>=0.8.0    # Email delivery service
jinja2>=3.1.0    # Template rendering
```

### MJML CLI Requirement

The email service requires the MJML CLI for template rendering:

```bash
# Install globally
npm install -g mjml

# Verify installation
mjml --version
```

## API Endpoints

### Admin Email Lookup

The service queries the Core API for tenant admin emails:

```http
GET /tenant/{tenant_id}/admins
```

**Expected Response**:
```json
{
  "admins": [
    {"email": "<EMAIL>", "role": "admin"},
    {"email": "<EMAIL>", "role": "admin"}
  ]
}
```

## Workflow Integration

### Call End Flow

1. **Call Completion**: Voice pipeline ends, triggers `end_voice_session()`
2. **Feature Check**: Query Core API for tenant's `core_intake_sync` feature
3. **Routing Decision**:
   - **Has Feature**: Send webhook to Core (existing S-1 behavior)
   - **No Feature**: Send email to tenant admins
4. **Email Process** (if no feature):
   - Fetch admin emails from Core API
   - Build intake event with call data
   - Render MJML template with event context
   - Send email via Resend
   - Log success/failure

### Error Handling

- **No Admin Emails**: Log warning, return failure result
- **MJML Rendering Error**: Log error with details, raise RuntimeError
- **Resend API Error**: Log error, return failure result
- **Core API Unavailable**: Default to email fallback (fail-safe)

## Testing

### Unit Tests (`tests/email_fallback/test_email_service.py`)

- Service initialization and configuration
- MJML template rendering
- Admin email lookup (success/failure cases)
- Email sending with Resend mocking
- Error handling scenarios
- Skip email functionality

### Integration Tests (`tests/email_fallback/test_integration.py`)

- End-to-end workflow testing
- Feature flag decision logic
- Webhook vs email routing
- Recording URL inclusion
- Error handling in complete flow

### Running Tests

```bash
# Run email fallback tests
cd apps/voice-svc
pytest tests/email_fallback/ -v

# Run with coverage
pytest tests/email_fallback/ --cov=services.email_fallback --cov-report=html
```

## Monitoring and Logging

### Log Messages

**Feature Flag Check**:
```
INFO: Tenant {tenant_id} core_intake_sync check: {has_feature}
```

**Email Fallback Triggered**:
```
INFO: Tenant {tenant_id} lacks core_intake_sync feature - using email fallback
```

**Email Success**:
```
INFO: Email fallback sent successfully for call {call_id} to {count} recipients
```

**Email Failure**:
```
ERROR: Email fallback failed for call {call_id}: {error}
```

### Audit Logging

Email fallback usage can be tracked in the existing `audit_log` table:

```sql
INSERT INTO audit_log (tenant_id, action, details, created_at)
VALUES (?, 'email_fallback_sent', '{"call_id": "...", "recipients": [...]}', NOW());
```

## Customization

### Email Template

To customize the email template:

1. **Edit MJML**: Modify `templates/intake_email.mjml`
2. **Template Variables**: Available context variables:
   - `call_id`, `tenant_id`, `caller_name`, `caller_number`
   - `transcript`, `intent`, `booking`, `recording_url`
   - `created_at`, `admin_url`, `call_duration`
3. **Test Rendering**: Use unit tests to verify template changes

### Adding New Email Types

To add new email templates:

1. **Create Template**: Add new `.mjml` file in `templates/`
2. **Service Method**: Add method to `EmailFallbackService`
3. **Integration**: Call from appropriate workflow points
4. **Tests**: Add unit and integration tests

### Future Packages

The email service is designed to be extensible:

- **SMS Fallback**: Add Twilio integration for SMS notifications
- **Slack Integration**: Send notifications to Slack channels
- **Push Notifications**: Mobile app notifications
- **Multi-Channel**: Combine email, SMS, and push for critical alerts

## Troubleshooting

### Common Issues

**MJML CLI Not Found**:
```bash
npm install -g mjml
```

**No Admin Emails Found**:
- Check Core API endpoint `/tenant/{id}/admins`
- Verify tenant has users with admin role
- Check API authentication/authorization

**Resend API Errors**:
- Verify `RESEND_API_KEY` is valid
- Check Resend dashboard for quota/limits
- Verify sender domain is verified in Resend

**Template Rendering Errors**:
- Check MJML syntax in template
- Verify all template variables are provided
- Test with minimal template first

### Debug Mode

Enable debug logging for detailed troubleshooting:

```python
import logging
logging.getLogger('services.email_fallback').setLevel(logging.DEBUG)
```

## Security Considerations

- **API Keys**: Store `RESEND_API_KEY` securely, never in code
- **Email Content**: Sanitize user input in templates
- **Admin Lookup**: Validate tenant access to admin emails
- **Rate Limiting**: Monitor email sending rates to prevent abuse
- **PII Handling**: Be mindful of sensitive data in email content
