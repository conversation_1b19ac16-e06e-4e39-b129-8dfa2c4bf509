# Operations Runbook

## Overview

This runbook provides operational guidance for the AI Voice Receptionist system, covering monitoring, troubleshooting, scaling, and maintenance procedures.

## System Components

### Core Services
- **voice-svc**: Main voice service with call queue system
- **calendar-svc**: Calendar integration and booking management
- **notification-svc**: Email and webhook notifications
- **dashboard**: Admin interface

### Infrastructure
- **Redis**: Call queue, caching, and session storage
- **PostgreSQL**: Primary database for bookings and metadata
- **S3**: Call recording storage
- **Telnyx**: Voice infrastructure and phone numbers

## Redis Configuration & Sizing

### Memory Sizing Guidelines

**Call Queue System Requirements:**
- **Per Queued Call**: ~500 bytes (metadata + Redis overhead)
- **Per Active Counter**: ~100 bytes
- **Estimated Usage**: 1MB per 1000 queued calls

**Sizing Recommendations:**
```
Small Deployment (< 10 tenants):     256MB Redis
Medium Deployment (10-100 tenants):  1GB Redis
Large Deployment (100+ tenants):     4GB+ Redis
```

### Redis Configuration

**Production Settings:**
```redis
# Memory management
maxmemory 1gb
maxmemory-policy allkeys-lru

# Persistence (for call queue durability)
save 900 1
save 300 10
save 60 10000

# Network
timeout 300
tcp-keepalive 300

# Logging
loglevel notice
```

**Docker Compose Example:**
```yaml
redis:
  image: redis:7-alpine
  command: >
    redis-server
    --maxmemory 1gb
    --maxmemory-policy allkeys-lru
    --save 900 1
    --save 300 10
    --save 60 10000
  volumes:
    - redis_data:/data
```

### Redis Monitoring

**Key Metrics to Monitor:**
```bash
# Memory usage
redis-cli INFO memory | grep used_memory_human

# Queue depths
redis-cli --scan --pattern "voice:queue:*" | xargs -I {} redis-cli XLEN {}

# Active call counts
redis-cli --scan --pattern "voice:active:*" | xargs -I {} redis-cli GET {}

# Connection count
redis-cli INFO clients | grep connected_clients
```

**Alert Thresholds:**
- Memory usage > 80%
- Queue depth > 5 calls per tenant
- Connection count > 1000
- Keyspace misses > 10%

## Call Queue System Operations

### Health Checks

**Queue System Health:**
```bash
# Check queue worker status
curl http://localhost:8000/api/v1/queue/health

# Check specific tenant queue
curl http://localhost:8000/api/v1/queue/?tenant_id=TENANT_ID

# Check all tenant queues (admin)
curl http://localhost:8000/api/v1/queue/all
```

**Expected Response (Healthy):**
```json
{
  "status": "healthy",
  "queue_service": "connected",
  "queue_worker": "running",
  "redis_connection": "ok",
  "max_active_per_tenant": 4,
  "max_queue_per_tenant": 10
}
```

### Common Operations

**Restart Queue Worker:**
```bash
# Stop worker
curl -X POST http://localhost:8000/api/v1/queue/worker/stop

# Start worker
curl -X POST http://localhost:8000/api/v1/queue/worker/start
```

**Clear Stuck Queue:**
```bash
# Clear specific tenant queue (DESTRUCTIVE)
curl -X POST "http://localhost:8000/api/v1/queue/clear?tenant_id=TENANT_ID"

# Clear all queues (EMERGENCY ONLY)
redis-cli --scan --pattern "voice:queue:*" | xargs redis-cli DEL
```

**Reset Active Counters:**
```bash
# Reset specific tenant
redis-cli DEL voice:active:TENANT_ID

# Reset all active counters
redis-cli --scan --pattern "voice:active:*" | xargs redis-cli DEL
```

### Scaling Operations

**Increase Tenant Capacity:**
```bash
# Update environment variables
export MAX_ACTIVE_CALLS_PER_TENANT=8
export MAX_QUEUE_LENGTH_PER_TENANT=20

# Restart service to apply changes
systemctl restart voice-svc
```

**Monitor Impact:**
```bash
# Watch queue depths
watch -n 5 'redis-cli --scan --pattern "voice:queue:*" | xargs -I {} sh -c "echo -n \"{}: \"; redis-cli XLEN {}"'

# Monitor active calls
watch -n 5 'redis-cli --scan --pattern "voice:active:*" | xargs -I {} sh -c "echo -n \"{}: \"; redis-cli GET {}"'
```

## Troubleshooting Guide

### High Call Volume Issues

**Symptoms:**
- Increased queue depths
- Higher call rejection rates
- Longer wait times

**Investigation:**
```bash
# Check current load
curl http://localhost:8000/api/v1/queue/all | jq '.[] | select(.queued > 0)'

# Monitor rejection rate
grep "Queue full" /var/log/voice-svc.log | tail -20

# Check Redis memory
redis-cli INFO memory
```

**Resolution:**
1. Increase `MAX_ACTIVE_CALLS_PER_TENANT` if resources allow
2. Scale Redis if memory usage > 80%
3. Add more voice-svc instances with load balancing
4. Implement tenant-specific limits for high-volume users

### Queue Worker Issues

**Symptoms:**
- Calls stuck in queue
- Worker status "stopped"
- No call processing

**Investigation:**
```bash
# Check worker logs
tail -f /var/log/voice-svc.log | grep -i "queue worker"

# Verify Redis connectivity
redis-cli ping

# Check for stuck tasks
ps aux | grep voice-svc
```

**Resolution:**
```bash
# Restart worker via API
curl -X POST http://localhost:8000/api/v1/queue/worker/stop
curl -X POST http://localhost:8000/api/v1/queue/worker/start

# Or restart entire service
systemctl restart voice-svc

# Clear stuck queues if necessary
redis-cli --scan --pattern "voice:queue:*" | xargs redis-cli DEL
```

### Redis Performance Issues

**Symptoms:**
- Slow queue operations (> 10ms)
- High memory usage
- Connection timeouts

**Investigation:**
```bash
# Check Redis performance
redis-cli --latency-history -i 1

# Monitor slow queries
redis-cli CONFIG SET slowlog-log-slower-than 10000
redis-cli SLOWLOG GET 10

# Check memory fragmentation
redis-cli INFO memory | grep fragmentation
```

**Resolution:**
1. **High Memory**: Increase Redis memory or implement eviction
2. **Fragmentation**: Restart Redis during maintenance window
3. **Slow Queries**: Optimize queue operations or scale Redis
4. **Connections**: Implement connection pooling

### Call Recording Issues

**Symptoms:**
- Missing recordings
- S3 upload failures
- Disk space issues

**Investigation:**
```bash
# Check recording service status
curl http://localhost:8000/api/v1/recordings/health

# Monitor S3 uploads
grep "recording" /var/log/voice-svc.log | grep -i error

# Check disk space
df -h /tmp
```

**Resolution:**
1. Verify S3 credentials and permissions
2. Check network connectivity to S3
3. Monitor and clean temporary files
4. Implement recording retention policies

## Monitoring & Alerting

### Critical Alerts

**Queue System:**
```yaml
# Queue depth too high
- alert: QueueDepthCritical
  expr: max(redis_stream_length{key=~"voice:queue:.*"}) > 8
  for: 2m
  labels:
    severity: critical

# Queue worker down
- alert: QueueWorkerDown
  expr: queue_worker_running == 0
  for: 1m
  labels:
    severity: critical

# High call rejection rate
- alert: CallRejectionHigh
  expr: rate(calls_rejected_total[5m]) > 0.2
  for: 3m
  labels:
    severity: warning
```

**Redis:**
```yaml
# Redis memory usage
- alert: RedisMemoryHigh
  expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.8
  for: 5m
  labels:
    severity: warning

# Redis down
- alert: RedisDown
  expr: redis_up == 0
  for: 1m
  labels:
    severity: critical
```

### Performance Metrics

**Key Performance Indicators:**
- P99 queue operation latency < 10ms
- Call setup time < 2 seconds
- Queue worker processing rate > 10 calls/minute
- Redis memory usage < 80%
- Call rejection rate < 5%

**Dashboard Queries:**
```promql
# Queue operation latency
histogram_quantile(0.99, rate(queue_operation_duration_seconds_bucket[5m]))

# Active calls by tenant
sum by (tenant_id) (redis_key_value{key=~"voice:active:.*"})

# Queue depth by tenant
sum by (tenant_id) (redis_stream_length{key=~"voice:queue:.*"})

# Call processing rate
rate(calls_processed_total[5m])
```

## Maintenance Procedures

### Regular Maintenance

**Daily:**
- Check queue depths and clear any stuck queues
- Monitor Redis memory usage
- Review error logs for patterns

**Weekly:**
- Analyze call volume trends
- Review and adjust capacity limits
- Clean up old recordings per retention policy

**Monthly:**
- Redis performance analysis
- Capacity planning review
- Update monitoring thresholds

### Emergency Procedures

**Complete System Reset:**
```bash
# 1. Stop all services
systemctl stop voice-svc

# 2. Clear all queues and counters
redis-cli FLUSHDB

# 3. Restart services
systemctl start voice-svc

# 4. Verify health
curl http://localhost:8000/api/v1/queue/health
```

**Partial Queue Reset:**
```bash
# Clear queues but preserve other data
redis-cli --scan --pattern "voice:queue:*" | xargs redis-cli DEL
redis-cli --scan --pattern "voice:active:*" | xargs redis-cli DEL
```

### Backup & Recovery

**Redis Backup:**
```bash
# Create backup
redis-cli BGSAVE
cp /var/lib/redis/dump.rdb /backup/redis-$(date +%Y%m%d).rdb

# Restore from backup
systemctl stop redis
cp /backup/redis-20231201.rdb /var/lib/redis/dump.rdb
systemctl start redis
```

**Configuration Backup:**
```bash
# Backup environment configuration
cp .env /backup/env-$(date +%Y%m%d)

# Backup Redis configuration
cp /etc/redis/redis.conf /backup/redis-conf-$(date +%Y%m%d)
```

## Capacity Planning

### Growth Projections

**Tenant Growth:**
- Small: 1-10 tenants, 50 calls/day each
- Medium: 10-100 tenants, 200 calls/day each  
- Large: 100+ tenants, 500+ calls/day each

**Resource Requirements:**
```
Small:  2 CPU, 4GB RAM, 256MB Redis
Medium: 4 CPU, 8GB RAM, 1GB Redis
Large:  8+ CPU, 16GB+ RAM, 4GB+ Redis
```

### Scaling Triggers

**Scale Up When:**
- Queue depths consistently > 3
- Redis memory usage > 70%
- Call rejection rate > 2%
- P99 latency > 5ms

**Scale Out When:**
- Single instance CPU > 80%
- Need geographic distribution
- Tenant isolation requirements
