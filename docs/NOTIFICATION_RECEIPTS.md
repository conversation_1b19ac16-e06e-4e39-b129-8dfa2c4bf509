# Notification Delivery Receipts

This document describes the notification delivery receipt system that tracks email and SMS delivery status through webhooks from Resend and Telnyx providers.

## Overview

The notification service captures delivery status events from email and SMS providers to track successful deliveries and failures. This enables monitoring of notification delivery rates and troubleshooting failed deliveries through the admin dashboard.

## Database Schema

### delivery_receipts Table

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| tenant_id | UUID | Tenant identifier |
| message_id | TEXT | Provider message identifier |
| channel | ENUM | 'email' or 'sms' |
| provider | ENUM | 'resend' or 'telnyx' |
| status | ENUM | 'delivered' or 'failed' |
| delivered_at | TIMESTAMPTZ | Delivery timestamp (null for failed) |
| failure_reason | TEXT | Failure description (null for delivered) |
| raw_payload | JSONB | Original webhook payload |
| created_at | TIMESTAMPTZ | Record creation timestamp |

## Webhook Endpoints

### Resend Email Webhooks

**Endpoint:** `POST /webhooks/resend`

**Headers:**
- `resend-signature`: HMAC-SHA256 signature for verification

**Payload Example:**
```json
{
  "type": "email.delivered",
  "created_at": "2024-01-01T12:00:00Z",
  "data": {
    "id": "resend_msg_123",
    "to": [{"email": "<EMAIL>"}],
    "subject": "Welcome Email"
  }
}
```

**Supported Event Types:**
- `email.delivered` → status: delivered
- `email.bounced` → status: failed
- `email.complained` → status: failed

### Telnyx SMS Webhooks

**Endpoint:** `POST /webhooks/telnyx`

**Headers:**
- `telnyx-signature-ed25519`: ED25519 signature for verification
- `telnyx-timestamp`: Unix timestamp

**Payload Example:**
```json
{
  "event_type": "message.sent",
  "id": "webhook_456",
  "occurred_at": "2024-01-01T12:00:00Z",
  "payload": {
    "id": "telnyx_msg_789",
    "to": "+15551234567",
    "from": "+15559876543"
  }
}
```

**Supported Event Types:**
- `message.sent` → status: delivered
- `message.failed` → status: failed

## Admin API

### Get Failed Notifications

**Endpoint:** `GET /api/v1/admin/notifications/failed`

**Query Parameters:**
- `tenant_id` (optional): Filter by tenant UUID
- `since` (optional): Filter notifications created after this ISO datetime
- `until` (optional): Filter notifications created before this ISO datetime  
- `limit` (optional): Maximum results (1-1000, default: 50)

**Response Example:**
```json
[
  {
    "message_id": "resend_msg_123",
    "channel": "email",
    "provider": "resend",
    "failure_reason": "Email address bounced",
    "created_at": "2024-01-01T12:00:00Z"
  }
]
```

## Setup Instructions

### 1. Environment Variables

Add these secrets to your `.env` file:

```bash
# Webhook signature verification
RESEND_WEBHOOK_SECRET=your_resend_webhook_secret
TELNYX_WEBHOOK_SECRET=your_telnyx_webhook_secret

# Database connection
DATABASE_URL=********************************/db
DATABASE_URL_ASYNC=postgresql+asyncpg://user:pass@host:5432/db
```

### 2. Provider Configuration

#### Resend Setup
1. Log into your Resend dashboard
2. Go to Settings → Webhooks
3. Add webhook URL: `https://your-domain.com/webhooks/resend`
4. Select events: `email.delivered`, `email.bounced`, `email.complained`
5. Copy the webhook secret to `RESEND_WEBHOOK_SECRET`

#### Telnyx Setup
1. Log into your Telnyx portal
2. Go to Messaging → Webhooks
3. Add webhook URL: `https://your-domain.com/webhooks/telnyx`
4. Select events: `message.sent`, `message.failed`
5. Copy the webhook secret to `TELNYX_WEBHOOK_SECRET`

### 3. Database Migration

Run the Alembic migration to create the delivery_receipts table:

```bash
alembic upgrade head
```

## Security

- All webhook endpoints verify signatures using HMAC-SHA256
- Invalid signatures return HTTP 400 errors
- Raw payloads are stored for debugging but should not contain sensitive data
- Consider implementing rate limiting for webhook endpoints in production

## Monitoring

- Monitor webhook endpoint response times and error rates
- Set up alerts for high failure rates by provider/channel
- Use the admin API to investigate delivery issues
- Check raw_payload field for detailed error information

## Testing

Run the test suite to verify functionality:

```bash
# Install test dependencies
pip install -r requirements.txt

# Run tests with coverage
pytest --cov=app --cov-report=term-missing

# Run specific test files
pytest tests/test_webhooks.py
pytest tests/test_admin_api.py
```

## Troubleshooting

### Common Issues

1. **Invalid Signature Errors**
   - Verify webhook secrets are correctly configured
   - Check that the webhook URL is using HTTPS in production
   - Ensure the signature header names match exactly

2. **Missing Message IDs**
   - Some webhook events may not contain message IDs
   - These are logged and ignored with status "ignored"

3. **Database Connection Issues**
   - Verify DATABASE_URL and DATABASE_URL_ASYNC are correct
   - Ensure the database user has CREATE/INSERT/UPDATE permissions
   - Check that the delivery_receipts table exists

### Logs

The service logs important events:
- Webhook receipt and processing
- Signature verification failures  
- Database operation errors
- Missing or invalid payload data

Check application logs for detailed error information.
