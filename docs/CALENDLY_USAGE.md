# Calendly Integration Usage Guide

This guide explains how to use the Calendly integration in the AI Voice Receptionist system.

## Overview

The Calendly integration allows the AI Voice Receptionist to:
- List available Calendly event types
- Create scheduling links for specific event types
- Check availability across event types
- Create bookings through the unified booking interface

## Prerequisites

1. **Calendly Account**: You need a Calendly account with API access
2. **OAuth Setup**: Configure Calendly OAuth credentials in your environment
3. **Feature Access**: Ensure the `calendar_booking` feature is enabled for your subscription

## Environment Configuration

Add the following environment variables to your `.env` file:

```bash
# Calendly OAuth
CALENDLY_CLIENT_ID=your_calendly_client_id
CALENDLY_CLIENT_SECRET=your_calendly_client_secret
CALENDLY_REDIRECT_URI=your_calendly_redirect_uri

# Calendly API
CALENDLY_API_BASE=https://api.calendly.com

# Caching
CACHE_URL=redis://localhost:6379/1
```

## Authentication Flow

### 1. Initiate OAuth Flow

```bash
curl -X GET "http://localhost:8000/api/v1/oauth/calendly/authorize?user_id=your_user_id"
```

This will redirect to Calendly's authorization page.

### 2. Handle OAuth Callback

After user authorization, Calendly will redirect to your callback URL with an authorization code. The system will automatically exchange this for access tokens and store the connection.

## API Endpoints

### Get Event Types

Retrieve all available Calendly event types for the authenticated user.

```bash
curl -X GET "http://localhost:8000/api/v1/calendly/event-types" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Response:**
```json
[
  {
    "uri": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
    "name": "30 Minute Meeting",
    "description": "A brief 30-minute consultation",
    "duration": 30,
    "scheduling_url": "https://calendly.com/user/30min",
    "active": true,
    "kind": "solo",
    "type": "StandardEventType",
    "color": "#0069ff",
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-01T00:00:00.000000Z"
  }
]
```

### Create Scheduling Link

Create a scheduling link for a specific event type.

```bash
curl -X POST "http://localhost:8000/api/v1/calendly/scheduling-links" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "event_type_id": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
    "max_events": 1,
    "start_time": "2023-12-01T09:00:00Z",
    "end_time": "2023-12-31T17:00:00Z"
  }'
```

**Response:**
```json
{
  "booking_url": "https://calendly.com/d/abc123/30-minute-meeting",
  "owner": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
  "owner_type": "EventType",
  "created_at": "2023-01-01T00:00:00.000000Z"
}
```

### Check Availability

Get availability for Calendly event types.

```bash
curl -X GET "http://localhost:8000/api/v1/calendly/availability?start_time=2023-12-01T09:00:00Z&end_time=2023-12-01T17:00:00Z" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Response:**
```json
[
  {
    "calendar_id": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
    "busy": []
  }
]
```

## Unified Booking Interface

The Calendly integration works seamlessly with the existing booking interface.

### Check Availability

```bash
curl -X POST "http://localhost:8000/api/v1/availability?provider=calendly" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "calendar_ids": ["https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA"],
    "time_min": "2023-12-01T09:00:00Z",
    "time_max": "2023-12-01T17:00:00Z"
  }'
```

### Create Booking

```bash
curl -X POST "http://localhost:8000/api/v1/book" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "calendly",
    "calendar_id": "https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
    "summary": "Voice Consultation",
    "start_at": "2023-12-01T10:00:00Z",
    "end_at": "2023-12-01T10:30:00Z"
  }'
```

**Response:**
```json
{
  "id": "booking-uuid-123",
  "provider": "calendly",
  "external_id": "https://calendly.com/d/abc123/30-minute-meeting",
  "start_at": "2023-12-01T10:00:00Z",
  "booked_at": "2023-11-30T15:30:00Z",
  "provider_event_link": "https://calendly.com/d/abc123/30-minute-meeting"
}
```

## Voice Agent Integration

The voice agent can use Calendly through the same interface as other calendar providers:

```python
from packages.calendar_core import get_provider

# Get Calendly provider
provider = get_provider(firm_id="your-firm", provider_name="calendly")

# Check availability (proposeSlot)
availability = await provider.get_availability(
    calendar_ids=["https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA"],
    time_min=start_time,
    time_max=end_time
)

# Create booking (confirmSlot)
booking = await provider.create_event(
    calendar_id="https://api.calendly.com/event_types/AAAAAAAAAAAAAAAA",
    event={
        "summary": "Voice Consultation",
        "start": "2023-12-01T10:00:00Z",
        "end": "2023-12-01T10:30:00Z"
    }
)
```

## Webhook Integration

Calendly webhooks are automatically processed to update booking status. The webhook handler processes:

- `invitee.created` - When someone books through a Calendly link
- `invitee.canceled` - When a booking is canceled

Webhooks are verified using HMAC SHA256 signature validation.

## Error Handling

The integration includes comprehensive error handling:

- **Rate Limiting**: Respects Calendly's 30 requests/minute limit with exponential backoff
- **Token Refresh**: Automatically refreshes expired access tokens
- **Fallback**: Returns empty availability on API errors to prevent booking failures
- **Validation**: Validates all input data and provides clear error messages

## Caching

Event types are cached for 30 minutes to improve performance and reduce API calls. The cache is automatically invalidated when:

- Event types are modified in Calendly
- The cache TTL expires
- The service is restarted

## Monitoring

Monitor the integration using:

- **Logs**: Check application logs for API errors and rate limiting
- **Metrics**: Track booking success rates and API response times
- **Health Checks**: Verify OAuth token validity and API connectivity

## Troubleshooting

### Common Issues

1. **"No active Calendly connection found"**
   - Ensure OAuth flow is completed
   - Check that the connection is marked as active in the database

2. **Rate limit exceeded**
   - The system automatically retries with exponential backoff
   - Consider reducing the frequency of API calls

3. **Invalid event type ID**
   - Ensure you're using the full Calendly URI format
   - Verify the event type exists and is active

4. **Webhook signature verification failed**
   - Check that the webhook secret is correctly configured
   - Ensure the webhook URL is accessible from Calendly

### Debug Mode

Enable debug logging to troubleshoot issues:

```bash
export LOG_LEVEL=DEBUG
```

This will provide detailed information about API calls, token refresh, and error conditions.
