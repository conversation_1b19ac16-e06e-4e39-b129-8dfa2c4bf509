# Booking Flow Documentation

This document describes the booking flow implementation with real provider integration for Google Calendar and Calendly.

## Overview

The booking system provides a unified interface for scheduling appointments across multiple calendar providers. It supports both a two-step flow (propose → confirm) and a legacy single-step flow for backward compatibility.

## Architecture

```mermaid
graph TD
    A[Client Request] --> B{Booking Type}
    B -->|Propose| C[proposeSlot()]
    B -->|Confirm| D[confirmSlot()]
    B -->|Legacy| E[/book endpoint]
    
    C --> F[get_aggregated_availability()]
    F --> G[Google Provider]
    F --> H[Calendly Provider]
    F --> I[Outlook Provider]
    
    G --> J[Google Calendar API]
    H --> K[Calendly API]
    I --> L[Microsoft Graph API]
    
    D --> M[Provider create_event()]
    E --> D
    
    M --> N[Database Booking Record]
    N --> O[Response with Provider Link]
```

## API Endpoints

### 1. Propose Slots - `/api/v1/propose`

Proposes available booking slots across all configured providers.

**Request:**
```json
{
  "preferred_start": "2024-01-15T14:00:00Z",  // Optional
  "duration_minutes": 30,                      // Default: 30
  "max_slots": 3                              // Default: 3
}
```

**Response:**
```json
[
  {
    "start_at": "2024-01-15T14:00:00Z",
    "end_at": "2024-01-15T14:30:00Z",
    "provider": "google",
    "calendar_id": "primary",
    "duration_minutes": 30
  },
  {
    "start_at": "2024-01-15T15:00:00Z",
    "end_at": "2024-01-15T15:30:00Z",
    "provider": "calendly",
    "calendar_id": "https://api.calendly.com/event_types/123",
    "duration_minutes": 30
  }
]
```

### 2. Confirm Slot - `/api/v1/confirm`

Confirms a proposed slot and creates the actual booking.

**Request:**
```json
{
  "start_at": "2024-01-15T14:00:00Z",
  "end_at": "2024-01-15T14:30:00Z",
  "provider": "google",
  "calendar_id": "primary",
  "summary": "Client Consultation",
  "attendee_email": "<EMAIL>",     // Optional
  "description": "Initial consultation"       // Optional
}
```

**Response:**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "provider": "GOOGLE",
  "external_id": "google-event-123",
  "start_at": "2024-01-15T14:00:00Z",
  "booked_at": "2024-01-15T10:30:00Z",
  "provider_event_link": "https://calendar.google.com/event/123"
}
```

### 3. Legacy Book - `/api/v1/book`

Legacy single-step booking endpoint for backward compatibility.

**Request:**
```json
{
  "provider": "google",
  "calendar_id": "primary",
  "summary": "Client Consultation",
  "start_at": "2024-01-15T14:00:00Z",
  "end_at": "2024-01-15T14:30:00Z"
}
```

**Response:** Same as confirm endpoint.

## Provider-Specific Behavior

### Google Calendar

- **Event Creation**: Creates actual calendar events with Google Meet links
- **Availability**: Uses Google Calendar FreeBusy API
- **Response**: Returns `htmlLink` for calendar event and meeting join URL
- **Retry Logic**: Handles rate limiting and server errors with exponential backoff

### Calendly

- **Event Creation**: Creates scheduling links (not direct events)
- **Availability**: Uses Calendly user availability API
- **Response**: Returns `booking_url` for scheduling link
- **Special Handling**: Event type URIs are used as calendar IDs

### Outlook/Microsoft Graph

- **Event Creation**: Creates calendar events with Teams meeting links
- **Availability**: Uses Microsoft Graph getSchedule API
- **Response**: Returns `webLink` and Teams `joinUrl`
- **Authentication**: Uses Microsoft Graph OAuth tokens

## Error Handling

### Slot Unavailable (409)

Returned when a proposed slot is no longer available:

```json
{
  "detail": "Slot is no longer available: conflict detected"
}
```

**Client Action**: Call `/propose` again to get new available slots.

### Provider Error (502)

Returned when provider APIs fail:

```json
{
  "detail": "Provider error: Google Calendar API timeout"
}
```

**Retry Logic**: Automatically retries up to 2 times for 5xx errors.

### Rate Limiting (429)

Returned when rate limits are exceeded:

```json
{
  "detail": "Rate limit exceeded"
}
```

**Rate Limits**: 5 requests per minute per tenant for booking endpoints.

## Implementation Details

### Availability Aggregation

The `get_aggregated_availability()` function:

1. Queries all configured providers in parallel
2. Handles provider failures gracefully (continues with working providers)
3. Normalizes response formats across providers
4. Returns combined availability data

### Slot Proposal Algorithm

The `proposeSlot()` function:

1. Gets aggregated availability from all providers
2. Finds gaps in busy times using `get_available_slots()`
3. Applies 5-minute buffer between appointments
4. Sorts slots by start time
5. Returns up to `max_slots` earliest available slots

### Booking Confirmation

The `confirmSlot()` function:

1. Validates slot is not in the past
2. Creates event/scheduling link via provider API
3. Handles provider-specific response formats
4. Persists booking record to database
5. Returns unified response format

### Retry Logic

Uses `tenacity` library for robust error handling:

- **Retries**: Up to 2 attempts for provider errors
- **Backoff**: Exponential backoff (2-5 seconds)
- **Conditions**: Only retries on 5xx server errors
- **Exceptions**: `SlotUnavailableError` and client errors (4xx) are not retried

## Database Schema

Bookings are stored in the `bookings` table:

```sql
CREATE TABLE bookings (
    id UUID PRIMARY KEY,
    firm_id UUID NOT NULL,
    provider booking_provider_enum NOT NULL,
    external_id VARCHAR,
    start_at TIMESTAMP NOT NULL,
    booked_at TIMESTAMP NOT NULL,
    provider_event_link VARCHAR,
    metadata_ JSON,
    status VARCHAR DEFAULT 'confirmed',
    -- Additional fields...
);
```

## Testing Strategy

### Unit Tests

- Mock provider responses using `respx` for HTTP calls
- Test error conditions and retry logic
- Verify database operations
- Test availability aggregation logic

### Integration Tests

- End-to-end booking flows for each provider
- Mixed provider scenarios
- Error handling and recovery
- Rate limiting behavior

### Test Coverage Requirements

- Minimum 90% code coverage
- All error paths tested
- Provider-specific edge cases covered

## Monitoring and Metrics

The system emits StatsD metrics for monitoring:

- `booking.proposed` - Successful slot proposals
- `booking.confirmed` - Successful confirmations
- `booking.slot_unavailable` - Slot conflicts
- `booking.provider_error` - Provider API failures
- `booking.rate_limited` - Rate limit hits

## Configuration

### Environment Variables

- `GOOGLE_CALENDAR_API_KEY` - Google Calendar API credentials
- `CALENDLY_CLIENT_ID` - Calendly OAuth client ID
- `CALENDLY_CLIENT_SECRET` - Calendly OAuth secret
- `MICROSOFT_CLIENT_ID` - Microsoft Graph client ID

### Provider Capabilities

Provider capabilities are defined in `capability_matrix.json`:

```json
{
  "google": {
    "supports_availability": true,
    "supports_events": true,
    "supports_meeting_links": true
  },
  "calendly": {
    "supports_availability": true,
    "supports_events": false,
    "supports_scheduling_links": true
  }
}
```

## Migration Guide

### From Legacy `/book` Endpoint

The legacy endpoint continues to work but internally uses the new `confirmSlot()` function. No client changes required.

### To New Two-Step Flow

1. Replace direct `/book` calls with `/propose` + `/confirm`
2. Handle 409 responses by re-proposing slots
3. Update error handling for new error codes
4. Implement retry logic for provider errors

## Security Considerations

- All provider API calls use OAuth 2.0 tokens
- Tokens are managed by the auth service
- Rate limiting prevents abuse
- Input validation on all endpoints
- SQL injection protection via SQLAlchemy ORM

## Performance Optimization

- Provider calls are made in parallel where possible
- Availability data is cached for 30 minutes
- Database queries use appropriate indexes
- Connection pooling for provider APIs
- Async/await throughout for non-blocking I/O
