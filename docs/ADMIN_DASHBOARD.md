# Admin Dashboard Guide

This guide covers the admin dashboard features for the AiLex Receptionist system.

## Failed Notifications Panel

The Failed Notifications panel allows administrators to view and manage email/SMS delivery failures across all tenants.

### Features

- **Real-time Monitoring**: View failed notifications as they occur
- **Multi-tenant Support**: Filter by specific tenants or view all
- **Advanced Filtering**: Filter by date range, channel (email/SMS), and provider
- **Resend Functionality**: Retry failed notifications with one click
- **Pagination**: Handle large datasets with 50 items per page
- **Export Capabilities**: Export data for further analysis

### Accessing the Panel

1. Navigate to the admin dashboard
2. Click on "Failed Notifications" in the sidebar
3. The panel will load with the most recent failures

### Using Filters

#### Date Range Filter
- Click the date range picker to select a specific time period
- Use preset options: Today, Yesterday, Last 7 days, Last 30 days
- Or select a custom range using the calendar

#### Tenant Filter
- Select "All tenants" to view failures across all tenants
- Choose a specific tenant to filter results
- Tenant status (active/inactive) is displayed for reference

#### Channel Filter
- Filter by communication channel:
  - **Email**: Email delivery failures
  - **SMS**: SMS delivery failures
  - **All channels**: View both types

#### Provider Filter
- Filter by notification provider (e.g., Resend, Telnyx)
- Type provider name to filter results

### Understanding the Data

Each row in the table shows:

- **Time**: When the notification failed
- **Channel**: Email or SMS (with color-coded badges)
- **Provider**: The service that attempted delivery
- **Message ID**: Unique identifier for tracking
- **Failure Reason**: Detailed error message
- **Actions**: Resend button for retry attempts

### Resending Notifications

1. Locate the failed notification in the table
2. Click the "Re-send" button in the Actions column
3. A toast notification will confirm success or show any errors
4. The table will refresh to show updated status

### Pagination

- Navigate through pages using Previous/Next buttons
- View current page and total pages at the bottom
- Each page shows up to 50 notifications

### Error Handling

If the panel fails to load:
1. Check your network connection
2. Click the "Retry" button if shown
3. Contact support if issues persist

### Performance Tips

- Use date range filters to limit data for faster loading
- Filter by specific tenants when investigating issues
- Use the provider filter to identify problematic services

## API Integration

The panel integrates with the following API endpoints:

- `GET /api/admin/notifications/failed` - Fetch failed notifications
- `POST /api/admin/notifications/resend` - Resend a notification
- `GET /api/admin/tenants` - Fetch tenant list

### Response Format

Failed notifications API returns:

```json
{
  "data": [
    {
      "id": "notif-123",
      "time": "2024-01-01T10:00:00Z",
      "channel": "email",
      "provider": "Resend",
      "messageId": "msg-456",
      "failureReason": "Invalid email address",
      "tenantId": "tenant-1",
      "tenantName": "Law Firm ABC",
      "retryCount": 1,
      "createdAt": "2024-01-01T10:00:00Z",
      "updatedAt": "2024-01-01T10:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 150,
    "totalPages": 3
  }
}
```

## Troubleshooting

### Common Issues

**Panel not loading**
- Verify API endpoints are accessible
- Check browser console for JavaScript errors
- Ensure proper authentication

**Filters not working**
- Clear browser cache and reload
- Check if API supports the filter parameters
- Verify date format in requests

**Resend failing**
- Check if the original failure reason is resolved
- Verify the notification service is operational
- Contact the notification provider if needed

### Support

For technical issues or questions:
1. Check the browser console for errors
2. Review API response status codes
3. Contact the development team with specific error messages

## Security Considerations

- Access is restricted to admin users only
- All API calls include proper authentication
- Sensitive data is masked in logs
- Failed notification details may contain PII - handle appropriately

## Future Enhancements

Planned improvements include:
- Bulk resend operations
- Advanced analytics and reporting
- Automated retry policies
- Integration with monitoring systems
- Export to CSV/Excel functionality
