# Google OAuth Integration

This document outlines the Google OAuth 2.0 implementation for the calendar service, enabling secure access to Google Calendar APIs.

## Overview

The Google OAuth integration allows users to connect their Google Calendar accounts to the AI Lex Receptionist system. This enables the system to:

- Check calendar availability
- Create new events
- Update existing events
- Manage attendee notifications

## Implementation Details

### OAuth Flow

The implementation follows the OAuth 2.0 authorization code flow:

1. User initiates the OAuth flow from the dashboard
2. System generates an OAuth state token and redirects to Google's authorization page
3. User authenticates with Google and grants permissions
4. Google redirects back to our callback URL with an authorization code
5. System exchanges the code for access and refresh tokens
6. Tokens are securely stored in the database

### Key Components

1. **OAuthService** (`app/services/oauth_service.py`)
   - Handles OAuth flow and token management
   - Provides methods for generating authorization URLs and exchanging authorization codes
   - Manages token refresh

2. **API Endpoints** (`app/api/v1/endpoints/calendar.py`)
   - `/oauth/{provider}/authorize` - Initiates the OAuth flow
   - `/oauth/{provider}/callback` - Handles the OAuth callback

3. **Database Models** (`app/models/calendar_connection.py`)
   - `CalendarConnection` - Stores OAuth tokens and connection details

4. **Schemas** (`app/schemas/calendar.py`)
   - `OAuthState` - For securely passing state during OAuth flow
   - `OAuthTokenResponse` - Standardized response format for OAuth tokens

## Setup Instructions

### Prerequisites

1. Create a Google Cloud Project
2. Enable the Google Calendar API
3. Configure OAuth consent screen
4. Create OAuth 2.0 credentials

### Configuration

Add the following environment variables to your `.env` file:

```env
# Google OAuth
GOOGLE_CLIENT_ID=your-client-id
GOOGLE_CLIENT_SECRET=your-client-secret
GOOGLE_REDIRECT_URI=https://your-domain.com/api/v1/oauth/google/callback
```

### Required Scopes

The following scopes are requested during OAuth:

- `https://www.googleapis.com/auth/calendar` - Full access to Google Calendar
- `https://www.googleapis.com/auth/calendar.events` - Manage events in Google Calendar

## Usage

### Initiating OAuth Flow

1. Redirect the user to:
   ```
   GET /api/v1/oauth/google/authorize?user_id={user_id}
   ```

2. After successful authentication, Google will redirect to the callback URL with an authorization code.

### Handling the Callback

The callback URL will receive the authorization code which should be exchanged for tokens:

```
GET /api/v1/oauth/google/callback?code={code}&state={state}
```

### Storing Tokens

Upon successful authentication, the system will:

1. Exchange the authorization code for access and refresh tokens
2. Store the tokens securely in the database
3. Create a new `CalendarConnection` record

### Token Refresh

The implementation automatically handles token refresh using the refresh token when the access token expires.

## Security Considerations

1. **State Parameter**
   - All OAuth requests include a state parameter to prevent CSRF attacks
   - The state is signed and includes a timestamp for expiration

2. **Token Storage**
   - Access tokens are encrypted at rest
   - Refresh tokens are stored securely
   - Tokens are only sent over HTTPS

3. **Rate Limiting**
   - OAuth endpoints are rate limited to prevent abuse
   - Failed authentication attempts are logged and monitored

## Error Handling

Common error scenarios and their resolutions:

| Error | Cause | Resolution |
|-------|-------|------------|
| `invalid_grant` | Expired or invalid refresh token | Re-authenticate the user |
| `invalid_request` | Missing or invalid parameters | Check request parameters |
| `unauthorized_client` | Client authentication failed | Verify client credentials |
| `access_denied` | User denied permission | Inform user about required permissions |

## Testing

### Local Testing

1. Set up a local tunnel (e.g., ngrok) to expose your local server:
   ```bash
   ngrok http 8000
   ```

2. Update your Google Cloud OAuth credentials with the ngrok URL:
   - Authorized JavaScript origins: `https://your-ngrok-subdomain.ngrok.io`
   - Authorized redirect URIs: `https://your-ngrok-subdomain.ngrok.io/api/v1/oauth/google/callback`

3. Update your `.env` file with the ngrok URL:
   ```env
   GOOGLE_REDIRECT_URI=https://your-ngrok-subdomain.ngrok.io/api/v1/oauth/google/callback
   ```

### Unit Tests

Run the test suite:

```bash
pytest tests/test_oauth.py
```

## Troubleshooting

### Common Issues

1. **Redirect URI Mismatch**
   - Ensure the redirect URI in your Google Cloud Console matches exactly with what's in your `.env` file
   - Include/exclude the trailing slash as needed

2. **Invalid Scope**
   - Verify the requested scopes are correctly configured in the Google Cloud Console
   - Check that the scopes are properly formatted

3. **Token Refresh Failures**
   - Ensure the refresh token hasn't been revoked by the user
   - Check that the client ID and secret are correct

## Monitoring and Logging

All OAuth-related events are logged with appropriate log levels:

- `INFO`: OAuth flow initiated, tokens obtained
- `WARNING`: Token refresh required
- `ERROR`: Authentication failures, token validation errors

Logs include relevant metadata while ensuring sensitive information is properly redacted.

## Related Documentation

- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Google Calendar API Reference](https://developers.google.com/calendar/api/guides/overview)
- [OAuth 2.0 Security Best Practices](https://datatracker.ietf.org/doc/html/draft-ietf-oauth-security-topics)
