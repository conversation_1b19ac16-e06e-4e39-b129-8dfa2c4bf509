# Google Calendar Integration

This package uses the `@googleapis/calendar` library to interact with Google Calendar.

## Setup

1. Create a Google Cloud project and enable the Calendar API.
2. Configure OAuth client credentials and obtain a refresh token.
3. Provide `GOOGLE_CLIENT_ID`, `GOOGLE_CLIENT_SECRET`, and `GOOGLE_REFRESH_TOKEN` as environment variables.

Required scopes:

- `https://www.googleapis.com/auth/calendar.events`
- `https://www.googleapis.com/auth/calendar.settings.readonly`
- `https://www.googleapis.com/auth/calendar`

## Usage

Run the demo script to create a test event:

```bash
pnpm ts-node scripts/dev/book_demo.ts
```

## Push Notifications

1. Create a watch channel using `POST /events/watch` and set `X-Goog-Channel-Token` to your secret.
2. Google sends updates to `/calendar/google/push` with the same token.
3. Renew the watch every 7 days (cron job placeholder).
