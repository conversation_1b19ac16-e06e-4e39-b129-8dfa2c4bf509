# Bring Your Own Number (BYON) Guide

## Overview

The Bring Your Own Number (BYON) feature allows tenants to register their existing phone numbers from any carrier and forward calls to the AI Voice Receptionist. This guide provides step-by-step instructions for setting up external number registration and verification.

## How It Works

1. **Register Your Number**: Submit your existing phone number to get a SIP URI and verification code
2. **Configure Call Forwarding**: Set up your carrier to forward calls to the provided SIP URI
3. **Verify Your Number**: Place a test call and enter the 6-digit verification code
4. **Start Receiving Calls**: Your number is now ready to handle calls through the AI Voice Receptionist

## Prerequisites

- Active subscription with `voice_intake` feature enabled
- Existing phone number from any carrier
- Ability to configure call forwarding with your carrier
- Valid tenant ID and API access

## API Endpoints

### 1. Register External Number

Register your existing phone number to get SIP URI and verification code.

**Endpoint**: `POST /api/v1/external-numbers`

**Headers**:
```
X-Tenant-ID: your-tenant-uuid
Content-Type: application/json
```

**Request Body**:
```json
{
  "did": "+***********"
}
```

**Response** (201 Created):
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "tenant_id": "987fcdeb-51a2-43d1-9f12-************",
  "did": "+***********",
  "status": "pending",
  "verification_code": "123456",
  "sip_uri": "sip:+***********@voice-svc:5060",
  "created_at": "2024-12-19T14:30:00Z",
  "verified_at": null
}
```

**cURL Example**:
```bash
curl -X POST "https://api.ailex.com/api/v1/external-numbers" \
  -H "X-Tenant-ID: 987fcdeb-51a2-43d1-9f12-************" \
  -H "Content-Type: application/json" \
  -d '{"did": "+***********"}'
```

### 2. Verify External Number

Verify your number by entering the 6-digit code during a test call.

**Endpoint**: `POST /api/v1/external-numbers/{did}/verify`

**Request Body**:
```json
{
  "code": "123456"
}
```

**Response** (200 OK):
```json
{
  "status": "verified",
  "verified_at": "2024-12-19T14:35:00Z",
  "message": "Phone number successfully verified and ready for use"
}
```

**cURL Example**:
```bash
curl -X POST "https://api.ailex.com/api/v1/external-numbers/+***********/verify" \
  -H "X-Tenant-ID: 987fcdeb-51a2-43d1-9f12-************" \
  -H "Content-Type: application/json" \
  -d '{"code": "123456"}'
```

### 3. List External Numbers

Get all registered external numbers for your tenant.

**Endpoint**: `GET /api/v1/external-numbers`

**Response** (200 OK):
```json
{
  "numbers": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "did": "+***********",
      "status": "verified",
      "created_at": "2024-12-19T14:30:00Z",
      "verified_at": "2024-12-19T14:35:00Z"
    }
  ],
  "total": 1
}
```

### 4. Get SIP URI Information

Retrieve SIP URI and verification code for an existing external number.

**Endpoint**: `GET /api/v1/external-numbers/{did}/sip-uri`

**Response** (200 OK):
```json
{
  "sip_uri": "sip:+***********@voice-svc:5060",
  "verification_code": "123456",
  "instructions": "1. Configure your phone carrier to forward calls to the SIP URI above\n2. Place a test call to your number\n3. When prompted, enter the 6-digit verification code\n4. Your number will be verified and ready for use"
}
```

## Carrier-Specific Setup Instructions

### Verizon
1. Log into your Verizon account
2. Go to "My Plan" → "Manage Products & Apps"
3. Select "Call Forwarding"
4. Enter the SIP URI: `sip:+***********@voice-svc:5060`
5. Save changes

### AT&T
1. Dial `*72` from your phone
2. When prompted, enter: `+***********@voice-svc:5060`
3. Wait for confirmation tone
4. Hang up

### T-Mobile
1. Access T-Mobile account online
2. Go to "Phone" → "Call Forwarding"
3. Enable "Forward all calls"
4. Enter SIP URI: `sip:+***********@voice-svc:5060`
5. Save settings

### Sprint
1. Dial `*72` followed by the SIP URI
2. Press call and wait for confirmation
3. Hang up when you hear the confirmation tone

### Generic SIP Provider
Most SIP providers support call forwarding through their web interface:
1. Log into your SIP provider's control panel
2. Find "Call Forwarding" or "Call Routing" settings
3. Add a new rule to forward all calls to: `sip:+***********@voice-svc:5060`
4. Save and activate the rule

## Verification Process

### Step-by-Step Verification

1. **Complete Registration**: Use the API to register your number and receive the SIP URI
2. **Configure Forwarding**: Set up call forwarding with your carrier (see carrier instructions above)
3. **Test Call**: Call your registered number from any phone
4. **Enter Code**: When the AI answers, it will prompt you to enter your 6-digit verification code
5. **Confirmation**: Upon successful verification, you'll hear a confirmation message

### Verification Call Flow

```
[You call your number] → [Carrier forwards to SIP URI] → [AI Voice Receptionist answers]

AI: "Welcome to AI Voice Receptionist. To verify your phone number, 
     please enter your 6-digit verification code followed by the pound key."

[You enter: 1-2-3-4-5-6-#]

AI: "Thank you! Your phone number has been successfully verified. 
     You will now be connected to the AI Voice Receptionist."
```

## Status Meanings

- **pending**: Number registered, awaiting verification call
- **verifying**: Test call received, code entry in progress  
- **verified**: Successfully verified and ready for use
- **failed**: Verification failed, contact support

## Error Handling

### Common Errors

| Error Code | Description | Solution |
|------------|-------------|----------|
| 400 | Invalid phone number format | Use E.164 format: +*********** |
| 409 | Number already registered | Number is already in use by another tenant |
| 404 | Number not found | Register the number first |
| 422 | Validation error | Check request format and required fields |

### Premium Numbers Blocked

The following number types are not allowed:
- 900 numbers (premium rate)
- 976 numbers (premium rate)
- 550 numbers (directory assistance)

### Troubleshooting

**Call forwarding not working?**
- Verify SIP URI is entered correctly
- Check with your carrier that forwarding is active
- Some carriers require 24-48 hours for changes to take effect

**Verification code not accepted?**
- Ensure you're entering exactly 6 digits
- Press the pound key (#) after entering the code
- Code is case-sensitive and expires after 24 hours

**Can't hear the AI prompt?**
- Check your carrier's call forwarding configuration
- Verify the SIP URI includes the correct domain and port
- Contact support if issues persist

## Environment Variables

Configure these environment variables for your deployment:

```bash
# Voice service domain for SIP URI generation
VOICE_SVC_DOMAIN=voice-svc  # Default: voice-svc

# Voice service port for SIP URI generation  
VOICE_SVC_PORT=5060         # Default: 5060
```

## Security Considerations

- Verification codes are single-use and expire after successful verification
- All API endpoints require valid tenant authentication
- SIP URIs are tenant-specific and cannot be used by other tenants
- Premium numbers are automatically blocked to prevent fraud

## Support

For technical support or questions about the Bring Your Own Number feature:

- Check the troubleshooting section above
- Review API error responses for specific guidance
- Contact support with your tenant ID and phone number for assistance

## Rate Limits

- Maximum 10 external number registrations per tenant
- Verification attempts limited to 3 per number per hour
- API rate limits apply per standard tenant limits
