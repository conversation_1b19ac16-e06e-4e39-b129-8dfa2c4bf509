version: "3.8"

services:
  calendar-svc:
    build: ./apps/calendar-svc
    ports:
      - "8000:8000"
    env_file: .env
    environment:
      - DATABASE_URL=**************************************/calendar_db
    volumes:
      - ./apps/calendar-svc:/app
    depends_on:
      - db
      - redis
    restart: unless-stopped
    networks:
      - app-network

  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: calendar_db
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    ports:
      - "5432:5432"
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network
    command: redis-server --appendonly yes

  # Voice Service
  voice-svc:
    build: ./apps/voice-svc
    ports:
      - "8001:8000"  # Use 8001 to avoid conflict with calendar-svc
    env_file: .env
    environment:
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=sqlite+aiosqlite:///./voice_svc.db
      - MAX_ACTIVE_CALLS_PER_TENANT=4
      - MAX_QUEUE_LENGTH_PER_TENANT=10
      - LOG_LEVEL=INFO
    volumes:
      - ./apps/voice-svc:/app
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - app-network

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=7d'
      - '--web.enable-lifecycle'
    networks:
      - app-network

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana:/var/lib/grafana/dashboards:ro
    networks:
      - app-network
    depends_on:
      - prometheus

  # Add other services here as they are developed
  # voice-agent:
  #   build: ./apps/voice-agent
  #   ports:
  #     - "3001:3000"
  #   env_file: .env
  #   volumes:
  #     - ./apps/voice-agent:/app
  #   depends_on:
  #     - db
  #     - redis
  #   restart: unless-stopped
  #   networks:
  #     - app-network

  # dashboard:
  #   build: ./apps/dashboard
  #   ports:
  #     - "3001:3000"
  #   env_file: .env
  #   volumes:
  #     - ./apps/dashboard:/app
  #   depends_on:
  #     - calendar-svc
  #   restart: unless-stopped
  #   networks:
  #     - app-network

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
