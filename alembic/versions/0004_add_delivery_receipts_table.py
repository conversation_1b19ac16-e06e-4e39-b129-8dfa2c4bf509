"""add delivery_receipts table

Revision ID: 0004
Revises: 0003
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0004'
down_revision = '0003'
branch_labels = None
depends_on = None


def upgrade():
    """Add delivery_receipts table for tracking email/SMS delivery status."""
    is_sqlite = op.get_bind().dialect.name == 'sqlite'
    schema = None if is_sqlite else 'tenants'
    
    # Create enum types for PostgreSQL
    if not is_sqlite:
        # Create enum types if they don't exist
        op.execute("CREATE TYPE delivery_channel_enum AS ENUM ('email', 'sms')")
        op.execute("CREATE TYPE delivery_provider_enum AS ENUM ('resend', 'telnyx')")
        op.execute("CREATE TYPE delivery_status_enum AS ENUM ('delivered', 'failed')")
    
    # Create the delivery_receipts table
    op.create_table(
        'delivery_receipts',
        sa.Column('id', postgresql.UUID(as_uuid=True) if not is_sqlite else sa.String(), 
                 primary_key=True, nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True) if not is_sqlite else sa.String(), 
                 nullable=False, index=True),
        sa.Column('message_id', sa.Text(), nullable=False),
        sa.Column('channel', 
                 sa.Enum('email', 'sms', name='delivery_channel_enum') if not is_sqlite 
                 else sa.String(), nullable=False),
        sa.Column('provider', 
                 sa.Enum('resend', 'telnyx', name='delivery_provider_enum') if not is_sqlite 
                 else sa.String(), nullable=False),
        sa.Column('status', 
                 sa.Enum('delivered', 'failed', name='delivery_status_enum') if not is_sqlite 
                 else sa.String(), nullable=False),
        sa.Column('delivered_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('failure_reason', sa.Text(), nullable=True),
        sa.Column('raw_payload', sa.JSON(), nullable=True, default={}),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, 
                 server_default=sa.text('CURRENT_TIMESTAMP')),
        schema=schema
    )
    
    # Create indexes
    op.create_index(
        'idx_delivery_receipts_tenant_status', 
        'delivery_receipts', 
        ['tenant_id', 'status'],
        schema=schema
    )
    op.create_index(
        'idx_delivery_receipts_message_id', 
        'delivery_receipts', 
        ['message_id'],
        schema=schema
    )


def downgrade():
    """Remove delivery_receipts table."""
    is_sqlite = op.get_bind().dialect.name == 'sqlite'
    schema = None if is_sqlite else 'tenants'
    
    # Drop indexes
    op.drop_index('idx_delivery_receipts_message_id', 'delivery_receipts', schema=schema)
    op.drop_index('idx_delivery_receipts_tenant_status', 'delivery_receipts', schema=schema)
    
    # Drop table
    op.drop_table('delivery_receipts', schema=schema)
    
    # Drop enum types for PostgreSQL
    if not is_sqlite:
        op.execute("DROP TYPE IF EXISTS delivery_status_enum")
        op.execute("DROP TYPE IF EXISTS delivery_provider_enum")
        op.execute("DROP TYPE IF EXISTS delivery_channel_enum")
