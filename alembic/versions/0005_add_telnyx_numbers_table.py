"""add telnyx_numbers table

Revision ID: 0005
Revises: 0004
Create Date: 2024-12-19 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0005'
down_revision = '0004'
branch_labels = None
depends_on = None


def upgrade():
    """Add telnyx_numbers table for DID provisioning."""
    is_sqlite = op.get_bind().dialect.name == 'sqlite'
    schema = None if is_sqlite else 'tenants'
    
    # Create telnyx_numbers table
    op.create_table(
        'telnyx_numbers',
        sa.Column('id', postgresql.UUID(as_uuid=True) if not is_sqlite else sa.String, primary_key=True, nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True) if not is_sqlite else sa.String, nullable=False, index=True),
        sa.Column('did', sa.String(length=20), nullable=False, unique=True, index=True),
        sa.Column('state', sa.String(length=2), nullable=False, index=True),
        sa.Column('forwarding_number', sa.String(length=20), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=False, default='pending', index=True),
        sa.Column('telnyx_number_id', sa.String(length=100), nullable=True),  # Telnyx's internal ID
        sa.Column('metadata', sa.JSON(), nullable=True),  # Additional Telnyx data
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        schema=schema
    )
    
    # Add indexes for common queries
    op.create_index(
        'idx_telnyx_numbers_tenant_status', 
        'telnyx_numbers', 
        ['tenant_id', 'status'],
        schema=schema
    )
    
    op.create_index(
        'idx_telnyx_numbers_state_status', 
        'telnyx_numbers', 
        ['state', 'status'],
        schema=schema
    )
    
    # Add RLS policies for PostgreSQL (Supabase)
    if not is_sqlite:
        policy_expr = "((auth.jwt() ->> 'tenant_id'))::uuid"
        table_name = f"tenants.telnyx_numbers"
        
        # Enable RLS
        op.execute(f"ALTER TABLE {table_name} ENABLE ROW LEVEL SECURITY")
        
        # Create policies
        op.execute(
            f"CREATE POLICY tenant_select ON {table_name} FOR SELECT USING (tenant_id = {policy_expr})"
        )
        op.execute(
            f"CREATE POLICY tenant_insert ON {table_name} FOR INSERT WITH CHECK (tenant_id = {policy_expr})"
        )
        op.execute(
            f"CREATE POLICY tenant_update ON {table_name} FOR UPDATE USING (tenant_id = {policy_expr})"
        )
        op.execute(
            f"CREATE POLICY tenant_delete ON {table_name} FOR DELETE USING (tenant_id = {policy_expr})"
        )


def downgrade():
    """Remove telnyx_numbers table."""
    is_sqlite = op.get_bind().dialect.name == 'sqlite'
    schema = None if is_sqlite else 'tenants'
    
    # Drop indexes first
    op.drop_index('idx_telnyx_numbers_state_status', 'telnyx_numbers', schema=schema)
    op.drop_index('idx_telnyx_numbers_tenant_status', 'telnyx_numbers', schema=schema)
    
    # Drop table
    op.drop_table('telnyx_numbers', schema=schema)
