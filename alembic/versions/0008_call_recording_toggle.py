"""add call recording toggle and voice settings

Revision ID: 0008
Revises: 0007
Create Date: 2024-12-19 16:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0008'
down_revision = '0007'
branch_labels = None
depends_on = None


def upgrade():
    """Add voice_settings table and recording fields to calls table."""
    is_sqlite = op.get_bind().dialect.name == 'sqlite'
    schema = None if is_sqlite else 'tenants'
    
    # Create voice_settings table
    op.create_table(
        'voice_settings',
        sa.Column('id', sa.String() if is_sqlite else postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('tenant_id', sa.String() if is_sqlite else postgresql.UUID(as_uuid=True), nullable=False, unique=True),
        sa.Column('record_calls', sa.<PERSON>(), nullable=False, server_default='true'),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        schema=schema
    )
    
    # Add indexes for voice_settings
    op.create_index(
        'idx_voice_settings_tenant_id',
        'voice_settings',
        ['tenant_id'],
        unique=True,
        schema=schema
    )
    
    # Add recording fields to calls table
    with op.batch_alter_table('calls', schema=schema) as batch_op:
        batch_op.add_column(
            sa.Column('recording_url', sa.Text(), nullable=True)
        )
        batch_op.add_column(
            sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True)
        )
    
    # Add indexes for calls recording fields
    op.create_index(
        'idx_calls_recording_url',
        'calls',
        ['recording_url'],
        schema=schema
    )
    
    op.create_index(
        'idx_calls_expires_at',
        'calls',
        ['expires_at'],
        schema=schema
    )
    
    # Add RLS policies for PostgreSQL (Supabase)
    if not is_sqlite:
        policy_expr = "((auth.jwt() ->> 'tenant_id'))::uuid"
        table_name = f"tenants.voice_settings"
        
        # Enable RLS
        op.execute(f"ALTER TABLE {table_name} ENABLE ROW LEVEL SECURITY")
        
        # Create policies for voice_settings
        op.execute(
            f"CREATE POLICY tenant_select ON {table_name} FOR SELECT USING (tenant_id = {policy_expr})"
        )
        op.execute(
            f"CREATE POLICY tenant_insert ON {table_name} FOR INSERT WITH CHECK (tenant_id = {policy_expr})"
        )
        op.execute(
            f"CREATE POLICY tenant_update ON {table_name} FOR UPDATE USING (tenant_id = {policy_expr})"
        )
        op.execute(
            f"CREATE POLICY tenant_delete ON {table_name} FOR DELETE USING (tenant_id = {policy_expr})"
        )


def downgrade():
    """Remove voice_settings table and recording fields from calls table."""
    is_sqlite = op.get_bind().dialect.name == 'sqlite'
    schema = None if is_sqlite else 'tenants'
    
    # Drop indexes for calls recording fields
    op.drop_index('idx_calls_expires_at', 'calls', schema=schema)
    op.drop_index('idx_calls_recording_url', 'calls', schema=schema)
    
    # Remove recording fields from calls table
    with op.batch_alter_table('calls', schema=schema) as batch_op:
        batch_op.drop_column('expires_at')
        batch_op.drop_column('recording_url')
    
    # Drop voice_settings table indexes
    op.drop_index('idx_voice_settings_tenant_id', 'voice_settings', schema=schema)
    
    # Drop voice_settings table
    op.drop_table('voice_settings', schema=schema)
