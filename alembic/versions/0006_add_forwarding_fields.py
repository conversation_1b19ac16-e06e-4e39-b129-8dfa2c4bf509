"""add forwarding fields to telnyx_numbers

Revision ID: 0006
Revises: 0005
Create Date: 2024-12-19 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0006'
down_revision = '0005'
branch_labels = None
depends_on = None


def upgrade():
    """Add forwarding and connection status fields to telnyx_numbers table."""
    is_sqlite = op.get_bind().dialect.name == 'sqlite'
    schema = None if is_sqlite else 'tenants'
    
    # Add connection_status enum type for PostgreSQL
    if not is_sqlite:
        # Create enum type
        connection_status_enum = postgresql.ENUM(
            'provisioned', 'forwarding', 'sip',
            name='telnyx_connection_status_enum',
            create_type=True
        )
        connection_status_enum.create(op.get_bind())
    
    # Add new columns to telnyx_numbers table
    with op.batch_alter_table('telnyx_numbers', schema=schema) as batch_op:
        # Add connection_status column
        if is_sqlite:
            batch_op.add_column(
                sa.Column('connection_status', sa.String(20), nullable=False, server_default='sip')
            )
        else:
            batch_op.add_column(
                sa.Column(
                    'connection_status',
                    sa.Enum('provisioned', 'forwarding', 'sip', name='telnyx_connection_status_enum'),
                    nullable=False,
                    server_default='sip'
                )
            )
        
        # Add verification_required column
        batch_op.add_column(
            sa.Column('verification_required', sa.Boolean(), nullable=False, server_default='false')
        )
        
        # Add telnyx_connection_id column for tracking Telnyx connection IDs
        batch_op.add_column(
            sa.Column('telnyx_connection_id', sa.String(100), nullable=True)
        )
    
    # Add indexes for new columns
    op.create_index(
        'idx_telnyx_numbers_connection_status',
        'telnyx_numbers',
        ['connection_status'],
        schema=schema
    )
    
    op.create_index(
        'idx_telnyx_numbers_verification_required',
        'telnyx_numbers',
        ['verification_required'],
        schema=schema
    )


def downgrade():
    """Remove forwarding fields from telnyx_numbers table."""
    is_sqlite = op.get_bind().dialect.name == 'sqlite'
    schema = None if is_sqlite else 'tenants'
    
    # Drop indexes first
    op.drop_index('idx_telnyx_numbers_verification_required', 'telnyx_numbers', schema=schema)
    op.drop_index('idx_telnyx_numbers_connection_status', 'telnyx_numbers', schema=schema)
    
    # Remove columns
    with op.batch_alter_table('telnyx_numbers', schema=schema) as batch_op:
        batch_op.drop_column('telnyx_connection_id')
        batch_op.drop_column('verification_required')
        batch_op.drop_column('connection_status')
    
    # Drop enum type for PostgreSQL
    if not is_sqlite:
        op.execute('DROP TYPE IF EXISTS telnyx_connection_status_enum')
