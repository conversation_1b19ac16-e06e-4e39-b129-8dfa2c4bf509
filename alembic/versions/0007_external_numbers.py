"""add external_numbers table for bring-your-own-number

Revision ID: 0007
Revises: 0006
Create Date: 2024-12-19 14:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0007'
down_revision = '0006'
branch_labels = None
depends_on = None


def upgrade():
    """Add external_numbers table for bring-your-own-number feature."""
    is_sqlite = op.get_bind().dialect.name == 'sqlite'
    schema = None if is_sqlite else 'tenants'
    
    # Add external_number_status enum type for PostgreSQL
    if not is_sqlite:
        # Create enum type
        external_status_enum = postgresql.ENUM(
            'pending', 'verifying', 'verified', 'failed',
            name='external_number_status_enum',
            create_type=True
        )
        external_status_enum.create(op.get_bind())
    
    # Create external_numbers table
    op.create_table(
        'external_numbers',
        sa.Column('id', sa.String() if is_sqlite else postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('tenant_id', sa.String() if is_sqlite else postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('did', sa.String(20), nullable=False),
        sa.Column(
            'status',
            sa.String(20) if is_sqlite else sa.Enum('pending', 'verifying', 'verified', 'failed', name='external_number_status_enum'),
            nullable=False,
            server_default='pending'
        ),
        sa.Column('verification_code', sa.CHAR(6), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('verified_at', sa.DateTime(timezone=True), nullable=True),
        schema=schema
    )
    
    # Add indexes
    op.create_index(
        'idx_external_numbers_tenant_id',
        'external_numbers',
        ['tenant_id'],
        schema=schema
    )
    
    op.create_index(
        'idx_external_numbers_did',
        'external_numbers',
        ['did'],
        unique=True,
        schema=schema
    )
    
    op.create_index(
        'idx_external_numbers_tenant_status',
        'external_numbers',
        ['tenant_id', 'status'],
        schema=schema
    )
    
    op.create_index(
        'idx_external_numbers_status',
        'external_numbers',
        ['status'],
        schema=schema
    )


def downgrade():
    """Remove external_numbers table."""
    is_sqlite = op.get_bind().dialect.name == 'sqlite'
    schema = None if is_sqlite else 'tenants'
    
    # Drop indexes first
    op.drop_index('idx_external_numbers_status', 'external_numbers', schema=schema)
    op.drop_index('idx_external_numbers_tenant_status', 'external_numbers', schema=schema)
    op.drop_index('idx_external_numbers_did', 'external_numbers', schema=schema)
    op.drop_index('idx_external_numbers_tenant_id', 'external_numbers', schema=schema)
    
    # Drop table
    op.drop_table('external_numbers', schema=schema)
    
    # Drop enum type for PostgreSQL
    if not is_sqlite:
        op.execute('DROP TYPE IF EXISTS external_number_status_enum')
